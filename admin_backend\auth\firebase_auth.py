"""
APEX Firebase Authentication System
نظام مصادقة Firebase متقدم مع إدارة الصلاحيات
"""

import asyncio
import json
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

import structlog
import firebase_admin
from firebase_admin import credentials, auth as firebase_auth, firestore
from fastapi import HTTPException, Depends, Request
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

from ..config import settings, admin_permissions
from ..database import database, admins_table, admin_sessions_table, audit_log_table

logger = structlog.get_logger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


class FirebaseAuth:
    """نظام مصادقة Firebase المتقدم"""
    
    def __init__(self):
        self.app = None
        self.db = None
        self.initialized = False
    
    async def initialize(self):
        """تهيئة Firebase"""
        try:
            if not self.initialized:
                # Initialize Firebase Admin SDK
                cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS_PATH)
                self.app = firebase_admin.initialize_app(cred, {
                    'projectId': settings.FIREBASE_PROJECT_ID,
                    'storageBucket': settings.FIREBASE_STORAGE_BUCKET
                })
                
                # Initialize Firestore
                self.db = firestore.client()
                
                self.initialized = True
                logger.info("✅ تم تهيئة Firebase بنجاح")
                
                # Create super admin if not exists
                await self.create_super_admin()
                
        except Exception as e:
            logger.error("❌ خطأ في تهيئة Firebase", error=str(e))
            raise
    
    async def create_super_admin(self):
        """إنشاء الأدمن الرئيسي"""
        try:
            # Check if super admin exists
            query = admins_table.select().where(
                admins_table.c.email == settings.SUPER_ADMIN_EMAIL
            )
            existing_admin = await database.fetch_one(query)
            
            if not existing_admin:
                # Create Firebase user
                try:
                    user_record = firebase_auth.create_user(
                        email=settings.SUPER_ADMIN_EMAIL,
                        password=settings.SUPER_ADMIN_PASSWORD,
                        display_name="Super Admin"
                    )
                    
                    # Insert into database
                    insert_query = admins_table.insert().values(
                        firebase_uid=user_record.uid,
                        email=settings.SUPER_ADMIN_EMAIL,
                        display_name="Super Admin",
                        role="super_admin",
                        permissions=admin_permissions.SUPER_ADMIN,
                        is_active=True,
                        created_at=datetime.utcnow()
                    )
                    
                    await database.execute(insert_query)
                    logger.info("✅ تم إنشاء الأدمن الرئيسي")
                    
                except firebase_auth.EmailAlreadyExistsError:
                    # User exists in Firebase but not in our database
                    user_record = firebase_auth.get_user_by_email(settings.SUPER_ADMIN_EMAIL)
                    
                    insert_query = admins_table.insert().values(
                        firebase_uid=user_record.uid,
                        email=settings.SUPER_ADMIN_EMAIL,
                        display_name="Super Admin",
                        role="super_admin",
                        permissions=admin_permissions.SUPER_ADMIN,
                        is_active=True,
                        created_at=datetime.utcnow()
                    )
                    
                    await database.execute(insert_query)
                    logger.info("✅ تم ربط الأدمن الرئيسي الموجود")
                    
        except Exception as e:
            logger.error("❌ خطأ في إنشاء الأدمن الرئيسي", error=str(e))
            # Don't raise here, let the system continue
    
    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """التحقق من صحة الرمز المميز"""
        try:
            # Verify Firebase token
            decoded_token = firebase_auth.verify_id_token(token)
            
            # Get admin from database
            query = admins_table.select().where(
                admins_table.c.firebase_uid == decoded_token['uid']
            )
            admin = await database.fetch_one(query)
            
            if not admin:
                logger.warning("مستخدم غير موجود في قاعدة البيانات", uid=decoded_token['uid'])
                return None
            
            if not admin['is_active']:
                logger.warning("حساب المدير معطل", email=admin['email'])
                return None
            
            # Update last login
            update_query = admins_table.update().where(
                admins_table.c.id == admin['id']
            ).values(last_login=datetime.utcnow())
            
            await database.execute(update_query)
            
            return {
                'admin_id': str(admin['id']),
                'firebase_uid': admin['firebase_uid'],
                'email': admin['email'],
                'display_name': admin['display_name'],
                'role': admin['role'],
                'permissions': admin['permissions'],
                'firebase_claims': decoded_token
            }
            
        except firebase_auth.InvalidIdTokenError:
            logger.warning("رمز مميز غير صحيح")
            return None
        except firebase_auth.ExpiredIdTokenError:
            logger.warning("رمز مميز منتهي الصلاحية")
            return None
        except Exception as e:
            logger.error("خطأ في التحقق من الرمز المميز", error=str(e))
            return None
    
    async def create_admin(
        self, 
        email: str, 
        password: str, 
        display_name: str,
        role: str = "regular_admin",
        created_by_id: str = None
    ) -> Dict[str, Any]:
        """إنشاء مدير جديد"""
        try:
            # Validate role
            if role not in ["super_admin", "regular_admin"]:
                raise ValueError("دور غير صحيح")
            
            # Create Firebase user
            user_record = firebase_auth.create_user(
                email=email,
                password=password,
                display_name=display_name
            )
            
            # Set permissions based on role
            permissions = (
                admin_permissions.SUPER_ADMIN 
                if role == "super_admin" 
                else admin_permissions.REGULAR_ADMIN
            )
            
            # Insert into database
            insert_query = admins_table.insert().values(
                firebase_uid=user_record.uid,
                email=email,
                display_name=display_name,
                role=role,
                permissions=permissions,
                is_active=True,
                created_by=created_by_id,
                created_at=datetime.utcnow()
            )
            
            admin_id = await database.execute(insert_query)
            
            # Log the action
            await self.log_admin_action(
                admin_id=created_by_id,
                action="admin.create",
                resource_type="admin",
                resource_id=str(admin_id),
                new_values={
                    "email": email,
                    "role": role,
                    "display_name": display_name
                }
            )
            
            logger.info("✅ تم إنشاء مدير جديد", email=email, role=role)
            
            return {
                "id": str(admin_id),
                "firebase_uid": user_record.uid,
                "email": email,
                "display_name": display_name,
                "role": role,
                "permissions": permissions
            }
            
        except firebase_auth.EmailAlreadyExistsError:
            raise HTTPException(status_code=400, detail="البريد الإلكتروني مستخدم بالفعل")
        except Exception as e:
            logger.error("❌ خطأ في إنشاء المدير", error=str(e))
            raise HTTPException(status_code=500, detail="خطأ في إنشاء المدير")
    
    async def update_admin_permissions(
        self, 
        admin_id: str, 
        permissions: List[str],
        updated_by_id: str
    ):
        """تحديث صلاحيات المدير"""
        try:
            # Get current admin data
            query = admins_table.select().where(admins_table.c.id == admin_id)
            admin = await database.fetch_one(query)
            
            if not admin:
                raise HTTPException(status_code=404, detail="المدير غير موجود")
            
            old_permissions = admin['permissions']
            
            # Update permissions
            update_query = admins_table.update().where(
                admins_table.c.id == admin_id
            ).values(
                permissions=permissions,
                updated_at=datetime.utcnow()
            )
            
            await database.execute(update_query)
            
            # Log the action
            await self.log_admin_action(
                admin_id=updated_by_id,
                action="admin.update_permissions",
                resource_type="admin",
                resource_id=admin_id,
                old_values={"permissions": old_permissions},
                new_values={"permissions": permissions}
            )
            
            logger.info("✅ تم تحديث صلاحيات المدير", admin_id=admin_id)
            
        except Exception as e:
            logger.error("❌ خطأ في تحديث الصلاحيات", error=str(e))
            raise HTTPException(status_code=500, detail="خطأ في تحديث الصلاحيات")
    
    async def deactivate_admin(self, admin_id: str, deactivated_by_id: str):
        """إلغاء تفعيل المدير"""
        try:
            # Get admin data
            query = admins_table.select().where(admins_table.c.id == admin_id)
            admin = await database.fetch_one(query)
            
            if not admin:
                raise HTTPException(status_code=404, detail="المدير غير موجود")
            
            # Disable Firebase user
            firebase_auth.update_user(admin['firebase_uid'], disabled=True)
            
            # Update database
            update_query = admins_table.update().where(
                admins_table.c.id == admin_id
            ).values(
                is_active=False,
                updated_at=datetime.utcnow()
            )
            
            await database.execute(update_query)
            
            # Invalidate all sessions
            await self.invalidate_admin_sessions(admin_id)
            
            # Log the action
            await self.log_admin_action(
                admin_id=deactivated_by_id,
                action="admin.deactivate",
                resource_type="admin",
                resource_id=admin_id,
                old_values={"is_active": True},
                new_values={"is_active": False}
            )
            
            logger.info("✅ تم إلغاء تفعيل المدير", admin_id=admin_id)
            
        except Exception as e:
            logger.error("❌ خطأ في إلغاء تفعيل المدير", error=str(e))
            raise HTTPException(status_code=500, detail="خطأ في إلغاء تفعيل المدير")
    
    async def invalidate_admin_sessions(self, admin_id: str):
        """إلغاء جميع جلسات المدير"""
        try:
            update_query = admin_sessions_table.update().where(
                admin_sessions_table.c.admin_id == admin_id
            ).values(is_active=False)
            
            await database.execute(update_query)
            
        except Exception as e:
            logger.error("❌ خطأ في إلغاء الجلسات", error=str(e))
    
    async def log_admin_action(
        self,
        admin_id: Optional[str],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        old_values: Optional[Dict] = None,
        new_values: Optional[Dict] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """تسجيل عمليات المديرين"""
        try:
            insert_query = audit_log_table.insert().values(
                admin_id=admin_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                old_values=old_values,
                new_values=new_values,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message,
                created_at=datetime.utcnow()
            )
            
            await database.execute(insert_query)
            
        except Exception as e:
            logger.error("❌ خطأ في تسجيل العملية", error=str(e))


# Global Firebase Auth instance
firebase_auth_manager = FirebaseAuth()


# Dependency for getting current admin
async def get_current_admin(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Dict[str, Any]:
    """الحصول على المدير الحالي"""
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="مطلوب رمز مصادقة",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    admin_data = await firebase_auth_manager.verify_token(credentials.credentials)
    
    if not admin_data:
        raise HTTPException(
            status_code=401,
            detail="رمز مصادقة غير صحيح",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    # Log the request
    await firebase_auth_manager.log_admin_action(
        admin_id=admin_data['admin_id'],
        action="auth.access",
        resource_type="endpoint",
        resource_id=request.url.path,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )
    
    return admin_data


# Dependency for checking permissions
def require_permission(permission: str):
    """التحقق من الصلاحية المطلوبة"""
    async def permission_checker(
        current_admin: Dict[str, Any] = Depends(get_current_admin)
    ):
        if permission not in current_admin['permissions']:
            raise HTTPException(
                status_code=403,
                detail=f"ليس لديك صلاحية: {permission}"
            )
        return current_admin
    
    return permission_checker


# Dependency for super admin only
async def require_super_admin(
    current_admin: Dict[str, Any] = Depends(get_current_admin)
):
    """التحقق من كون المستخدم أدمن رئيسي"""
    if current_admin['role'] != 'super_admin':
        raise HTTPException(
            status_code=403,
            detail="هذه العملية مخصصة للأدمن الرئيسي فقط"
        )
    return current_admin
