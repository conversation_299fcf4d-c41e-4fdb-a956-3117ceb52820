"""
APEX Permission Management System
نظام إدارة الصلاحيات المتقدم مع تحكم دقيق
"""

from typing import Dict, List, Set, Optional, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

import structlog
from fastapi import HTTPException

logger = structlog.get_logger(__name__)


class PermissionLevel(Enum):
    """مستويات الصلاحيات"""
    READ = "read"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    ADMIN = "admin"


class ResourceType(Enum):
    """أنواع الموارد"""
    ADMIN = "admin"
    CATEGORY = "categories"
    BOOK = "books"
    FILE = "files"
    SYSTEM = "system"
    AUDIT = "audit"


@dataclass
class Permission:
    """صلاحية واحدة"""
    resource: ResourceType
    level: PermissionLevel
    description: str
    
    def __str__(self) -> str:
        return f"{self.resource.value}.{self.level.value}"
    
    @classmethod
    def from_string(cls, permission_str: str) -> 'Permission':
        """إنشاء صلاحية من نص"""
        try:
            resource_str, level_str = permission_str.split('.')
            resource = ResourceType(resource_str)
            level = PermissionLevel(level_str)
            return cls(resource, level, f"{resource_str} {level_str}")
        except ValueError:
            raise ValueError(f"صيغة صلاحية غير صحيحة: {permission_str}")


class PermissionManager:
    """مدير الصلاحيات المتقدم"""
    
    def __init__(self):
        self.permissions_registry: Dict[str, Permission] = {}
        self.role_permissions: Dict[str, Set[str]] = {}
        self.permission_hierarchy: Dict[str, List[str]] = {}
        self.initialized = False
    
    async def initialize(self):
        """تهيئة نظام الصلاحيات"""
        if self.initialized:
            return
        
        try:
            # Register all permissions
            await self._register_permissions()
            
            # Setup role permissions
            await self._setup_role_permissions()
            
            # Setup permission hierarchy
            await self._setup_permission_hierarchy()
            
            self.initialized = True
            logger.info("✅ تم تهيئة نظام الصلاحيات")
            
        except Exception as e:
            logger.error("❌ خطأ في تهيئة نظام الصلاحيات", error=str(e))
            raise
    
    async def _register_permissions(self):
        """تسجيل جميع الصلاحيات"""
        permissions = [
            # Admin permissions
            Permission(ResourceType.ADMIN, PermissionLevel.READ, "عرض المديرين"),
            Permission(ResourceType.ADMIN, PermissionLevel.CREATE, "إنشاء مديرين"),
            Permission(ResourceType.ADMIN, PermissionLevel.UPDATE, "تعديل المديرين"),
            Permission(ResourceType.ADMIN, PermissionLevel.DELETE, "حذف المديرين"),
            Permission(ResourceType.ADMIN, PermissionLevel.ADMIN, "إدارة كاملة للمديرين"),
            
            # Category permissions
            Permission(ResourceType.CATEGORY, PermissionLevel.READ, "عرض الأقسام"),
            Permission(ResourceType.CATEGORY, PermissionLevel.CREATE, "إنشاء أقسام"),
            Permission(ResourceType.CATEGORY, PermissionLevel.UPDATE, "تعديل الأقسام"),
            Permission(ResourceType.CATEGORY, PermissionLevel.DELETE, "حذف الأقسام"),
            
            # Book permissions
            Permission(ResourceType.BOOK, PermissionLevel.READ, "عرض الكتب"),
            Permission(ResourceType.BOOK, PermissionLevel.CREATE, "إنشاء كتب"),
            Permission(ResourceType.BOOK, PermissionLevel.UPDATE, "تعديل الكتب"),
            Permission(ResourceType.BOOK, PermissionLevel.DELETE, "حذف الكتب"),
            
            # File permissions
            Permission(ResourceType.FILE, PermissionLevel.CREATE, "رفع ملفات"),
            Permission(ResourceType.FILE, PermissionLevel.DELETE, "حذف ملفات"),
            Permission(ResourceType.FILE, PermissionLevel.ADMIN, "إدارة كاملة للملفات"),
            
            # System permissions
            Permission(ResourceType.SYSTEM, PermissionLevel.READ, "عرض إعدادات النظام"),
            Permission(ResourceType.SYSTEM, PermissionLevel.UPDATE, "تعديل إعدادات النظام"),
            Permission(ResourceType.SYSTEM, PermissionLevel.ADMIN, "إدارة كاملة للنظام"),
            
            # Audit permissions
            Permission(ResourceType.AUDIT, PermissionLevel.READ, "عرض سجل العمليات"),
            Permission(ResourceType.AUDIT, PermissionLevel.ADMIN, "إدارة سجل العمليات"),
        ]
        
        for permission in permissions:
            self.permissions_registry[str(permission)] = permission
    
    async def _setup_role_permissions(self):
        """إعداد صلاحيات الأدوار"""
        # Super Admin - جميع الصلاحيات
        self.role_permissions["super_admin"] = set([
            "admin.read", "admin.create", "admin.update", "admin.delete", "admin.admin",
            "categories.read", "categories.create", "categories.update", "categories.delete",
            "books.read", "books.create", "books.update", "books.delete",
            "files.create", "files.delete", "files.admin",
            "system.read", "system.update", "system.admin",
            "audit.read", "audit.admin"
        ])
        
        # Regular Admin - صلاحيات محدودة
        self.role_permissions["regular_admin"] = set([
            "categories.read", "categories.create", "categories.update",
            "books.read", "books.create", "books.update", "books.delete",
            "files.create"
        ])
        
        # Read Only - قراءة فقط
        self.role_permissions["read_only"] = set([
            "categories.read",
            "books.read"
        ])
    
    async def _setup_permission_hierarchy(self):
        """إعداد تسلسل الصلاحيات"""
        # Admin level permissions include all lower levels
        self.permission_hierarchy = {
            "admin.admin": ["admin.read", "admin.create", "admin.update", "admin.delete"],
            "files.admin": ["files.create", "files.delete"],
            "system.admin": ["system.read", "system.update"],
            "audit.admin": ["audit.read"]
        }
    
    def has_permission(self, user_permissions: List[str], required_permission: str) -> bool:
        """التحقق من وجود صلاحية"""
        if not self.initialized:
            logger.warning("نظام الصلاحيات غير مهيأ")
            return False
        
        # Direct permission check
        if required_permission in user_permissions:
            return True
        
        # Check hierarchy - if user has higher level permission
        for high_perm, low_perms in self.permission_hierarchy.items():
            if high_perm in user_permissions and required_permission in low_perms:
                return True
        
        return False
    
    def get_role_permissions(self, role: str) -> Set[str]:
        """الحصول على صلاحيات الدور"""
        return self.role_permissions.get(role, set())
    
    def validate_permissions(self, permissions: List[str]) -> List[str]:
        """التحقق من صحة الصلاحيات"""
        valid_permissions = []
        invalid_permissions = []
        
        for permission in permissions:
            if permission in self.permissions_registry:
                valid_permissions.append(permission)
            else:
                invalid_permissions.append(permission)
        
        if invalid_permissions:
            logger.warning("صلاحيات غير صحيحة", invalid=invalid_permissions)
        
        return valid_permissions
    
    def get_permission_description(self, permission: str) -> Optional[str]:
        """الحصول على وصف الصلاحية"""
        perm_obj = self.permissions_registry.get(permission)
        return perm_obj.description if perm_obj else None
    
    def get_all_permissions(self) -> Dict[str, str]:
        """الحصول على جميع الصلاحيات مع أوصافها"""
        return {
            perm_str: perm_obj.description 
            for perm_str, perm_obj in self.permissions_registry.items()
        }
    
    def get_permissions_by_resource(self, resource: ResourceType) -> Dict[str, str]:
        """الحصول على صلاحيات مورد معين"""
        return {
            perm_str: perm_obj.description
            for perm_str, perm_obj in self.permissions_registry.items()
            if perm_obj.resource == resource
        }
    
    def can_manage_admin(self, current_admin_role: str, target_admin_role: str) -> bool:
        """التحقق من إمكانية إدارة مدير آخر"""
        # Super admin can manage everyone
        if current_admin_role == "super_admin":
            return True
        
        # Regular admin cannot manage anyone
        if current_admin_role == "regular_admin":
            return False
        
        return False
    
    def get_effective_permissions(self, user_permissions: List[str]) -> Set[str]:
        """الحصول على الصلاحيات الفعلية (مع التسلسل)"""
        effective_permissions = set(user_permissions)
        
        # Add inherited permissions from hierarchy
        for high_perm, low_perms in self.permission_hierarchy.items():
            if high_perm in user_permissions:
                effective_permissions.update(low_perms)
        
        return effective_permissions
    
    async def check_resource_access(
        self, 
        user_permissions: List[str], 
        resource_type: str, 
        action: str,
        resource_id: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """فحص متقدم للوصول للموارد"""
        required_permission = f"{resource_type}.{action}"
        
        # Basic permission check
        if not self.has_permission(user_permissions, required_permission):
            return False
        
        # Additional context-based checks
        if additional_context:
            # Example: Check if user can only manage their own resources
            if additional_context.get("owner_only") and resource_id:
                user_id = additional_context.get("user_id")
                resource_owner = additional_context.get("resource_owner")
                
                if user_id != resource_owner:
                    return False
        
        return True
    
    def audit_permission_check(
        self, 
        user_id: str, 
        permission: str, 
        granted: bool,
        context: Optional[Dict[str, Any]] = None
    ):
        """تسجيل فحص الصلاحيات للمراجعة"""
        logger.info(
            "permission_check",
            user_id=user_id,
            permission=permission,
            granted=granted,
            context=context,
            timestamp=datetime.utcnow().isoformat()
        )


# Global permission manager instance
permission_manager = PermissionManager()


# Utility functions for common permission checks
async def check_admin_permission(user_permissions: List[str], action: str) -> bool:
    """فحص صلاحيات الإدارة"""
    return permission_manager.has_permission(user_permissions, f"admin.{action}")


async def check_category_permission(user_permissions: List[str], action: str) -> bool:
    """فحص صلاحيات الأقسام"""
    return permission_manager.has_permission(user_permissions, f"categories.{action}")


async def check_book_permission(user_permissions: List[str], action: str) -> bool:
    """فحص صلاحيات الكتب"""
    return permission_manager.has_permission(user_permissions, f"books.{action}")


async def check_file_permission(user_permissions: List[str], action: str) -> bool:
    """فحص صلاحيات الملفات"""
    return permission_manager.has_permission(user_permissions, f"files.{action}")


async def check_system_permission(user_permissions: List[str], action: str) -> bool:
    """فحص صلاحيات النظام"""
    return permission_manager.has_permission(user_permissions, f"system.{action}")


# Decorator for permission checking
def require_permissions(*required_permissions: str):
    """ديكوريتر للتحقق من الصلاحيات المطلوبة"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get current admin from kwargs (injected by FastAPI)
            current_admin = kwargs.get('current_admin')
            if not current_admin:
                raise HTTPException(status_code=401, detail="مطلوب مصادقة")
            
            user_permissions = current_admin.get('permissions', [])
            
            # Check all required permissions
            for permission in required_permissions:
                if not permission_manager.has_permission(user_permissions, permission):
                    raise HTTPException(
                        status_code=403, 
                        detail=f"ليس لديك صلاحية: {permission}"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
