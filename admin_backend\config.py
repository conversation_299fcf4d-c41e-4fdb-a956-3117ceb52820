"""
APEX Admin System Configuration
إعدادات النظام المتقدمة مع أمان عالي
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """إعدادات النظام الأساسية"""
    
    # Application Settings
    APP_NAME: str = "APEX Admin System"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server Settings
    HOST: str = Field(default="127.0.0.1", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    WORKERS: int = Field(default=4, env="WORKERS")
    
    # Security Settings
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS Settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )
    
    # Database Settings
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # Firebase Settings
    FIREBASE_CREDENTIALS_PATH: str = Field(..., env="FIREBASE_CREDENTIALS_PATH")
    FIREBASE_PROJECT_ID: str = Field(..., env="FIREBASE_PROJECT_ID")
    FIREBASE_STORAGE_BUCKET: str = Field(..., env="FIREBASE_STORAGE_BUCKET")
    
    # Redis Settings (for caching and rate limiting)
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    
    # File Upload Settings
    MAX_FILE_SIZE: int = Field(default=50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["pdf", "jpg", "jpeg", "png", "webp"],
        env="ALLOWED_FILE_TYPES"
    )
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    
    # Rate Limiting Settings
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Admin Settings
    SUPER_ADMIN_EMAIL: str = Field(..., env="SUPER_ADMIN_EMAIL")
    SUPER_ADMIN_PASSWORD: str = Field(..., env="SUPER_ADMIN_PASSWORD")
    
    # Monitoring Settings
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Performance Settings
    CACHE_TTL: int = Field(default=300, env="CACHE_TTL")  # 5 minutes
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("ALLOWED_FILE_TYPES", pre=True)
    def parse_file_types(cls, v):
        if isinstance(v, str):
            return [file_type.strip().lower() for file_type in v.split(",")]
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


class AdminPermissions:
    """صلاحيات الإدارة"""
    
    # Super Admin Permissions (كل الصلاحيات)
    SUPER_ADMIN = [
        "admin.create",
        "admin.read",
        "admin.update", 
        "admin.delete",
        "categories.create",
        "categories.read",
        "categories.update",
        "categories.delete",
        "books.create",
        "books.read",
        "books.update",
        "books.delete",
        "files.upload",
        "files.delete",
        "system.monitor",
        "system.configure"
    ]
    
    # Regular Admin Permissions (صلاحيات محدودة)
    REGULAR_ADMIN = [
        "categories.create",
        "categories.read",
        "categories.update",
        "books.create",
        "books.read",
        "books.update",
        "books.delete",
        "files.upload"
    ]
    
    # Read Only Permissions
    READ_ONLY = [
        "categories.read",
        "books.read"
    ]


class SecurityConfig:
    """إعدادات الأمان المتقدمة"""
    
    # Password Requirements
    MIN_PASSWORD_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_NUMBERS = True
    REQUIRE_SPECIAL_CHARS = True
    
    # Session Security
    SECURE_COOKIES = True
    HTTPONLY_COOKIES = True
    SAMESITE_COOKIES = "strict"
    
    # Request Security
    MAX_REQUEST_SIZE = 100 * 1024 * 1024  # 100MB
    MAX_HEADER_SIZE = 8192  # 8KB
    
    # IP Security
    BLOCKED_IPS = []
    ALLOWED_IPS = []  # Empty means all IPs allowed
    
    # Content Security
    ALLOWED_CONTENT_TYPES = [
        "application/json",
        "multipart/form-data",
        "application/x-www-form-urlencoded"
    ]


# Create settings instance
settings = Settings()

# Validate critical settings
if not settings.SECRET_KEY:
    raise ValueError("SECRET_KEY must be set")

if not settings.DATABASE_URL:
    raise ValueError("DATABASE_URL must be set")

if not settings.FIREBASE_CREDENTIALS_PATH:
    raise ValueError("FIREBASE_CREDENTIALS_PATH must be set")

if not settings.SUPER_ADMIN_EMAIL:
    raise ValueError("SUPER_ADMIN_EMAIL must be set")

# Create upload directory if it doesn't exist
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

# Export commonly used configs
admin_permissions = AdminPermissions()
security_config = SecurityConfig()
