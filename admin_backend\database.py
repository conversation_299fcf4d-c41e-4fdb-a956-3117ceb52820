"""
APEX Admin System Database Configuration
إعداد قاعدة البيانات المتقدمة مع SQLAlchemy
"""

import asyncio
from typing import Optional, Dict, Any
from datetime import datetime

import structlog
from databases import Database
from sqlalchemy import (
    create_engine, MetaData, Table, Column, Integer, String, 
    Text, Boolean, DateTime, ForeignKey, JSON, Index,
    UniqueConstraint, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from .config import settings

logger = structlog.get_logger(__name__)

# Database connection
database = Database(
    settings.DATABASE_URL,
    min_size=5,
    max_size=settings.DATABASE_POOL_SIZE
)

# SQLAlchemy engine
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=settings.DEBUG
)

# Metadata
metadata = MetaData()

# ===== TABLES DEFINITION =====

# Admins table - جدول المديرين
admins_table = Table(
    "admins",
    metadata,
    Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("firebase_uid", String(128), unique=True, nullable=False, index=True),
    Column("email", String(255), unique=True, nullable=False, index=True),
    Column("display_name", String(255), nullable=True),
    Column("role", String(50), nullable=False, default="regular_admin"),
    Column("permissions", JSON, nullable=False, default=list),
    Column("is_active", Boolean, default=True, nullable=False),
    Column("last_login", DateTime(timezone=True), nullable=True),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    Column("updated_at", DateTime(timezone=True), server_default=func.now(), onupdate=func.now()),
    Column("created_by", UUID(as_uuid=True), ForeignKey("admins.id"), nullable=True),
    
    # Constraints
    CheckConstraint("role IN ('super_admin', 'regular_admin')", name="valid_admin_role"),
    Index("idx_admins_role_active", "role", "is_active"),
)

# Categories table - جدول الأقسام
categories_table = Table(
    "categories",
    metadata,
    Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("name", String(255), nullable=False),
    Column("name_en", String(255), nullable=True),
    Column("slug", String(255), unique=True, nullable=False, index=True),
    Column("description", Text, nullable=True),
    Column("icon", String(255), nullable=True),
    Column("color", String(7), nullable=True),  # Hex color
    Column("sort_order", Integer, default=0, nullable=False),
    Column("is_active", Boolean, default=True, nullable=False),
    Column("metadata", JSON, nullable=True),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    Column("updated_at", DateTime(timezone=True), server_default=func.now(), onupdate=func.now()),
    Column("created_by", UUID(as_uuid=True), ForeignKey("admins.id"), nullable=False),
    
    # Constraints
    CheckConstraint("LENGTH(name) >= 2", name="category_name_min_length"),
    CheckConstraint("sort_order >= 0", name="category_sort_order_positive"),
    Index("idx_categories_active_sort", "is_active", "sort_order"),
)

# Books table - جدول الكتب
books_table = Table(
    "books",
    metadata,
    Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("title", String(500), nullable=False),
    Column("title_en", String(500), nullable=True),
    Column("author", String(255), nullable=False),
    Column("author_en", String(255), nullable=True),
    Column("description", Text, nullable=True),
    Column("description_en", Text, nullable=True),
    Column("isbn", String(20), nullable=True, unique=True),
    Column("category_id", UUID(as_uuid=True), ForeignKey("categories.id"), nullable=False),
    Column("cover_image", String(500), nullable=True),
    Column("pdf_file", String(500), nullable=True),
    Column("file_size", Integer, nullable=True),  # in bytes
    Column("page_count", Integer, nullable=True),
    Column("language", String(10), default="ar", nullable=False),
    Column("rating", Integer, default=0, nullable=False),
    Column("download_count", Integer, default=0, nullable=False),
    Column("view_count", Integer, default=0, nullable=False),
    Column("is_featured", Boolean, default=False, nullable=False),
    Column("is_popular", Boolean, default=False, nullable=False),
    Column("is_active", Boolean, default=True, nullable=False),
    Column("tags", JSON, nullable=True),  # Array of tags
    Column("metadata", JSON, nullable=True),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    Column("updated_at", DateTime(timezone=True), server_default=func.now(), onupdate=func.now()),
    Column("created_by", UUID(as_uuid=True), ForeignKey("admins.id"), nullable=False),
    
    # Constraints
    CheckConstraint("LENGTH(title) >= 2", name="book_title_min_length"),
    CheckConstraint("LENGTH(author) >= 2", name="book_author_min_length"),
    CheckConstraint("rating >= 0 AND rating <= 5", name="book_rating_range"),
    CheckConstraint("download_count >= 0", name="book_download_count_positive"),
    CheckConstraint("view_count >= 0", name="book_view_count_positive"),
    CheckConstraint("file_size >= 0", name="book_file_size_positive"),
    CheckConstraint("page_count >= 0", name="book_page_count_positive"),
    
    # Indexes
    Index("idx_books_category_active", "category_id", "is_active"),
    Index("idx_books_featured", "is_featured", "is_active"),
    Index("idx_books_popular", "is_popular", "is_active"),
    Index("idx_books_rating", "rating", "is_active"),
    Index("idx_books_created", "created_at"),
)

# Admin Sessions table - جدول جلسات المديرين
admin_sessions_table = Table(
    "admin_sessions",
    metadata,
    Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("admin_id", UUID(as_uuid=True), ForeignKey("admins.id"), nullable=False),
    Column("session_token", String(255), unique=True, nullable=False, index=True),
    Column("firebase_token", Text, nullable=True),
    Column("ip_address", String(45), nullable=True),  # IPv6 support
    Column("user_agent", Text, nullable=True),
    Column("is_active", Boolean, default=True, nullable=False),
    Column("expires_at", DateTime(timezone=True), nullable=False),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    Column("last_activity", DateTime(timezone=True), server_default=func.now(), nullable=False),
    
    # Indexes
    Index("idx_sessions_admin_active", "admin_id", "is_active"),
    Index("idx_sessions_expires", "expires_at"),
)

# Audit Log table - جدول سجل العمليات
audit_log_table = Table(
    "audit_log",
    metadata,
    Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("admin_id", UUID(as_uuid=True), ForeignKey("admins.id"), nullable=True),
    Column("action", String(100), nullable=False),
    Column("resource_type", String(50), nullable=False),
    Column("resource_id", String(255), nullable=True),
    Column("old_values", JSON, nullable=True),
    Column("new_values", JSON, nullable=True),
    Column("ip_address", String(45), nullable=True),
    Column("user_agent", Text, nullable=True),
    Column("success", Boolean, default=True, nullable=False),
    Column("error_message", Text, nullable=True),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    
    # Indexes
    Index("idx_audit_admin_action", "admin_id", "action"),
    Index("idx_audit_resource", "resource_type", "resource_id"),
    Index("idx_audit_created", "created_at"),
)

# System Settings table - جدول إعدادات النظام
system_settings_table = Table(
    "system_settings",
    metadata,
    Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("key", String(255), unique=True, nullable=False, index=True),
    Column("value", JSON, nullable=True),
    Column("description", Text, nullable=True),
    Column("is_public", Boolean, default=False, nullable=False),
    Column("created_at", DateTime(timezone=True), server_default=func.now(), nullable=False),
    Column("updated_at", DateTime(timezone=True), server_default=func.now(), onupdate=func.now()),
    Column("updated_by", UUID(as_uuid=True), ForeignKey("admins.id"), nullable=True),
)


# ===== DATABASE UTILITIES =====

class DatabaseManager:
    """مدير قاعدة البيانات المتقدم"""
    
    def __init__(self):
        self.database = database
        self.metadata = metadata
        self.engine = engine
    
    async def create_tables(self):
        """إنشاء الجداول"""
        try:
            metadata.create_all(engine)
            logger.info("✅ تم إنشاء جداول قاعدة البيانات")
        except Exception as e:
            logger.error("❌ خطأ في إنشاء الجداول", error=str(e))
            raise
    
    async def drop_tables(self):
        """حذف الجداول (للتطوير فقط)"""
        if not settings.DEBUG:
            raise ValueError("Cannot drop tables in production")
        
        try:
            metadata.drop_all(engine)
            logger.warning("⚠️ تم حذف جداول قاعدة البيانات")
        except Exception as e:
            logger.error("❌ خطأ في حذف الجداول", error=str(e))
            raise
    
    async def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """الحصول على معلومات الجدول"""
        query = """
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns 
        WHERE table_name = :table_name
        ORDER BY ordinal_position
        """
        
        rows = await self.database.fetch_all(query, {"table_name": table_name})
        return [dict(row) for row in rows]
    
    async def execute_raw_query(self, query: str, values: Optional[Dict] = None):
        """تنفيذ استعلام مخصص"""
        try:
            if values:
                return await self.database.fetch_all(query, values)
            else:
                return await self.database.fetch_all(query)
        except Exception as e:
            logger.error("❌ خطأ في تنفيذ الاستعلام", query=query, error=str(e))
            raise


# Create database manager instance
db_manager = DatabaseManager()
