"""
APEX Admin System - FastAPI Backend
المبرمج الأسطوري - نظام إدارة متكامل عالي الأداء

نظام إدارة متقدم مع Firebase Authentication وصلاحيات متدرجة
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

import structlog
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from prometheus_client import Counter, Histogram, generate_latest
from starlette.middleware.base import BaseHTTPMiddleware

from .config import settings
from .database import database, engine, metadata
from .auth.firebase_auth import FirebaseAuth
from .auth.permissions import PermissionManager
from .routers import admin, books, categories, auth, monitoring
from .middleware.security import SecurityMiddleware
from .middleware.rate_limiting import RateLimitMiddleware
from .utils.logger import setup_logging

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)

# Metrics
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware لمراقبة الأداء والإحصائيات"""
    
    async def dispatch(self, request: Request, call_next):
        start_time = asyncio.get_event_loop().time()
        
        try:
            response = await call_next(request)
            
            # Record metrics
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status=response.status_code
            ).inc()
            
            duration = asyncio.get_event_loop().time() - start_time
            REQUEST_DURATION.observe(duration)
            
            # Log request
            logger.info(
                "request_completed",
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration=duration,
                client_ip=request.client.host if request.client else None
            )
            
            return response
            
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status=500
            ).inc()
            
            logger.error(
                "request_failed",
                method=request.method,
                path=request.url.path,
                duration=duration,
                error=str(e),
                client_ip=request.client.host if request.client else None
            )
            raise

@asynccontextmanager
async def lifespan(app: FastAPI):
    """إدارة دورة حياة التطبيق"""
    logger.info("🚀 بدء تشغيل APEX Admin System...")
    
    # Connect to database
    await database.connect()
    logger.info("✅ تم الاتصال بقاعدة البيانات")
    
    # Initialize Firebase
    firebase_auth = FirebaseAuth()
    await firebase_auth.initialize()
    logger.info("✅ تم تهيئة Firebase Authentication")
    
    # Initialize Permission Manager
    permission_manager = PermissionManager()
    await permission_manager.initialize()
    logger.info("✅ تم تهيئة نظام الصلاحيات")
    
    logger.info("🎯 APEX Admin System جاهز للعمل!")
    
    yield
    
    # Cleanup
    await database.disconnect()
    logger.info("👋 تم إيقاف APEX Admin System")

# Create FastAPI app
app = FastAPI(
    title="APEX Admin System",
    description="نظام إدارة متكامل للمكتبة الطبية الإلكترونية",
    version="1.0.0",
    docs_url="/admin/docs" if settings.DEBUG else None,
    redoc_url="/admin/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Security Middleware
app.add_middleware(SecurityMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(MetricsMiddleware)

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Trusted Host Middleware
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

# Mount static files
app.mount("/admin/static", StaticFiles(directory="admin_backend/static"), name="static")

# Include routers
app.include_router(auth.router, prefix="/admin/api/auth", tags=["Authentication"])
app.include_router(admin.router, prefix="/admin/api/admin", tags=["Admin Management"])
app.include_router(categories.router, prefix="/admin/api/categories", tags=["Categories"])
app.include_router(books.router, prefix="/admin/api/books", tags=["Books"])
app.include_router(monitoring.router, prefix="/admin/api/monitoring", tags=["Monitoring"])

@app.get("/admin/health")
async def health_check():
    """فحص صحة النظام"""
    try:
        # Check database connection
        await database.fetch_one("SELECT 1")
        
        return {
            "status": "healthy",
            "service": "APEX Admin System",
            "version": "1.0.0",
            "database": "connected",
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as e:
        logger.error("health_check_failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/admin/metrics")
async def get_metrics():
    """إحصائيات النظام"""
    return generate_latest()

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """معالج الأخطاء المخصص"""
    logger.warning(
        "http_exception",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": request.url.path
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """معالج الأخطاء العام"""
    logger.error(
        "unhandled_exception",
        error=str(exc),
        path=request.url.path,
        method=request.method,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "خطأ داخلي في الخادم",
            "status_code": 500,
            "path": request.url.path
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=settings.WORKERS if not settings.DEBUG else 1,
        log_config=None,  # Use our custom logging
        access_log=False  # Handled by our middleware
    )
