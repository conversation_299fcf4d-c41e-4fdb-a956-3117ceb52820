"""
APEX Authentication Router
نظام المصادقة والتحكم في الجلسات
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta

import structlog
from fastapi import APIRouter, HTTPException, Depends, Request, Response
from pydantic import BaseModel, EmailStr, Field

from ..auth.firebase_auth import (
    firebase_auth_manager, 
    get_current_admin, 
    require_super_admin
)
from ..auth.permissions import permission_manager
from ..database import database, admins_table, admin_sessions_table
from ..config import settings

logger = structlog.get_logger(__name__)

router = APIRouter()


# ===== REQUEST/RESPONSE MODELS =====

class LoginRequest(BaseModel):
    """طلب تسجيل الدخول"""
    firebase_token: str = Field(..., description="Firebase ID Token")


class LoginResponse(BaseModel):
    """استجابة تسجيل الدخول"""
    success: bool
    message: str
    admin: Optional[Dict[str, Any]] = None
    permissions: Optional[Dict[str, str]] = None
    session_token: Optional[str] = None


class CreateAdminRequest(BaseModel):
    """طلب إنشاء مدير جديد"""
    email: EmailStr = Field(..., description="البريد الإلكتروني")
    password: str = Field(..., min_length=8, description="كلمة المرور")
    display_name: str = Field(..., min_length=2, max_length=255, description="الاسم المعروض")
    role: str = Field(default="regular_admin", description="الدور")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePassword123!",
                "display_name": "مدير النظام",
                "role": "regular_admin"
            }
        }


class UpdatePermissionsRequest(BaseModel):
    """طلب تحديث الصلاحيات"""
    permissions: list[str] = Field(..., description="قائمة الصلاحيات")


class AdminResponse(BaseModel):
    """استجابة بيانات المدير"""
    id: str
    email: str
    display_name: str
    role: str
    permissions: list[str]
    is_active: bool
    last_login: Optional[datetime]
    created_at: datetime


# ===== AUTHENTICATION ENDPOINTS =====

@router.post("/login", response_model=LoginResponse)
async def login(
    request: Request,
    login_data: LoginRequest
):
    """تسجيل دخول المدير"""
    try:
        # Verify Firebase token and get admin data
        admin_data = await firebase_auth_manager.verify_token(login_data.firebase_token)
        
        if not admin_data:
            raise HTTPException(
                status_code=401,
                detail="بيانات المصادقة غير صحيحة"
            )
        
        # Create session token (simplified - in production use proper JWT)
        session_token = f"session_{admin_data['admin_id']}_{datetime.utcnow().timestamp()}"
        
        # Store session in database
        session_insert = admin_sessions_table.insert().values(
            admin_id=admin_data['admin_id'],
            session_token=session_token,
            firebase_token=login_data.firebase_token,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            expires_at=datetime.utcnow() + timedelta(hours=24),
            created_at=datetime.utcnow(),
            last_activity=datetime.utcnow()
        )
        
        await database.execute(session_insert)
        
        # Get permission descriptions
        permissions_desc = {}
        for perm in admin_data['permissions']:
            desc = permission_manager.get_permission_description(perm)
            if desc:
                permissions_desc[perm] = desc
        
        # Log successful login
        await firebase_auth_manager.log_admin_action(
            admin_id=admin_data['admin_id'],
            action="auth.login",
            resource_type="session",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            success=True
        )
        
        logger.info(
            "تسجيل دخول ناجح",
            admin_id=admin_data['admin_id'],
            email=admin_data['email'],
            role=admin_data['role']
        )
        
        return LoginResponse(
            success=True,
            message="تم تسجيل الدخول بنجاح",
            admin={
                "id": admin_data['admin_id'],
                "email": admin_data['email'],
                "display_name": admin_data['display_name'],
                "role": admin_data['role']
            },
            permissions=permissions_desc,
            session_token=session_token
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في تسجيل الدخول", error=str(e))
        
        # Log failed login attempt
        await firebase_auth_manager.log_admin_action(
            admin_id=None,
            action="auth.login",
            resource_type="session",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            success=False,
            error_message=str(e)
        )
        
        raise HTTPException(
            status_code=500,
            detail="خطأ في تسجيل الدخول"
        )


@router.post("/logout")
async def logout(
    request: Request,
    current_admin: Dict[str, Any] = Depends(get_current_admin)
):
    """تسجيل خروج المدير"""
    try:
        # Get session token from header
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            session_token = auth_header[7:]
            
            # Invalidate session
            update_query = admin_sessions_table.update().where(
                admin_sessions_table.c.session_token == session_token
            ).values(is_active=False)
            
            await database.execute(update_query)
        
        # Log logout
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="auth.logout",
            resource_type="session",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        logger.info("تسجيل خروج ناجح", admin_id=current_admin['admin_id'])
        
        return {"success": True, "message": "تم تسجيل الخروج بنجاح"}
        
    except Exception as e:
        logger.error("خطأ في تسجيل الخروج", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في تسجيل الخروج")


@router.get("/me", response_model=AdminResponse)
async def get_current_admin_info(
    current_admin: Dict[str, Any] = Depends(get_current_admin)
):
    """الحصول على بيانات المدير الحالي"""
    try:
        # Get full admin data from database
        query = admins_table.select().where(
            admins_table.c.id == current_admin['admin_id']
        )
        admin = await database.fetch_one(query)
        
        if not admin:
            raise HTTPException(status_code=404, detail="المدير غير موجود")
        
        return AdminResponse(
            id=str(admin['id']),
            email=admin['email'],
            display_name=admin['display_name'],
            role=admin['role'],
            permissions=admin['permissions'],
            is_active=admin['is_active'],
            last_login=admin['last_login'],
            created_at=admin['created_at']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في الحصول على بيانات المدير", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على البيانات")


@router.get("/permissions")
async def get_available_permissions(
    current_admin: Dict[str, Any] = Depends(get_current_admin)
):
    """الحصول على جميع الصلاحيات المتاحة"""
    try:
        all_permissions = permission_manager.get_all_permissions()
        
        # Group permissions by resource type
        grouped_permissions = {}
        for perm, desc in all_permissions.items():
            resource_type = perm.split('.')[0]
            if resource_type not in grouped_permissions:
                grouped_permissions[resource_type] = {}
            grouped_permissions[resource_type][perm] = desc
        
        return {
            "success": True,
            "permissions": grouped_permissions,
            "user_permissions": current_admin['permissions']
        }
        
    except Exception as e:
        logger.error("خطأ في الحصول على الصلاحيات", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على الصلاحيات")


# ===== ADMIN MANAGEMENT ENDPOINTS =====

@router.post("/admins", response_model=AdminResponse)
async def create_admin(
    admin_data: CreateAdminRequest,
    current_admin: Dict[str, Any] = Depends(require_super_admin)
):
    """إنشاء مدير جديد (للأدمن الرئيسي فقط)"""
    try:
        # Validate role
        if admin_data.role not in ["super_admin", "regular_admin"]:
            raise HTTPException(status_code=400, detail="دور غير صحيح")
        
        # Only super admin can create other super admins
        if admin_data.role == "super_admin" and current_admin['role'] != "super_admin":
            raise HTTPException(
                status_code=403, 
                detail="فقط الأدمن الرئيسي يمكنه إنشاء أدمن رئيسي آخر"
            )
        
        # Create admin
        new_admin = await firebase_auth_manager.create_admin(
            email=admin_data.email,
            password=admin_data.password,
            display_name=admin_data.display_name,
            role=admin_data.role,
            created_by_id=current_admin['admin_id']
        )
        
        logger.info(
            "تم إنشاء مدير جديد",
            new_admin_id=new_admin['id'],
            email=new_admin['email'],
            role=new_admin['role'],
            created_by=current_admin['admin_id']
        )
        
        return AdminResponse(
            id=new_admin['id'],
            email=new_admin['email'],
            display_name=new_admin['display_name'],
            role=new_admin['role'],
            permissions=new_admin['permissions'],
            is_active=True,
            last_login=None,
            created_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في إنشاء المدير", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في إنشاء المدير")


@router.get("/admins")
async def list_admins(
    current_admin: Dict[str, Any] = Depends(require_super_admin)
):
    """عرض جميع المديرين (للأدمن الرئيسي فقط)"""
    try:
        query = admins_table.select().order_by(admins_table.c.created_at.desc())
        admins = await database.fetch_all(query)
        
        admin_list = []
        for admin in admins:
            admin_list.append({
                "id": str(admin['id']),
                "email": admin['email'],
                "display_name": admin['display_name'],
                "role": admin['role'],
                "is_active": admin['is_active'],
                "last_login": admin['last_login'],
                "created_at": admin['created_at'],
                "permissions_count": len(admin['permissions'])
            })
        
        return {
            "success": True,
            "admins": admin_list,
            "total": len(admin_list)
        }
        
    except Exception as e:
        logger.error("خطأ في عرض المديرين", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في عرض المديرين")


@router.put("/admins/{admin_id}/permissions")
async def update_admin_permissions(
    admin_id: str,
    permissions_data: UpdatePermissionsRequest,
    current_admin: Dict[str, Any] = Depends(require_super_admin)
):
    """تحديث صلاحيات المدير"""
    try:
        # Validate permissions
        valid_permissions = permission_manager.validate_permissions(permissions_data.permissions)
        
        if len(valid_permissions) != len(permissions_data.permissions):
            raise HTTPException(status_code=400, detail="بعض الصلاحيات غير صحيحة")
        
        # Update permissions
        await firebase_auth_manager.update_admin_permissions(
            admin_id=admin_id,
            permissions=valid_permissions,
            updated_by_id=current_admin['admin_id']
        )
        
        return {
            "success": True,
            "message": "تم تحديث الصلاحيات بنجاح",
            "permissions": valid_permissions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في تحديث الصلاحيات", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في تحديث الصلاحيات")


@router.delete("/admins/{admin_id}")
async def deactivate_admin(
    admin_id: str,
    current_admin: Dict[str, Any] = Depends(require_super_admin)
):
    """إلغاء تفعيل المدير"""
    try:
        # Cannot deactivate self
        if admin_id == current_admin['admin_id']:
            raise HTTPException(status_code=400, detail="لا يمكن إلغاء تفعيل حسابك الخاص")
        
        await firebase_auth_manager.deactivate_admin(
            admin_id=admin_id,
            deactivated_by_id=current_admin['admin_id']
        )
        
        return {
            "success": True,
            "message": "تم إلغاء تفعيل المدير بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في إلغاء تفعيل المدير", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في إلغاء تفعيل المدير")
