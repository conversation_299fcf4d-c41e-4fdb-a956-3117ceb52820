"""
APEX Books Management Router
نظام إدارة الكتب المتقدم مع رفع الملفات
"""

import uuid
import os
from typing import Dict, Any, Optional, List
from datetime import datetime

import structlog
from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File, Form
from pydantic import BaseModel, Field, validator
from slugify import slugify

from ..auth.firebase_auth import get_current_admin, firebase_auth_manager
from ..auth.permissions import require_permission
from ..database import database, books_table, categories_table
from ..config import settings
from ..utils.file_handler import FileHandler

logger = structlog.get_logger(__name__)

router = APIRouter()

# Initialize file handler
file_handler = FileHandler()


# ===== REQUEST/RESPONSE MODELS =====

class BookCreate(BaseModel):
    """إنشاء كتاب جديد"""
    title: str = Field(..., min_length=2, max_length=500, description="عنوان الكتاب")
    title_en: Optional[str] = Field(None, max_length=500, description="العنوان بالإنجليزية")
    author: str = Field(..., min_length=2, max_length=255, description="المؤلف")
    author_en: Optional[str] = Field(None, max_length=255, description="المؤلف بالإنجليزية")
    description: Optional[str] = Field(None, description="وصف الكتاب")
    description_en: Optional[str] = Field(None, description="الوصف بالإنجليزية")
    isbn: Optional[str] = Field(None, max_length=20, description="رقم ISBN")
    category_id: str = Field(..., description="معرف القسم")
    language: str = Field(default="ar", description="لغة الكتاب")
    page_count: Optional[int] = Field(None, ge=1, description="عدد الصفحات")
    rating: int = Field(default=0, ge=0, le=5, description="التقييم")
    is_featured: bool = Field(default=False, description="كتاب مميز")
    is_popular: bool = Field(default=False, description="كتاب شائع")
    tags: Optional[List[str]] = Field(default=[], description="العلامات")
    
    @validator('title', 'author')
    def validate_required_fields(cls, v):
        if not v.strip():
            raise ValueError('هذا الحقل مطلوب')
        return v.strip()
    
    @validator('language')
    def validate_language(cls, v):
        allowed_languages = ['ar', 'en', 'fr', 'de', 'es']
        if v not in allowed_languages:
            raise ValueError(f'اللغة يجب أن تكون واحدة من: {allowed_languages}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "title": "أطلس نيتر للتشريح البشري",
                "title_en": "Netter's Atlas of Human Anatomy",
                "author": "فرانك نيتر",
                "author_en": "Frank Netter",
                "description": "المرجع الأشهر في التشريح البشري",
                "category_id": "uuid-here",
                "language": "ar",
                "page_count": 672,
                "rating": 5,
                "is_featured": True,
                "tags": ["تشريح", "طب", "مرجع"]
            }
        }


class BookUpdate(BaseModel):
    """تحديث كتاب"""
    title: Optional[str] = Field(None, min_length=2, max_length=500)
    title_en: Optional[str] = Field(None, max_length=500)
    author: Optional[str] = Field(None, min_length=2, max_length=255)
    author_en: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    description_en: Optional[str] = None
    isbn: Optional[str] = Field(None, max_length=20)
    category_id: Optional[str] = None
    language: Optional[str] = None
    page_count: Optional[int] = Field(None, ge=1)
    rating: Optional[int] = Field(None, ge=0, le=5)
    is_featured: Optional[bool] = None
    is_popular: Optional[bool] = None
    is_active: Optional[bool] = None
    tags: Optional[List[str]] = None
    
    @validator('language')
    def validate_language(cls, v):
        if v is not None:
            allowed_languages = ['ar', 'en', 'fr', 'de', 'es']
            if v not in allowed_languages:
                raise ValueError(f'اللغة يجب أن تكون واحدة من: {allowed_languages}')
        return v


class BookResponse(BaseModel):
    """استجابة بيانات الكتاب"""
    id: str
    title: str
    title_en: Optional[str]
    author: str
    author_en: Optional[str]
    description: Optional[str]
    description_en: Optional[str]
    isbn: Optional[str]
    category_id: str
    category_name: str
    cover_image: Optional[str]
    pdf_file: Optional[str]
    file_size: Optional[int]
    page_count: Optional[int]
    language: str
    rating: int
    download_count: int
    view_count: int
    is_featured: bool
    is_popular: bool
    is_active: bool
    tags: List[str]
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: str


class BookListResponse(BaseModel):
    """استجابة قائمة الكتب"""
    success: bool
    books: List[BookResponse]
    total: int
    page: int
    per_page: int


# ===== UTILITY FUNCTIONS =====

async def validate_category_exists(category_id: str) -> bool:
    """التحقق من وجود القسم"""
    query = categories_table.select().where(
        (categories_table.c.id == category_id) &
        (categories_table.c.is_active == True)
    )
    category = await database.fetch_one(query)
    return category is not None


async def get_category_name(category_id: str) -> str:
    """الحصول على اسم القسم"""
    query = categories_table.select().where(categories_table.c.id == category_id)
    category = await database.fetch_one(query)
    return category['name'] if category else "غير محدد"


# ===== BOOK ENDPOINTS =====

@router.get("/", response_model=BookListResponse)
async def list_books(
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(20, ge=1, le=100, description="عدد العناصر في الصفحة"),
    search: Optional[str] = Query(None, description="البحث في العناوين والمؤلفين"),
    category_id: Optional[str] = Query(None, description="تصفية حسب القسم"),
    language: Optional[str] = Query(None, description="تصفية حسب اللغة"),
    featured_only: bool = Query(False, description="الكتب المميزة فقط"),
    popular_only: bool = Query(False, description="الكتب الشائعة فقط"),
    active_only: bool = Query(True, description="الكتب النشطة فقط"),
    current_admin: Dict[str, Any] = Depends(require_permission("books.read"))
):
    """عرض جميع الكتب مع البحث والتصفح"""
    try:
        # Build query with join to get category name
        query = books_table.select().select_from(
            books_table.join(categories_table, books_table.c.category_id == categories_table.c.id)
        )
        
        # Apply filters
        conditions = []
        if active_only:
            conditions.append(books_table.c.is_active == True)
        
        if category_id:
            conditions.append(books_table.c.category_id == category_id)
        
        if language:
            conditions.append(books_table.c.language == language)
        
        if featured_only:
            conditions.append(books_table.c.is_featured == True)
        
        if popular_only:
            conditions.append(books_table.c.is_popular == True)
        
        if search:
            search_term = f"%{search}%"
            conditions.append(
                (books_table.c.title.ilike(search_term)) |
                (books_table.c.title_en.ilike(search_term)) |
                (books_table.c.author.ilike(search_term)) |
                (books_table.c.author_en.ilike(search_term)) |
                (books_table.c.description.ilike(search_term))
            )
        
        if conditions:
            query = query.where(*conditions)
        
        # Count total
        count_query = query.with_only_columns([books_table.c.id])
        total_books = await database.fetch_all(count_query)
        total = len(total_books)
        
        # Apply pagination and ordering
        offset = (page - 1) * per_page
        query = query.order_by(
            books_table.c.is_featured.desc(),
            books_table.c.rating.desc(),
            books_table.c.created_at.desc()
        ).limit(per_page).offset(offset)
        
        books = await database.fetch_all(query)
        
        # Build response
        book_list = []
        for book in books:
            category_name = await get_category_name(str(book['category_id']))
            
            book_list.append(BookResponse(
                id=str(book['id']),
                title=book['title'],
                title_en=book['title_en'],
                author=book['author'],
                author_en=book['author_en'],
                description=book['description'],
                description_en=book['description_en'],
                isbn=book['isbn'],
                category_id=str(book['category_id']),
                category_name=category_name,
                cover_image=book['cover_image'],
                pdf_file=book['pdf_file'],
                file_size=book['file_size'],
                page_count=book['page_count'],
                language=book['language'],
                rating=book['rating'],
                download_count=book['download_count'],
                view_count=book['view_count'],
                is_featured=book['is_featured'],
                is_popular=book['is_popular'],
                is_active=book['is_active'],
                tags=book['tags'] or [],
                created_at=book['created_at'],
                updated_at=book['updated_at'],
                created_by=str(book['created_by'])
            ))
        
        return BookListResponse(
            success=True,
            books=book_list,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error("خطأ في عرض الكتب", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في عرض الكتب")


@router.get("/{book_id}", response_model=BookResponse)
async def get_book(
    book_id: str,
    current_admin: Dict[str, Any] = Depends(require_permission("books.read"))
):
    """الحصول على كتاب محدد"""
    try:
        query = books_table.select().where(books_table.c.id == book_id)
        book = await database.fetch_one(query)
        
        if not book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")
        
        category_name = await get_category_name(str(book['category_id']))
        
        return BookResponse(
            id=str(book['id']),
            title=book['title'],
            title_en=book['title_en'],
            author=book['author'],
            author_en=book['author_en'],
            description=book['description'],
            description_en=book['description_en'],
            isbn=book['isbn'],
            category_id=str(book['category_id']),
            category_name=category_name,
            cover_image=book['cover_image'],
            pdf_file=book['pdf_file'],
            file_size=book['file_size'],
            page_count=book['page_count'],
            language=book['language'],
            rating=book['rating'],
            download_count=book['download_count'],
            view_count=book['view_count'],
            is_featured=book['is_featured'],
            is_popular=book['is_popular'],
            is_active=book['is_active'],
            tags=book['tags'] or [],
            created_at=book['created_at'],
            updated_at=book['updated_at'],
            created_by=str(book['created_by'])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في الحصول على الكتاب", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على الكتاب")


@router.post("/", response_model=BookResponse)
async def create_book(
    book_data: BookCreate,
    current_admin: Dict[str, Any] = Depends(require_permission("books.create"))
):
    """إنشاء كتاب جديد"""
    try:
        # Validate category exists
        if not await validate_category_exists(book_data.category_id):
            raise HTTPException(status_code=400, detail="القسم غير موجود أو غير نشط")
        
        # Check ISBN uniqueness if provided
        if book_data.isbn:
            isbn_query = books_table.select().where(books_table.c.isbn == book_data.isbn)
            existing_isbn = await database.fetch_one(isbn_query)
            if existing_isbn:
                raise HTTPException(status_code=400, detail="رقم ISBN مستخدم بالفعل")
        
        # Create book
        book_id = uuid.uuid4()
        insert_query = books_table.insert().values(
            id=book_id,
            title=book_data.title,
            title_en=book_data.title_en,
            author=book_data.author,
            author_en=book_data.author_en,
            description=book_data.description,
            description_en=book_data.description_en,
            isbn=book_data.isbn,
            category_id=book_data.category_id,
            language=book_data.language,
            page_count=book_data.page_count,
            rating=book_data.rating,
            is_featured=book_data.is_featured,
            is_popular=book_data.is_popular,
            is_active=True,
            tags=book_data.tags,
            download_count=0,
            view_count=0,
            created_at=datetime.utcnow(),
            created_by=current_admin['admin_id']
        )
        
        await database.execute(insert_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="books.create",
            resource_type="book",
            resource_id=str(book_id),
            new_values={
                "title": book_data.title,
                "author": book_data.author,
                "category_id": book_data.category_id
            }
        )
        
        category_name = await get_category_name(book_data.category_id)
        
        logger.info(
            "تم إنشاء كتاب جديد",
            book_id=str(book_id),
            title=book_data.title,
            created_by=current_admin['admin_id']
        )
        
        return BookResponse(
            id=str(book_id),
            title=book_data.title,
            title_en=book_data.title_en,
            author=book_data.author,
            author_en=book_data.author_en,
            description=book_data.description,
            description_en=book_data.description_en,
            isbn=book_data.isbn,
            category_id=book_data.category_id,
            category_name=category_name,
            cover_image=None,
            pdf_file=None,
            file_size=None,
            page_count=book_data.page_count,
            language=book_data.language,
            rating=book_data.rating,
            download_count=0,
            view_count=0,
            is_featured=book_data.is_featured,
            is_popular=book_data.is_popular,
            is_active=True,
            tags=book_data.tags or [],
            created_at=datetime.utcnow(),
            updated_at=None,
            created_by=current_admin['admin_id']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في إنشاء الكتاب", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في إنشاء الكتاب")


@router.put("/{book_id}", response_model=BookResponse)
async def update_book(
    book_id: str,
    book_data: BookUpdate,
    current_admin: Dict[str, Any] = Depends(require_permission("books.update"))
):
    """تحديث كتاب"""
    try:
        # Get existing book
        query = books_table.select().where(books_table.c.id == book_id)
        existing_book = await database.fetch_one(query)
        
        if not existing_book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")
        
        # Validate category if provided
        if book_data.category_id and not await validate_category_exists(book_data.category_id):
            raise HTTPException(status_code=400, detail="القسم غير موجود أو غير نشط")
        
        # Check ISBN uniqueness if provided and changed
        if book_data.isbn and book_data.isbn != existing_book['isbn']:
            isbn_query = books_table.select().where(books_table.c.isbn == book_data.isbn)
            existing_isbn = await database.fetch_one(isbn_query)
            if existing_isbn:
                raise HTTPException(status_code=400, detail="رقم ISBN مستخدم بالفعل")
        
        # Prepare update data
        update_data = {}
        old_values = {}
        new_values = {}
        
        # Check each field for updates
        fields_to_check = [
            'title', 'title_en', 'author', 'author_en', 'description', 
            'description_en', 'isbn', 'category_id', 'language', 
            'page_count', 'rating', 'is_featured', 'is_popular', 
            'is_active', 'tags'
        ]
        
        for field in fields_to_check:
            new_value = getattr(book_data, field)
            if new_value is not None:
                old_value = existing_book[field]
                if new_value != old_value:
                    update_data[field] = new_value
                    old_values[field] = old_value
                    new_values[field] = new_value
        
        if not update_data:
            raise HTTPException(status_code=400, detail="لا توجد بيانات للتحديث")
        
        # Add updated timestamp
        update_data['updated_at'] = datetime.utcnow()
        
        # Update book
        update_query = books_table.update().where(
            books_table.c.id == book_id
        ).values(**update_data)
        
        await database.execute(update_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="books.update",
            resource_type="book",
            resource_id=book_id,
            old_values=old_values,
            new_values=new_values
        )
        
        # Get updated book
        updated_book = await database.fetch_one(
            books_table.select().where(books_table.c.id == book_id)
        )
        
        category_name = await get_category_name(str(updated_book['category_id']))
        
        logger.info(
            "تم تحديث الكتاب",
            book_id=book_id,
            updated_by=current_admin['admin_id']
        )
        
        return BookResponse(
            id=str(updated_book['id']),
            title=updated_book['title'],
            title_en=updated_book['title_en'],
            author=updated_book['author'],
            author_en=updated_book['author_en'],
            description=updated_book['description'],
            description_en=updated_book['description_en'],
            isbn=updated_book['isbn'],
            category_id=str(updated_book['category_id']),
            category_name=category_name,
            cover_image=updated_book['cover_image'],
            pdf_file=updated_book['pdf_file'],
            file_size=updated_book['file_size'],
            page_count=updated_book['page_count'],
            language=updated_book['language'],
            rating=updated_book['rating'],
            download_count=updated_book['download_count'],
            view_count=updated_book['view_count'],
            is_featured=updated_book['is_featured'],
            is_popular=updated_book['is_popular'],
            is_active=updated_book['is_active'],
            tags=updated_book['tags'] or [],
            created_at=updated_book['created_at'],
            updated_at=updated_book['updated_at'],
            created_by=str(updated_book['created_by'])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في تحديث الكتاب", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في تحديث الكتاب")


@router.delete("/{book_id}")
async def delete_book(
    book_id: str,
    current_admin: Dict[str, Any] = Depends(require_permission("books.delete"))
):
    """حذف كتاب"""
    try:
        # Check if book exists
        query = books_table.select().where(books_table.c.id == book_id)
        book = await database.fetch_one(query)
        
        if not book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")
        
        # Delete associated files
        if book['cover_image']:
            await file_handler.delete_file(book['cover_image'])
        
        if book['pdf_file']:
            await file_handler.delete_file(book['pdf_file'])
        
        # Delete book
        delete_query = books_table.delete().where(books_table.c.id == book_id)
        await database.execute(delete_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="books.delete",
            resource_type="book",
            resource_id=book_id,
            old_values={
                "title": book['title'],
                "author": book['author'],
                "had_files": bool(book['cover_image'] or book['pdf_file'])
            }
        )
        
        logger.info(
            "تم حذف الكتاب",
            book_id=book_id,
            title=book['title'],
            deleted_by=current_admin['admin_id']
        )
        
        return {
            "success": True,
            "message": "تم حذف الكتاب بنجاح"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في حذف الكتاب", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في حذف الكتاب")


# ===== FILE UPLOAD ENDPOINTS =====

@router.post("/{book_id}/cover")
async def upload_book_cover(
    book_id: str,
    cover_file: UploadFile = File(..., description="ملف صورة الغلاف"),
    current_admin: Dict[str, Any] = Depends(require_permission("files.create"))
):
    """رفع صورة غلاف الكتاب"""
    try:
        # Check if book exists
        query = books_table.select().where(books_table.c.id == book_id)
        book = await database.fetch_one(query)

        if not book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")

        # Validate file is an image
        file_info = await file_handler.validate_file(cover_file)
        if not file_info["is_image"]:
            raise HTTPException(status_code=400, detail="يجب أن يكون الملف صورة")

        # Delete old cover if exists
        if book['cover_image']:
            await file_handler.delete_file(book['cover_image'])

        # Save new cover
        file_result = await file_handler.save_file(cover_file, "cover")

        # Update book record
        update_query = books_table.update().where(
            books_table.c.id == book_id
        ).values(
            cover_image=file_result["relative_path"],
            updated_at=datetime.utcnow()
        )

        await database.execute(update_query)

        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="files.upload",
            resource_type="book_cover",
            resource_id=book_id,
            new_values={
                "filename": file_result["filename"],
                "file_size": file_result["file_size"]
            }
        )

        logger.info(
            "تم رفع صورة الغلاف",
            book_id=book_id,
            filename=file_result["filename"],
            uploaded_by=current_admin['admin_id']
        )

        return {
            "success": True,
            "message": "تم رفع صورة الغلاف بنجاح",
            "file_info": {
                "filename": file_result["filename"],
                "file_path": file_result["relative_path"],
                "file_size": file_result["file_size"],
                "thumbnail_path": file_result["thumbnail_path"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في رفع صورة الغلاف", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في رفع صورة الغلاف")


@router.post("/{book_id}/pdf")
async def upload_book_pdf(
    book_id: str,
    pdf_file: UploadFile = File(..., description="ملف PDF الكتاب"),
    current_admin: Dict[str, Any] = Depends(require_permission("files.create"))
):
    """رفع ملف PDF الكتاب"""
    try:
        # Check if book exists
        query = books_table.select().where(books_table.c.id == book_id)
        book = await database.fetch_one(query)

        if not book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")

        # Validate file is a PDF
        file_info = await file_handler.validate_file(pdf_file)
        if not file_info["is_pdf"]:
            raise HTTPException(status_code=400, detail="يجب أن يكون الملف PDF")

        # Delete old PDF if exists
        if book['pdf_file']:
            await file_handler.delete_file(book['pdf_file'])

        # Save new PDF
        file_result = await file_handler.save_file(pdf_file, "book")

        # Update book record
        update_query = books_table.update().where(
            books_table.c.id == book_id
        ).values(
            pdf_file=file_result["relative_path"],
            file_size=file_result["file_size"],
            page_count=file_result["metadata"].get("pages"),
            updated_at=datetime.utcnow()
        )

        await database.execute(update_query)

        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="files.upload",
            resource_type="book_pdf",
            resource_id=book_id,
            new_values={
                "filename": file_result["filename"],
                "file_size": file_result["file_size"]
            }
        )

        logger.info(
            "تم رفع ملف PDF",
            book_id=book_id,
            filename=file_result["filename"],
            file_size=file_result["file_size"],
            uploaded_by=current_admin['admin_id']
        )

        return {
            "success": True,
            "message": "تم رفع ملف PDF بنجاح",
            "file_info": {
                "filename": file_result["filename"],
                "file_path": file_result["relative_path"],
                "file_size": file_result["file_size"],
                "metadata": file_result["metadata"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في رفع ملف PDF", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في رفع ملف PDF")


@router.delete("/{book_id}/cover")
async def delete_book_cover(
    book_id: str,
    current_admin: Dict[str, Any] = Depends(require_permission("files.delete"))
):
    """حذف صورة غلاف الكتاب"""
    try:
        # Check if book exists
        query = books_table.select().where(books_table.c.id == book_id)
        book = await database.fetch_one(query)

        if not book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")

        if not book['cover_image']:
            raise HTTPException(status_code=404, detail="لا توجد صورة غلاف للحذف")

        # Delete cover file
        deleted = await file_handler.delete_file(book['cover_image'])

        if deleted:
            # Update book record
            update_query = books_table.update().where(
                books_table.c.id == book_id
            ).values(
                cover_image=None,
                updated_at=datetime.utcnow()
            )

            await database.execute(update_query)

            # Log the action
            await firebase_auth_manager.log_admin_action(
                admin_id=current_admin['admin_id'],
                action="files.delete",
                resource_type="book_cover",
                resource_id=book_id,
                old_values={"cover_image": book['cover_image']}
            )

            logger.info(
                "تم حذف صورة الغلاف",
                book_id=book_id,
                deleted_by=current_admin['admin_id']
            )

        return {
            "success": True,
            "message": "تم حذف صورة الغلاف بنجاح"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في حذف صورة الغلاف", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في حذف صورة الغلاف")


@router.delete("/{book_id}/pdf")
async def delete_book_pdf(
    book_id: str,
    current_admin: Dict[str, Any] = Depends(require_permission("files.delete"))
):
    """حذف ملف PDF الكتاب"""
    try:
        # Check if book exists
        query = books_table.select().where(books_table.c.id == book_id)
        book = await database.fetch_one(query)

        if not book:
            raise HTTPException(status_code=404, detail="الكتاب غير موجود")

        if not book['pdf_file']:
            raise HTTPException(status_code=404, detail="لا يوجد ملف PDF للحذف")

        # Delete PDF file
        deleted = await file_handler.delete_file(book['pdf_file'])

        if deleted:
            # Update book record
            update_query = books_table.update().where(
                books_table.c.id == book_id
            ).values(
                pdf_file=None,
                file_size=None,
                updated_at=datetime.utcnow()
            )

            await database.execute(update_query)

            # Log the action
            await firebase_auth_manager.log_admin_action(
                admin_id=current_admin['admin_id'],
                action="files.delete",
                resource_type="book_pdf",
                resource_id=book_id,
                old_values={"pdf_file": book['pdf_file']}
            )

            logger.info(
                "تم حذف ملف PDF",
                book_id=book_id,
                deleted_by=current_admin['admin_id']
            )

        return {
            "success": True,
            "message": "تم حذف ملف PDF بنجاح"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في حذف ملف PDF", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في حذف ملف PDF")
