"""
APEX Categories Management Router
نظام إدارة الأقسام المتقدم
"""

import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

import structlog
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field, validator
from slugify import slugify

from ..auth.firebase_auth import get_current_admin, firebase_auth_manager
from ..auth.permissions import require_permission
from ..database import database, categories_table, books_table
from ..config import settings

logger = structlog.get_logger(__name__)

router = APIRouter()


# ===== REQUEST/RESPONSE MODELS =====

class CategoryCreate(BaseModel):
    """إنشاء قسم جديد"""
    name: str = Field(..., min_length=2, max_length=255, description="اسم القسم")
    name_en: Optional[str] = Field(None, max_length=255, description="الاسم بالإنجليزية")
    description: Optional[str] = Field(None, description="وصف القسم")
    icon: Optional[str] = Field(None, description="أيقونة القسم")
    color: Optional[str] = Field(None, regex=r"^#[0-9A-Fa-f]{6}$", description="لون القسم")
    sort_order: int = Field(default=0, ge=0, description="ترتيب القسم")
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('اسم القسم مطلوب')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "name": "الكتب الطبية",
                "name_en": "Medical Books",
                "description": "مجموعة من الكتب الطبية المتخصصة",
                "icon": "fas fa-book-medical",
                "color": "#3498db",
                "sort_order": 1
            }
        }


class CategoryUpdate(BaseModel):
    """تحديث قسم"""
    name: Optional[str] = Field(None, min_length=2, max_length=255)
    name_en: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = Field(None, regex=r"^#[0-9A-Fa-f]{6}$")
    sort_order: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('اسم القسم لا يمكن أن يكون فارغاً')
        return v.strip() if v else v


class CategoryResponse(BaseModel):
    """استجابة بيانات القسم"""
    id: str
    name: str
    name_en: Optional[str]
    slug: str
    description: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    sort_order: int
    is_active: bool
    books_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: str


class CategoryListResponse(BaseModel):
    """استجابة قائمة الأقسام"""
    success: bool
    categories: List[CategoryResponse]
    total: int
    page: int
    per_page: int


# ===== UTILITY FUNCTIONS =====

async def generate_unique_slug(name: str, category_id: Optional[str] = None) -> str:
    """إنشاء slug فريد للقسم"""
    base_slug = slugify(name, language='ar')
    slug = base_slug
    counter = 1
    
    while True:
        # Check if slug exists
        query = categories_table.select().where(categories_table.c.slug == slug)
        if category_id:
            query = query.where(categories_table.c.id != category_id)
        
        existing = await database.fetch_one(query)
        if not existing:
            break
        
        slug = f"{base_slug}-{counter}"
        counter += 1
    
    return slug


async def get_category_books_count(category_id: str) -> int:
    """الحصول على عدد الكتب في القسم"""
    query = books_table.select().where(
        (books_table.c.category_id == category_id) &
        (books_table.c.is_active == True)
    ).with_only_columns([books_table.c.id])
    
    books = await database.fetch_all(query)
    return len(books)


# ===== CATEGORY ENDPOINTS =====

@router.get("/", response_model=CategoryListResponse)
async def list_categories(
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(20, ge=1, le=100, description="عدد العناصر في الصفحة"),
    search: Optional[str] = Query(None, description="البحث في الأسماء"),
    active_only: bool = Query(True, description="الأقسام النشطة فقط"),
    current_admin: Dict[str, Any] = Depends(require_permission("categories.read"))
):
    """عرض جميع الأقسام مع البحث والتصفح"""
    try:
        # Build query
        query = categories_table.select()
        
        # Apply filters
        conditions = []
        if active_only:
            conditions.append(categories_table.c.is_active == True)
        
        if search:
            search_term = f"%{search}%"
            conditions.append(
                (categories_table.c.name.ilike(search_term)) |
                (categories_table.c.name_en.ilike(search_term)) |
                (categories_table.c.description.ilike(search_term))
            )
        
        if conditions:
            query = query.where(*conditions)
        
        # Count total
        count_query = query.with_only_columns([categories_table.c.id])
        total_categories = await database.fetch_all(count_query)
        total = len(total_categories)
        
        # Apply pagination and ordering
        offset = (page - 1) * per_page
        query = query.order_by(
            categories_table.c.sort_order.asc(),
            categories_table.c.name.asc()
        ).limit(per_page).offset(offset)
        
        categories = await database.fetch_all(query)
        
        # Build response with books count
        category_list = []
        for category in categories:
            books_count = await get_category_books_count(str(category['id']))
            
            category_list.append(CategoryResponse(
                id=str(category['id']),
                name=category['name'],
                name_en=category['name_en'],
                slug=category['slug'],
                description=category['description'],
                icon=category['icon'],
                color=category['color'],
                sort_order=category['sort_order'],
                is_active=category['is_active'],
                books_count=books_count,
                created_at=category['created_at'],
                updated_at=category['updated_at'],
                created_by=str(category['created_by'])
            ))
        
        return CategoryListResponse(
            success=True,
            categories=category_list,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error("خطأ في عرض الأقسام", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في عرض الأقسام")


@router.get("/{category_id}", response_model=CategoryResponse)
async def get_category(
    category_id: str,
    current_admin: Dict[str, Any] = Depends(require_permission("categories.read"))
):
    """الحصول على قسم محدد"""
    try:
        query = categories_table.select().where(categories_table.c.id == category_id)
        category = await database.fetch_one(query)
        
        if not category:
            raise HTTPException(status_code=404, detail="القسم غير موجود")
        
        books_count = await get_category_books_count(category_id)
        
        return CategoryResponse(
            id=str(category['id']),
            name=category['name'],
            name_en=category['name_en'],
            slug=category['slug'],
            description=category['description'],
            icon=category['icon'],
            color=category['color'],
            sort_order=category['sort_order'],
            is_active=category['is_active'],
            books_count=books_count,
            created_at=category['created_at'],
            updated_at=category['updated_at'],
            created_by=str(category['created_by'])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في الحصول على القسم", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على القسم")


@router.post("/", response_model=CategoryResponse)
async def create_category(
    category_data: CategoryCreate,
    current_admin: Dict[str, Any] = Depends(require_permission("categories.create"))
):
    """إنشاء قسم جديد"""
    try:
        # Generate unique slug
        slug = await generate_unique_slug(category_data.name)
        
        # Create category
        category_id = uuid.uuid4()
        insert_query = categories_table.insert().values(
            id=category_id,
            name=category_data.name,
            name_en=category_data.name_en,
            slug=slug,
            description=category_data.description,
            icon=category_data.icon,
            color=category_data.color,
            sort_order=category_data.sort_order,
            is_active=True,
            created_at=datetime.utcnow(),
            created_by=current_admin['admin_id']
        )
        
        await database.execute(insert_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="categories.create",
            resource_type="category",
            resource_id=str(category_id),
            new_values={
                "name": category_data.name,
                "slug": slug,
                "sort_order": category_data.sort_order
            }
        )
        
        logger.info(
            "تم إنشاء قسم جديد",
            category_id=str(category_id),
            name=category_data.name,
            created_by=current_admin['admin_id']
        )
        
        return CategoryResponse(
            id=str(category_id),
            name=category_data.name,
            name_en=category_data.name_en,
            slug=slug,
            description=category_data.description,
            icon=category_data.icon,
            color=category_data.color,
            sort_order=category_data.sort_order,
            is_active=True,
            books_count=0,
            created_at=datetime.utcnow(),
            updated_at=None,
            created_by=current_admin['admin_id']
        )
        
    except Exception as e:
        logger.error("خطأ في إنشاء القسم", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في إنشاء القسم")


@router.put("/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: str,
    category_data: CategoryUpdate,
    current_admin: Dict[str, Any] = Depends(require_permission("categories.update"))
):
    """تحديث قسم"""
    try:
        # Get existing category
        query = categories_table.select().where(categories_table.c.id == category_id)
        existing_category = await database.fetch_one(query)
        
        if not existing_category:
            raise HTTPException(status_code=404, detail="القسم غير موجود")
        
        # Prepare update data
        update_data = {}
        old_values = {}
        new_values = {}
        
        # Check each field for updates
        if category_data.name is not None:
            update_data['name'] = category_data.name
            old_values['name'] = existing_category['name']
            new_values['name'] = category_data.name
            
            # Generate new slug if name changed
            if category_data.name != existing_category['name']:
                new_slug = await generate_unique_slug(category_data.name, category_id)
                update_data['slug'] = new_slug
                old_values['slug'] = existing_category['slug']
                new_values['slug'] = new_slug
        
        if category_data.name_en is not None:
            update_data['name_en'] = category_data.name_en
            old_values['name_en'] = existing_category['name_en']
            new_values['name_en'] = category_data.name_en
        
        if category_data.description is not None:
            update_data['description'] = category_data.description
            old_values['description'] = existing_category['description']
            new_values['description'] = category_data.description
        
        if category_data.icon is not None:
            update_data['icon'] = category_data.icon
            old_values['icon'] = existing_category['icon']
            new_values['icon'] = category_data.icon
        
        if category_data.color is not None:
            update_data['color'] = category_data.color
            old_values['color'] = existing_category['color']
            new_values['color'] = category_data.color
        
        if category_data.sort_order is not None:
            update_data['sort_order'] = category_data.sort_order
            old_values['sort_order'] = existing_category['sort_order']
            new_values['sort_order'] = category_data.sort_order
        
        if category_data.is_active is not None:
            update_data['is_active'] = category_data.is_active
            old_values['is_active'] = existing_category['is_active']
            new_values['is_active'] = category_data.is_active
        
        if not update_data:
            raise HTTPException(status_code=400, detail="لا توجد بيانات للتحديث")
        
        # Add updated timestamp
        update_data['updated_at'] = datetime.utcnow()
        
        # Update category
        update_query = categories_table.update().where(
            categories_table.c.id == category_id
        ).values(**update_data)
        
        await database.execute(update_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="categories.update",
            resource_type="category",
            resource_id=category_id,
            old_values=old_values,
            new_values=new_values
        )
        
        # Get updated category
        updated_category = await database.fetch_one(
            categories_table.select().where(categories_table.c.id == category_id)
        )
        
        books_count = await get_category_books_count(category_id)
        
        logger.info(
            "تم تحديث القسم",
            category_id=category_id,
            updated_by=current_admin['admin_id']
        )
        
        return CategoryResponse(
            id=str(updated_category['id']),
            name=updated_category['name'],
            name_en=updated_category['name_en'],
            slug=updated_category['slug'],
            description=updated_category['description'],
            icon=updated_category['icon'],
            color=updated_category['color'],
            sort_order=updated_category['sort_order'],
            is_active=updated_category['is_active'],
            books_count=books_count,
            created_at=updated_category['created_at'],
            updated_at=updated_category['updated_at'],
            created_by=str(updated_category['created_by'])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في تحديث القسم", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في تحديث القسم")


@router.delete("/{category_id}")
async def delete_category(
    category_id: str,
    force: bool = Query(False, description="حذف إجباري حتى لو كان يحتوي على كتب"),
    current_admin: Dict[str, Any] = Depends(require_permission("categories.delete"))
):
    """حذف قسم"""
    try:
        # Check if category exists
        query = categories_table.select().where(categories_table.c.id == category_id)
        category = await database.fetch_one(query)
        
        if not category:
            raise HTTPException(status_code=404, detail="القسم غير موجود")
        
        # Check if category has books
        books_count = await get_category_books_count(category_id)
        
        if books_count > 0 and not force:
            raise HTTPException(
                status_code=400,
                detail=f"لا يمكن حذف القسم لأنه يحتوي على {books_count} كتاب. استخدم force=true للحذف الإجباري"
            )
        
        # If force delete, deactivate all books in this category
        if force and books_count > 0:
            update_books_query = books_table.update().where(
                books_table.c.category_id == category_id
            ).values(is_active=False, updated_at=datetime.utcnow())
            
            await database.execute(update_books_query)
        
        # Delete category
        delete_query = categories_table.delete().where(categories_table.c.id == category_id)
        await database.execute(delete_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="categories.delete",
            resource_type="category",
            resource_id=category_id,
            old_values={
                "name": category['name'],
                "books_count": books_count,
                "force_delete": force
            }
        )
        
        logger.info(
            "تم حذف القسم",
            category_id=category_id,
            books_affected=books_count if force else 0,
            deleted_by=current_admin['admin_id']
        )
        
        return {
            "success": True,
            "message": f"تم حذف القسم بنجاح" + (f" مع {books_count} كتاب" if force and books_count > 0 else "")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في حذف القسم", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في حذف القسم")


@router.post("/{category_id}/reorder")
async def reorder_category(
    category_id: str,
    new_order: int = Query(..., ge=0, description="الترتيب الجديد"),
    current_admin: Dict[str, Any] = Depends(require_permission("categories.update"))
):
    """إعادة ترتيب القسم"""
    try:
        # Check if category exists
        query = categories_table.select().where(categories_table.c.id == category_id)
        category = await database.fetch_one(query)
        
        if not category:
            raise HTTPException(status_code=404, detail="القسم غير موجود")
        
        old_order = category['sort_order']
        
        # Update sort order
        update_query = categories_table.update().where(
            categories_table.c.id == category_id
        ).values(
            sort_order=new_order,
            updated_at=datetime.utcnow()
        )
        
        await database.execute(update_query)
        
        # Log the action
        await firebase_auth_manager.log_admin_action(
            admin_id=current_admin['admin_id'],
            action="categories.reorder",
            resource_type="category",
            resource_id=category_id,
            old_values={"sort_order": old_order},
            new_values={"sort_order": new_order}
        )
        
        logger.info(
            "تم إعادة ترتيب القسم",
            category_id=category_id,
            old_order=old_order,
            new_order=new_order,
            updated_by=current_admin['admin_id']
        )
        
        return {
            "success": True,
            "message": "تم إعادة ترتيب القسم بنجاح",
            "old_order": old_order,
            "new_order": new_order
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("خطأ في إعادة ترتيب القسم", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في إعادة ترتيب القسم")
