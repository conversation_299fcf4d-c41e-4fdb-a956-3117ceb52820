"""
APEX Monitoring & Analytics Router
نظام المراقبة والإحصائيات المتقدم
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import structlog
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel

from ..auth.firebase_auth import get_current_admin, require_super_admin
from ..auth.permissions import require_permission
from ..database import (
    database, books_table, categories_table, 
    admins_table, audit_log_table, admin_sessions_table
)
from ..utils.file_handler import file_handler

logger = structlog.get_logger(__name__)

router = APIRouter()


# ===== RESPONSE MODELS =====

class SystemStats(BaseModel):
    """إحصائيات النظام العامة"""
    total_books: int
    total_categories: int
    total_admins: int
    active_sessions: int
    total_downloads: int
    total_views: int
    storage_used: int
    last_updated: datetime


class BookStats(BaseModel):
    """إحصائيات الكتب"""
    total_books: int
    active_books: int
    featured_books: int
    popular_books: int
    books_with_pdf: int
    books_with_cover: int
    avg_rating: float
    top_categories: List[Dict[str, Any]]
    recent_books: List[Dict[str, Any]]


class AdminActivity(BaseModel):
    """نشاط المديرين"""
    admin_id: str
    admin_name: str
    last_login: Optional[datetime]
    actions_count: int
    recent_actions: List[Dict[str, Any]]


class AuditLogEntry(BaseModel):
    """سجل العمليات"""
    id: str
    admin_name: str
    action: str
    resource_type: str
    resource_id: Optional[str]
    success: bool
    created_at: datetime
    ip_address: Optional[str]


# ===== MONITORING ENDPOINTS =====

@router.get("/stats/system", response_model=SystemStats)
async def get_system_stats(
    current_admin: Dict[str, Any] = Depends(require_permission("system.read"))
):
    """إحصائيات النظام العامة"""
    try:
        # Count books
        books_query = books_table.select().with_only_columns([books_table.c.id])
        total_books = len(await database.fetch_all(books_query))
        
        # Count categories
        categories_query = categories_table.select().with_only_columns([categories_table.c.id])
        total_categories = len(await database.fetch_all(categories_query))
        
        # Count admins
        admins_query = admins_table.select().where(
            admins_table.c.is_active == True
        ).with_only_columns([admins_table.c.id])
        total_admins = len(await database.fetch_all(admins_query))
        
        # Count active sessions
        sessions_query = admin_sessions_table.select().where(
            (admin_sessions_table.c.is_active == True) &
            (admin_sessions_table.c.expires_at > datetime.utcnow())
        ).with_only_columns([admin_sessions_table.c.id])
        active_sessions = len(await database.fetch_all(sessions_query))
        
        # Calculate total downloads and views
        downloads_query = books_table.select().with_only_columns([books_table.c.download_count])
        downloads_result = await database.fetch_all(downloads_query)
        total_downloads = sum(row['download_count'] for row in downloads_result)
        
        views_query = books_table.select().with_only_columns([books_table.c.view_count])
        views_result = await database.fetch_all(views_query)
        total_views = sum(row['view_count'] for row in views_result)
        
        # Get storage stats
        storage_stats = await file_handler.get_storage_stats()
        storage_used = storage_stats.get("total_size", 0)
        
        return SystemStats(
            total_books=total_books,
            total_categories=total_categories,
            total_admins=total_admins,
            active_sessions=active_sessions,
            total_downloads=total_downloads,
            total_views=total_views,
            storage_used=storage_used,
            last_updated=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("خطأ في الحصول على إحصائيات النظام", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على إحصائيات النظام")


@router.get("/stats/books", response_model=BookStats)
async def get_books_stats(
    current_admin: Dict[str, Any] = Depends(require_permission("books.read"))
):
    """إحصائيات الكتب المفصلة"""
    try:
        # Basic book counts
        all_books = await database.fetch_all(books_table.select())
        
        total_books = len(all_books)
        active_books = len([b for b in all_books if b['is_active']])
        featured_books = len([b for b in all_books if b['is_featured']])
        popular_books = len([b for b in all_books if b['is_popular']])
        books_with_pdf = len([b for b in all_books if b['pdf_file']])
        books_with_cover = len([b for b in all_books if b['cover_image']])
        
        # Calculate average rating
        ratings = [b['rating'] for b in all_books if b['rating'] > 0]
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        
        # Top categories by book count
        category_counts = {}
        for book in all_books:
            if book['is_active']:
                cat_id = str(book['category_id'])
                category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
        
        # Get category names
        categories = await database.fetch_all(categories_table.select())
        category_map = {str(c['id']): c['name'] for c in categories}
        
        top_categories = [
            {
                "category_id": cat_id,
                "category_name": category_map.get(cat_id, "غير محدد"),
                "books_count": count
            }
            for cat_id, count in sorted(
                category_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        ]
        
        # Recent books
        recent_books_query = books_table.select().where(
            books_table.c.is_active == True
        ).order_by(books_table.c.created_at.desc()).limit(5)
        
        recent_books_data = await database.fetch_all(recent_books_query)
        recent_books = [
            {
                "id": str(book['id']),
                "title": book['title'],
                "author": book['author'],
                "created_at": book['created_at'],
                "rating": book['rating']
            }
            for book in recent_books_data
        ]
        
        return BookStats(
            total_books=total_books,
            active_books=active_books,
            featured_books=featured_books,
            popular_books=popular_books,
            books_with_pdf=books_with_pdf,
            books_with_cover=books_with_cover,
            avg_rating=round(avg_rating, 2),
            top_categories=top_categories,
            recent_books=recent_books
        )
        
    except Exception as e:
        logger.error("خطأ في الحصول على إحصائيات الكتب", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على إحصائيات الكتب")


@router.get("/activity/admins")
async def get_admin_activity(
    days: int = Query(7, ge=1, le=30, description="عدد الأيام"),
    current_admin: Dict[str, Any] = Depends(require_super_admin)
):
    """نشاط المديرين"""
    try:
        since_date = datetime.utcnow() - timedelta(days=days)
        
        # Get all admins
        admins = await database.fetch_all(
            admins_table.select().where(admins_table.c.is_active == True)
        )
        
        admin_activities = []
        
        for admin in admins:
            admin_id = str(admin['id'])
            
            # Count actions in the period
            actions_query = audit_log_table.select().where(
                (audit_log_table.c.admin_id == admin_id) &
                (audit_log_table.c.created_at >= since_date)
            )
            actions = await database.fetch_all(actions_query)
            
            # Get recent actions
            recent_actions_query = audit_log_table.select().where(
                audit_log_table.c.admin_id == admin_id
            ).order_by(audit_log_table.c.created_at.desc()).limit(5)
            
            recent_actions_data = await database.fetch_all(recent_actions_query)
            recent_actions = [
                {
                    "action": action['action'],
                    "resource_type": action['resource_type'],
                    "success": action['success'],
                    "created_at": action['created_at']
                }
                for action in recent_actions_data
            ]
            
            admin_activities.append(AdminActivity(
                admin_id=admin_id,
                admin_name=admin['display_name'] or admin['email'],
                last_login=admin['last_login'],
                actions_count=len(actions),
                recent_actions=recent_actions
            ))
        
        # Sort by activity
        admin_activities.sort(key=lambda x: x.actions_count, reverse=True)
        
        return {
            "success": True,
            "period_days": days,
            "admin_activities": admin_activities
        }
        
    except Exception as e:
        logger.error("خطأ في الحصول على نشاط المديرين", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على نشاط المديرين")


@router.get("/audit-log")
async def get_audit_log(
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(50, ge=1, le=200, description="عدد العناصر في الصفحة"),
    action: Optional[str] = Query(None, description="تصفية حسب العملية"),
    resource_type: Optional[str] = Query(None, description="تصفية حسب نوع المورد"),
    admin_id: Optional[str] = Query(None, description="تصفية حسب المدير"),
    success_only: Optional[bool] = Query(None, description="العمليات الناجحة فقط"),
    days: int = Query(7, ge=1, le=90, description="عدد الأيام"),
    current_admin: Dict[str, Any] = Depends(require_permission("audit.read"))
):
    """سجل العمليات مع التصفية"""
    try:
        since_date = datetime.utcnow() - timedelta(days=days)
        
        # Build query
        query = audit_log_table.select().where(
            audit_log_table.c.created_at >= since_date
        )
        
        # Apply filters
        if action:
            query = query.where(audit_log_table.c.action == action)
        
        if resource_type:
            query = query.where(audit_log_table.c.resource_type == resource_type)
        
        if admin_id:
            query = query.where(audit_log_table.c.admin_id == admin_id)
        
        if success_only is not None:
            query = query.where(audit_log_table.c.success == success_only)
        
        # Count total
        count_query = query.with_only_columns([audit_log_table.c.id])
        total_entries = len(await database.fetch_all(count_query))
        
        # Apply pagination
        offset = (page - 1) * per_page
        query = query.order_by(
            audit_log_table.c.created_at.desc()
        ).limit(per_page).offset(offset)
        
        entries = await database.fetch_all(query)
        
        # Get admin names
        admin_ids = list(set(str(entry['admin_id']) for entry in entries if entry['admin_id']))
        admin_names = {}
        
        if admin_ids:
            admins_query = admins_table.select().where(
                admins_table.c.id.in_(admin_ids)
            )
            admins = await database.fetch_all(admins_query)
            admin_names = {
                str(admin['id']): admin['display_name'] or admin['email']
                for admin in admins
            }
        
        # Build response
        audit_entries = []
        for entry in entries:
            admin_name = "نظام"
            if entry['admin_id']:
                admin_name = admin_names.get(str(entry['admin_id']), "غير معروف")
            
            audit_entries.append(AuditLogEntry(
                id=str(entry['id']),
                admin_name=admin_name,
                action=entry['action'],
                resource_type=entry['resource_type'],
                resource_id=entry['resource_id'],
                success=entry['success'],
                created_at=entry['created_at'],
                ip_address=entry['ip_address']
            ))
        
        return {
            "success": True,
            "entries": audit_entries,
            "total": total_entries,
            "page": page,
            "per_page": per_page,
            "period_days": days
        }
        
    except Exception as e:
        logger.error("خطأ في الحصول على سجل العمليات", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على سجل العمليات")


@router.get("/storage")
async def get_storage_info(
    current_admin: Dict[str, Any] = Depends(require_permission("system.read"))
):
    """معلومات التخزين"""
    try:
        storage_stats = await file_handler.get_storage_stats()
        
        return {
            "success": True,
            "storage_stats": storage_stats,
            "formatted_sizes": {
                "total_size": f"{storage_stats.get('total_size', 0) / (1024*1024):.2f} MB",
                "books_size": f"{storage_stats.get('books_size', 0) / (1024*1024):.2f} MB",
                "covers_size": f"{storage_stats.get('covers_size', 0) / (1024*1024):.2f} MB"
            }
        }
        
    except Exception as e:
        logger.error("خطأ في الحصول على معلومات التخزين", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في الحصول على معلومات التخزين")


@router.post("/cleanup/temp-files")
async def cleanup_temp_files(
    max_age_hours: int = Query(24, ge=1, le=168, description="عمر الملفات بالساعات"),
    current_admin: Dict[str, Any] = Depends(require_super_admin)
):
    """تنظيف الملفات المؤقتة"""
    try:
        deleted_count = await file_handler.cleanup_temp_files(max_age_hours)
        
        return {
            "success": True,
            "message": f"تم حذف {deleted_count} ملف مؤقت",
            "deleted_files": deleted_count
        }
        
    except Exception as e:
        logger.error("خطأ في تنظيف الملفات المؤقتة", error=str(e))
        raise HTTPException(status_code=500, detail="خطأ في تنظيف الملفات المؤقتة")
