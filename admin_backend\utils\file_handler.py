"""
APEX File Handler System
نظام إدارة الملفات المتقدم مع رفع آمن ومعالجة الصور
"""

import os
import uuid
import hashlib
import mimetypes
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
from pathlib import Path

import structlog
import aiofiles
from PIL import Image, ImageOps
from fastapi import HTTPException, UploadFile

from ..config import settings

logger = structlog.get_logger(__name__)


class FileHandler:
    """معالج الملفات المتقدم"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.max_file_size = settings.MAX_FILE_SIZE
        self.allowed_types = settings.ALLOWED_FILE_TYPES
        
        # Create upload directories
        self.books_dir = self.upload_dir / "books"
        self.covers_dir = self.upload_dir / "covers"
        self.temp_dir = self.upload_dir / "temp"
        
        # Ensure directories exist
        for directory in [self.books_dir, self.covers_dir, self.temp_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    async def validate_file(self, file: UploadFile) -> Dict[str, Any]:
        """التحقق من صحة الملف"""
        try:
            # Check file size
            if file.size and file.size > self.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"حجم الملف كبير جداً. الحد الأقصى: {self.max_file_size / (1024*1024):.1f} MB"
                )
            
            # Get file extension
            file_ext = Path(file.filename).suffix.lower().lstrip('.')
            
            # Check file type
            if file_ext not in self.allowed_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"نوع الملف غير مدعوم. الأنواع المدعومة: {', '.join(self.allowed_types)}"
                )
            
            # Detect MIME type
            mime_type, _ = mimetypes.guess_type(file.filename)
            
            # Additional security checks
            if not mime_type:
                raise HTTPException(
                    status_code=400,
                    detail="لا يمكن تحديد نوع الملف"
                )
            
            # Check for malicious files
            dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
            if any(file.filename.lower().endswith(ext) for ext in dangerous_extensions):
                raise HTTPException(
                    status_code=400,
                    detail="نوع ملف خطير غير مسموح"
                )
            
            return {
                "filename": file.filename,
                "size": file.size,
                "extension": file_ext,
                "mime_type": mime_type,
                "is_image": mime_type.startswith('image/'),
                "is_pdf": mime_type == 'application/pdf'
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("خطأ في التحقق من الملف", error=str(e))
            raise HTTPException(status_code=500, detail="خطأ في التحقق من الملف")
    
    async def generate_secure_filename(self, original_filename: str, prefix: str = "") -> str:
        """إنشاء اسم ملف آمن وفريد"""
        try:
            # Get file extension
            file_ext = Path(original_filename).suffix.lower()
            
            # Generate unique ID
            unique_id = str(uuid.uuid4())
            
            # Create timestamp
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            
            # Combine parts
            if prefix:
                filename = f"{prefix}_{timestamp}_{unique_id}{file_ext}"
            else:
                filename = f"{timestamp}_{unique_id}{file_ext}"
            
            return filename
            
        except Exception as e:
            logger.error("خطأ في إنشاء اسم الملف", error=str(e))
            raise HTTPException(status_code=500, detail="خطأ في إنشاء اسم الملف")
    
    async def calculate_file_hash(self, file_path: Path) -> str:
        """حساب hash للملف للتحقق من التكرار"""
        try:
            hash_sha256 = hashlib.sha256()
            
            async with aiofiles.open(file_path, 'rb') as f:
                while chunk := await f.read(8192):
                    hash_sha256.update(chunk)
            
            return hash_sha256.hexdigest()
            
        except Exception as e:
            logger.error("خطأ في حساب hash الملف", error=str(e))
            return ""
    
    async def save_file(self, file: UploadFile, file_type: str = "general") -> Dict[str, Any]:
        """حفظ الملف مع معالجة متقدمة"""
        try:
            # Validate file
            file_info = await self.validate_file(file)
            
            # Determine target directory
            if file_type == "cover" or file_info["is_image"]:
                target_dir = self.covers_dir
                prefix = "cover"
            elif file_type == "book" or file_info["is_pdf"]:
                target_dir = self.books_dir
                prefix = "book"
            else:
                target_dir = self.upload_dir
                prefix = "file"
            
            # Generate secure filename
            secure_filename = await self.generate_secure_filename(
                file.filename, prefix
            )
            
            file_path = target_dir / secure_filename
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            # Calculate file hash
            file_hash = await self.calculate_file_hash(file_path)
            
            # Get actual file size
            actual_size = file_path.stat().st_size
            
            # Process image if it's a cover
            thumbnail_path = None
            if file_info["is_image"] and file_type == "cover":
                thumbnail_path = await self.create_thumbnail(file_path)
            
            # Extract PDF metadata if it's a PDF
            pdf_metadata = {}
            if file_info["is_pdf"]:
                pdf_metadata = await self.extract_pdf_metadata(file_path)
            
            result = {
                "filename": secure_filename,
                "original_filename": file.filename,
                "file_path": str(file_path),
                "relative_path": str(file_path.relative_to(self.upload_dir)),
                "file_size": actual_size,
                "file_hash": file_hash,
                "mime_type": file_info["mime_type"],
                "extension": file_info["extension"],
                "upload_date": datetime.utcnow(),
                "thumbnail_path": thumbnail_path,
                "metadata": pdf_metadata
            }
            
            logger.info(
                "تم حفظ الملف بنجاح",
                filename=secure_filename,
                size=actual_size,
                type=file_type
            )
            
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("خطأ في حفظ الملف", error=str(e))
            raise HTTPException(status_code=500, detail="خطأ في حفظ الملف")
    
    async def create_thumbnail(self, image_path: Path, size: Tuple[int, int] = (300, 400)) -> Optional[str]:
        """إنشاء صورة مصغرة"""
        try:
            # Generate thumbnail filename
            thumb_filename = f"thumb_{image_path.stem}.webp"
            thumb_path = self.covers_dir / "thumbnails"
            thumb_path.mkdir(exist_ok=True)
            full_thumb_path = thumb_path / thumb_filename
            
            # Open and process image
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Auto-orient based on EXIF data
                img = ImageOps.exif_transpose(img)
                
                # Create thumbnail maintaining aspect ratio
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # Save as WebP for better compression
                img.save(full_thumb_path, 'WEBP', quality=85, optimize=True)
            
            return str(full_thumb_path.relative_to(self.upload_dir))
            
        except Exception as e:
            logger.error("خطأ في إنشاء الصورة المصغرة", error=str(e))
            return None
    
    async def extract_pdf_metadata(self, pdf_path: Path) -> Dict[str, Any]:
        """استخراج معلومات PDF"""
        try:
            # This would require PyPDF2 or similar library
            # For now, return basic info
            file_size = pdf_path.stat().st_size
            
            return {
                "file_size": file_size,
                "pages": None,  # Would extract with PyPDF2
                "title": None,
                "author": None,
                "creation_date": None
            }
            
        except Exception as e:
            logger.error("خطأ في استخراج معلومات PDF", error=str(e))
            return {}
    
    async def delete_file(self, file_path: str) -> bool:
        """حذف ملف"""
        try:
            full_path = self.upload_dir / file_path
            
            if full_path.exists():
                full_path.unlink()
                
                # Delete thumbnail if exists
                if "covers/" in file_path:
                    thumb_path = self.covers_dir / "thumbnails" / f"thumb_{full_path.stem}.webp"
                    if thumb_path.exists():
                        thumb_path.unlink()
                
                logger.info("تم حذف الملف", file_path=file_path)
                return True
            
            return False
            
        except Exception as e:
            logger.error("خطأ في حذف الملف", error=str(e))
            return False
    
    async def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الملف"""
        try:
            full_path = self.upload_dir / file_path
            
            if not full_path.exists():
                return None
            
            stat = full_path.stat()
            
            return {
                "filename": full_path.name,
                "file_path": str(full_path),
                "relative_path": file_path,
                "file_size": stat.st_size,
                "created_date": datetime.fromtimestamp(stat.st_ctime),
                "modified_date": datetime.fromtimestamp(stat.st_mtime),
                "extension": full_path.suffix.lower().lstrip('.'),
                "exists": True
            }
            
        except Exception as e:
            logger.error("خطأ في الحصول على معلومات الملف", error=str(e))
            return None
    
    async def cleanup_temp_files(self, max_age_hours: int = 24):
        """تنظيف الملفات المؤقتة"""
        try:
            current_time = datetime.utcnow()
            deleted_count = 0
            
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - datetime.fromtimestamp(file_path.stat().st_ctime)
                    
                    if file_age.total_seconds() > (max_age_hours * 3600):
                        file_path.unlink()
                        deleted_count += 1
            
            if deleted_count > 0:
                logger.info(f"تم حذف {deleted_count} ملف مؤقت")
            
            return deleted_count
            
        except Exception as e:
            logger.error("خطأ في تنظيف الملفات المؤقتة", error=str(e))
            return 0
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """إحصائيات التخزين"""
        try:
            stats = {
                "total_files": 0,
                "total_size": 0,
                "books_count": 0,
                "books_size": 0,
                "covers_count": 0,
                "covers_size": 0
            }
            
            # Count books
            for file_path in self.books_dir.rglob("*"):
                if file_path.is_file():
                    stats["books_count"] += 1
                    stats["books_size"] += file_path.stat().st_size
            
            # Count covers
            for file_path in self.covers_dir.rglob("*"):
                if file_path.is_file():
                    stats["covers_count"] += 1
                    stats["covers_size"] += file_path.stat().st_size
            
            stats["total_files"] = stats["books_count"] + stats["covers_count"]
            stats["total_size"] = stats["books_size"] + stats["covers_size"]
            
            return stats
            
        except Exception as e:
            logger.error("خطأ في حساب إحصائيات التخزين", error=str(e))
            return {}


# Global file handler instance
file_handler = FileHandler()
