<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - فتح الكتب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #ffda37;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .test-book {
            width: 280px;
            height: 400px;
            margin: 20px auto;
            position: relative;
            perspective: 2000px;
            cursor: pointer;
            transition: all 0.4s ease;
        }
        .book-container {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
        }
        .book-pages-container {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
        }
        .book-page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 8px;
            transform-origin: left center;
            transition: transform 0.6s ease;
            backface-visibility: hidden;
        }
        .book-cover-page {
            background: linear-gradient(135deg, #654321 0%, #8B4513 50%, #A0522D 100%);
            z-index: 4;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        .book-page-1, .book-page-2 {
            background: #f8f8f8;
            z-index: 3;
            display: none;
            padding: 20px;
            color: #333;
        }
        .book-title {
            color: #ffda37;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .book-author {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            margin-bottom: 15px;
        }
        .cover-click-area {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 218, 55, 0.1);
            border: 1px dashed rgba(255, 218, 55, 0.3);
        }
        .cover-hint {
            background: rgba(255, 218, 55, 0.9);
            color: #000;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        /* Book States */
        .test-book.cover-mode .book-page-1,
        .test-book.cover-mode .book-page-2 {
            display: none;
        }
        .test-book.internal-mode .book-cover-page {
            display: none;
        }
        .test-book.internal-mode .book-page-1,
        .test-book.internal-mode .book-page-2 {
            display: block;
        }
        .test-book.page-1-open .book-page-1 {
            transform: rotateY(-175deg) translateZ(2px);
            z-index: 1;
        }
        .test-book.page-2-open .book-page-1 {
            transform: rotateY(-175deg) translateZ(2px);
            z-index: 1;
        }
        .test-book.page-2-open .book-page-2 {
            transform: rotateY(-175deg) translateZ(4px);
            z-index: 2;
        }
        .page-content {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .page-title {
            color: #ffda37;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        .btn {
            background: #ffda37;
            color: #000;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #e6c533;
            transform: scale(1.05);
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid #ffda37;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .warning { color: #ffda37; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📖 اختبار فتح الكتب النهائي</h1>
            <p>اختبار مبسط لوظيفة فتح الكتب مثل الكتاب الحقيقي</p>
        </div>

        <div id="status" class="status warning">
            ⏳ جاري تحميل الاختبار...
        </div>

        <!-- Test Book -->
        <div class="test-book cover-mode" id="testBook" data-book-id="1">
            <div class="book-container">
                <div class="book-pages-container">
                    <!-- Cover Page -->
                    <div class="book-page book-cover-page">
                        <div class="book-title">أطلس نيتر للتشريح البشري</div>
                        <div class="book-author">د. فرانك نيتر</div>
                        <div class="cover-click-area" onclick="openTestBook()">
                            <div class="cover-hint">انقر لفتح الكتاب</div>
                        </div>
                    </div>

                    <!-- Page 1 -->
                    <div class="book-page book-page-1">
                        <div class="page-content">
                            <div class="page-header">
                                <button class="btn" onclick="backToCover()">🔙 العودة للغلاف</button>
                                <div class="page-title">تفاصيل الكتاب</div>
                            </div>
                            <div style="flex: 1;">
                                <h2>أطلس نيتر للتشريح البشري</h2>
                                <p><strong>المؤلف:</strong> د. فرانك نيتر</p>
                                <p><strong>الوصف:</strong> المرجع الأشهر في التشريح البشري مع رسوم توضيحية مفصلة</p>
                                <p><strong>التقييم:</strong> ⭐ 4.9</p>
                            </div>
                            <div>
                                <button class="btn" onclick="nextPage()" style="width: 100%;">
                                    الصفحة التالية ➡️
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Page 2 -->
                    <div class="book-page book-page-2">
                        <div class="page-content">
                            <div class="page-header">
                                <button class="btn" onclick="prevPage()">⬅️ السابقة</button>
                                <div class="page-title">الإجراءات</div>
                            </div>
                            <div style="flex: 1;">
                                <button class="btn" onclick="readBook()" style="width: 100%; margin: 10px 0;">
                                    📚 قراءة مباشرة
                                </button>
                                <button class="btn" onclick="downloadBook()" style="width: 100%; margin: 10px 0;">
                                    ⬇️ تحميل الكتاب
                                </button>
                                <button class="btn" onclick="bookmarkBook()" style="width: 100%; margin: 10px 0;">
                                    🔖 إضافة للمفضلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 'cover';
        const testBook = document.getElementById('testBook');
        const status = document.getElementById('status');

        function updateStatus(message, type = 'warning') {
            status.innerHTML = message;
            status.className = `status ${type}`;
        }

        function openTestBook() {
            console.log('📖 فتح الكتاب');
            updateStatus('📖 تم فتح الكتاب - الصفحة 1', 'success');
            
            testBook.classList.remove('cover-mode');
            testBook.classList.add('internal-mode', 'page-1-open');
            currentPage = 'page1';
        }

        function backToCover() {
            console.log('🔙 العودة للغلاف');
            updateStatus('📕 تم إغلاق الكتاب', 'warning');
            
            testBook.classList.remove('internal-mode', 'page-1-open', 'page-2-open');
            testBook.classList.add('cover-mode');
            currentPage = 'cover';
        }

        function nextPage() {
            if (currentPage === 'page1') {
                console.log('➡️ الانتقال للصفحة 2');
                updateStatus('📄 الصفحة 2 - الإجراءات', 'success');
                
                testBook.classList.remove('page-1-open');
                testBook.classList.add('page-2-open');
                currentPage = 'page2';
            }
        }

        function prevPage() {
            if (currentPage === 'page2') {
                console.log('⬅️ العودة للصفحة 1');
                updateStatus('📄 الصفحة 1 - التفاصيل', 'success');
                
                testBook.classList.remove('page-2-open');
                testBook.classList.add('page-1-open');
                currentPage = 'page1';
            }
        }

        function readBook() {
            updateStatus('📚 بدء القراءة...', 'success');
            alert('🎉 سيتم فتح الكتاب للقراءة!');
        }

        function downloadBook() {
            updateStatus('⬇️ بدء التحميل...', 'success');
            alert('📥 سيبدأ تحميل الكتاب قريباً!');
        }

        function bookmarkBook() {
            updateStatus('🔖 تم إضافة الكتاب للمفضلة!', 'success');
            alert('⭐ تم حفظ الكتاب في المفضلة!');
        }

        // Close book when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.test-book') && currentPage !== 'cover') {
                backToCover();
            }
        });

        // Initial status
        updateStatus('✅ الاختبار جاهز - انقر على الكتاب لفتحه', 'success');
    </script>
</body>
</html>
