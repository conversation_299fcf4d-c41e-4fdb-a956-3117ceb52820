<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص نهائي - المكتبة الطبية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #ffda37;
        }
        .test-title {
            color: #ffda37;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: rgba(0, 255, 0, 0.2);
            color: #51cf66;
            border: 1px solid #51cf66;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid #ff6b6b;
        }
        .warning {
            background: rgba(255, 218, 55, 0.2);
            color: #ffda37;
            border: 1px solid #ffda37;
        }
        .books-test-grid {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            min-height: 300px;
        }
        .test-book {
            width: 200px;
            height: 280px;
            background: rgba(255, 218, 55, 0.1);
            border: 1px solid #ffda37;
            border-radius: 8px;
            padding: 10px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            text-align: center;
        }
        .test-book img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            background: #333;
        }
        .test-book h3 {
            color: #ffda37;
            margin: 10px 0 5px 0;
            font-size: 14px;
        }
        .test-book p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #ffda37; margin-bottom: 30px;">
            🔍 فحص نهائي - المكتبة الطبية الإلكترونية
        </h1>

        <!-- Test 1: JavaScript Loading -->
        <div class="test-section">
            <div class="test-title">1. اختبار تحميل JavaScript</div>
            <div id="js-test" class="test-result error">❌ لم يتم تحميل JavaScript</div>
        </div>

        <!-- Test 2: Books Data -->
        <div class="test-section">
            <div class="test-title">2. اختبار بيانات الكتب</div>
            <div id="data-test" class="test-result error">❌ لم يتم تحميل بيانات الكتب</div>
        </div>

        <!-- Test 3: DOM Elements -->
        <div class="test-section">
            <div class="test-title">3. اختبار عناصر DOM</div>
            <div id="dom-test" class="test-result error">❌ لم يتم العثور على العناصر</div>
        </div>

        <!-- Test 4: CSS Visibility -->
        <div class="test-section">
            <div class="test-title">4. اختبار رؤية CSS</div>
            <div id="css-test" class="test-result error">❌ لم يتم فحص CSS</div>
        </div>

        <!-- Test 5: Books Rendering -->
        <div class="test-section">
            <div class="test-title">5. اختبار عرض الكتب</div>
            <div id="render-test" class="test-result error">❌ لم يتم عرض الكتب</div>
            <div class="books-test-grid" id="test-books-container">
                <div style="padding: 20px; color: #ffda37;">⏳ جاري التحميل...</div>
            </div>
        </div>

        <!-- Test 6: Original Site Check -->
        <div class="test-section">
            <div class="test-title">6. فحص الموقع الأصلي</div>
            <div id="original-test" class="test-result warning">⏳ جاري الفحص...</div>
            <button onclick="checkOriginalSite()" style="
                background: #ffda37; 
                color: #1a1a2e; 
                border: none; 
                padding: 10px 20px; 
                border-radius: 5px; 
                cursor: pointer; 
                margin-top: 10px;
            ">
                فحص الموقع الأصلي
            </button>
        </div>
    </div>

    <script>
        console.log('🚀 بدء الفحص النهائي...');

        // Test 1: JavaScript Loading
        document.getElementById('js-test').innerHTML = '✅ تم تحميل JavaScript بنجاح';
        document.getElementById('js-test').className = 'test-result success';

        // Test Books Data
        const testBooks = [
            {
                id: 1,
                title: "كتاب اختبار 1",
                author: "مؤلف تجريبي",
                cover: "https://picsum.photos/200/150?random=1"
            },
            {
                id: 2,
                title: "كتاب اختبار 2", 
                author: "مؤلف تجريبي 2",
                cover: "https://picsum.photos/200/150?random=2"
            },
            {
                id: 3,
                title: "كتاب اختبار 3",
                author: "مؤلف تجريبي 3", 
                cover: "https://picsum.photos/200/150?random=3"
            }
        ];

        // Test 2: Books Data
        if (testBooks && testBooks.length > 0) {
            document.getElementById('data-test').innerHTML = `✅ تم تحميل ${testBooks.length} كتاب تجريبي`;
            document.getElementById('data-test').className = 'test-result success';
        }

        // Test 3: DOM Elements
        setTimeout(() => {
            const featuredContainer = document.getElementById('featured-books');
            const popularContainer = document.getElementById('popular-books');
            const recentContainer = document.getElementById('recent-books');
            
            let domResults = [];
            if (featuredContainer) domResults.push('✅ featured-books');
            else domResults.push('❌ featured-books');
            
            if (popularContainer) domResults.push('✅ popular-books');
            else domResults.push('❌ popular-books');
            
            if (recentContainer) domResults.push('✅ recent-books');
            else domResults.push('❌ recent-books');
            
            document.getElementById('dom-test').innerHTML = domResults.join('<br>');
            document.getElementById('dom-test').className = domResults.every(r => r.includes('✅')) ? 'test-result success' : 'test-result warning';
        }, 500);

        // Test 4: CSS Visibility
        setTimeout(() => {
            const testElement = document.createElement('div');
            testElement.className = 'books-grid';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);
            
            const styles = window.getComputedStyle(testElement);
            const display = styles.display;
            const opacity = styles.opacity;
            const visibility = styles.visibility;
            
            document.body.removeChild(testElement);
            
            const cssResults = [
                `Display: ${display}`,
                `Opacity: ${opacity}`,
                `Visibility: ${visibility}`
            ];
            
            document.getElementById('css-test').innerHTML = '✅ CSS: ' + cssResults.join(', ');
            document.getElementById('css-test').className = 'test-result success';
        }, 1000);

        // Test 5: Books Rendering
        function renderTestBooks() {
            const container = document.getElementById('test-books-container');
            container.innerHTML = '';
            
            testBooks.forEach((book, index) => {
                const bookElement = document.createElement('div');
                bookElement.className = 'test-book';
                bookElement.innerHTML = `
                    <img src="${book.cover}" alt="${book.title}" 
                         onerror="this.style.background='#666'; this.alt='صورة غير متوفرة';">
                    <h3>${book.title}</h3>
                    <p>${book.author}</p>
                `;
                container.appendChild(bookElement);
            });
            
            document.getElementById('render-test').innerHTML = `✅ تم عرض ${testBooks.length} كتاب تجريبي بنجاح`;
            document.getElementById('render-test').className = 'test-result success';
        }

        setTimeout(renderTestBooks, 1500);

        // Test 6: Original Site Check
        function checkOriginalSite() {
            const iframe = document.createElement('iframe');
            iframe.src = 'index.html';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            setTimeout(() => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const featuredBooks = iframeDoc.getElementById('featured-books');
                    const hasBooks = featuredBooks && featuredBooks.children.length > 0;
                    
                    if (hasBooks) {
                        document.getElementById('original-test').innerHTML = `✅ الموقع الأصلي يعمل - ${featuredBooks.children.length} كتاب معروض`;
                        document.getElementById('original-test').className = 'test-result success';
                    } else {
                        document.getElementById('original-test').innerHTML = '⚠️ الموقع الأصلي لا يعرض كتب';
                        document.getElementById('original-test').className = 'test-result warning';
                    }
                } catch (error) {
                    document.getElementById('original-test').innerHTML = '❌ خطأ في فحص الموقع الأصلي: ' + error.message;
                    document.getElementById('original-test').className = 'test-result error';
                }
                
                document.body.removeChild(iframe);
            }, 3000);
        }

        console.log('✅ انتهى الفحص النهائي');
    </script>
</body>
</html>
