<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - المكتبة الطبية</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .debug-panel {
            position: fixed;
            top: 100px;
            right: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffda37;
            border-radius: 10px;
            padding: 15px;
            z-index: 9999;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-panel h3 {
            color: #ffda37;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        .debug-log {
            color: #fff;
            margin: 5px 0;
        }
        .debug-error {
            color: #ff6b6b;
            margin: 5px 0;
        }
        .debug-success {
            color: #51cf66;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-panel">
        <h3>🔍 لوحة التشخيص</h3>
        <div id="debug-output">⏳ جاري التحميل...</div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="#" class="logo">
                <i class="fas fa-book-medical"></i>
                المكتبة الطبية الإلكترونية
            </a>
            <nav class="nav-menu">
                <a href="#home">الرئيسية</a>
                <a href="#featured">المميزة</a>
                <a href="#popular">الشائعة</a>
                <a href="#recent">الحديثة</a>
                <a href="#contact">اتصل بنا</a>
            </nav>
            <div class="search-container">
                <input type="text" class="search-input" placeholder="ابحث عن كتاب...">
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section" id="home">
            <div class="hero-content">
                <h1>مكتبتك الطبية الشاملة</h1>
                <p>اكتشف أحدث الكتب والمراجع الطبية من أفضل المؤلفين والناشرين في العالم</p>
            </div>
        </section>

        <!-- Categories -->
        <section class="section">
            <div class="categories">
                <button class="category-btn active" data-category="all">جميع الكتب</button>
                <button class="category-btn" data-category="anatomy">التشريح</button>
                <button class="category-btn" data-category="physiology">الفسيولوجي</button>
                <button class="category-btn" data-category="pathology">الأمراض</button>
                <button class="category-btn" data-category="pharmacology">الأدوية</button>
                <button class="category-btn" data-category="surgery">الجراحة</button>
            </div>
        </section>

        <!-- Featured Books -->
        <section class="section" id="featured">
            <div class="section-header">
                <h2 class="section-title">الكتب المميزة</h2>
                <a href="#" class="view-all-btn">عرض الكل</a>
            </div>
            <div class="books-grid-container">
                <button class="books-nav-arrow prev" onclick="scrollBooks('featured-books', -1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="featured-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
                <button class="books-nav-arrow next" onclick="scrollBooks('featured-books', 1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

        <!-- Popular Books -->
        <section class="section" id="popular">
            <div class="section-header">
                <h2 class="section-title">الكتب الشائعة</h2>
                <a href="#" class="view-all-btn">عرض الكل</a>
            </div>
            <div class="books-grid-container">
                <button class="books-nav-arrow prev" onclick="scrollBooks('popular-books', -1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="popular-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
                <button class="books-nav-arrow next" onclick="scrollBooks('popular-books', 1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

        <!-- Recent Books -->
        <section class="section" id="recent">
            <div class="section-header">
                <h2 class="section-title">الكتب الحديثة</h2>
                <a href="#" class="view-all-btn">عرض الكل</a>
            </div>
            <div class="books-grid-container">
                <button class="books-nav-arrow prev" onclick="scrollBooks('recent-books', -1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="recent-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
                <button class="books-nav-arrow next" onclick="scrollBooks('recent-books', 1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>
    </main>

    <!-- Book Modal -->
    <div id="book-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <div class="modal-body">
                <!-- Book details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="script.js"></script>
    
    <!-- Debug Script -->
    <script>
        let debugOutput = document.getElementById('debug-output');
        let debugMessages = [];
        
        function addDebugMessage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'debug-error' : type === 'success' ? 'debug-success' : 'debug-log';
            debugMessages.push(`<div class="${className}">[${timestamp}] ${message}</div>`);
            
            if (debugMessages.length > 20) {
                debugMessages.shift();
            }
            
            if (debugOutput) {
                debugOutput.innerHTML = debugMessages.join('');
                debugOutput.scrollTop = debugOutput.scrollHeight;
            }
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            addDebugMessage(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addDebugMessage(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        // Monitor script loading
        addDebugMessage('🚀 بدء تحميل الصفحة', 'log');
        
        // Check if script.js loaded
        setTimeout(function() {
            if (typeof booksData !== 'undefined') {
                addDebugMessage(`✅ تم تحميل بيانات الكتب: ${booksData.length} كتاب`, 'success');
            } else {
                addDebugMessage('❌ فشل تحميل بيانات الكتب', 'error');
            }
            
            // Check containers
            const containers = ['featured-books', 'popular-books', 'recent-books'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                if (container) {
                    addDebugMessage(`✅ تم العثور على حاوية: ${id}`, 'success');
                    addDebugMessage(`📊 محتوى ${id}: ${container.children.length} عنصر`, 'log');
                } else {
                    addDebugMessage(`❌ لم يتم العثور على حاوية: ${id}`, 'error');
                }
            });
        }, 2000);
        
        // Monitor for errors
        window.addEventListener('error', function(e) {
            addDebugMessage(`❌ خطأ: ${e.message} في ${e.filename}:${e.lineno}`, 'error');
        });
        
        addDebugMessage('🔍 تم تشغيل نظام التشخيص', 'success');
    </script>
</body>
</html>
