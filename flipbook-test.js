/* ===== APEX 3D FLIPBOOK TESTING SYSTEM =====
   نظام اختبار شامل للكتاب الثلاثي الأبعاد
   المبرمج الأسطوري - APEX Edition
*/

// Test configuration
const TEST_CONFIG = {
    enableTesting: true,
    enablePerformanceTests: true,
    enableVisualTests: true,
    enableInteractionTests: true,
    testTimeout: 5000,
    performanceThreshold: {
        fps: 30,
        loadTime: 2000,
        animationTime: 1000
    }
};

// Test results storage
const TEST_RESULTS = {
    performance: {},
    visual: {},
    interaction: {},
    overall: 'pending'
};

// ===== PERFORMANCE TESTING =====

function runPerformanceTests() {
    console.log('🧪 بدء اختبارات الأداء...');
    
    const tests = [
        testAnimationPerformance,
        testMemoryUsage,
        testLoadingSpeed,
        testResponsiveness
    ];
    
    return Promise.all(tests.map(test => test()));
}

async function testAnimationPerformance() {
    console.log('⚡ اختبار أداء الأنيميشن...');
    
    const startTime = performance.now();
    let frameCount = 0;
    let testDuration = 2000; // 2 seconds
    
    return new Promise((resolve) => {
        function countFrames() {
            frameCount++;
            if (performance.now() - startTime < testDuration) {
                requestAnimationFrame(countFrames);
            } else {
                const fps = Math.round((frameCount * 1000) / testDuration);
                const passed = fps >= TEST_CONFIG.performanceThreshold.fps;
                
                TEST_RESULTS.performance.animation = {
                    fps: fps,
                    passed: passed,
                    message: passed ? 'أداء ممتاز' : 'أداء منخفض'
                };
                
                console.log(`📊 FPS: ${fps} (${passed ? '✅' : '❌'})`);
                resolve(TEST_RESULTS.performance.animation);
            }
        }
        requestAnimationFrame(countFrames);
    });
}

async function testMemoryUsage() {
    console.log('💾 اختبار استخدام الذاكرة...');
    
    if (performance.memory) {
        const memoryInfo = {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };
        
        const memoryUsagePercent = (memoryInfo.used / memoryInfo.limit) * 100;
        const passed = memoryUsagePercent < 50; // Less than 50% memory usage
        
        TEST_RESULTS.performance.memory = {
            usage: memoryInfo,
            percentage: Math.round(memoryUsagePercent),
            passed: passed,
            message: passed ? 'استخدام ذاكرة مثالي' : 'استخدام ذاكرة مرتفع'
        };
        
        console.log(`💾 استخدام الذاكرة: ${memoryInfo.used}MB / ${memoryInfo.limit}MB (${passed ? '✅' : '❌'})`);
    } else {
        TEST_RESULTS.performance.memory = {
            passed: true,
            message: 'معلومات الذاكرة غير متاحة'
        };
    }
    
    return TEST_RESULTS.performance.memory;
}

async function testLoadingSpeed() {
    console.log('🚀 اختبار سرعة التحميل...');
    
    const startTime = performance.now();
    
    // Simulate book loading
    const books = document.querySelectorAll('.flipbook-card');
    const loadPromises = Array.from(books).map(book => {
        return new Promise(resolve => {
            const img = book.querySelector('img');
            if (img && !img.complete) {
                img.onload = resolve;
                img.onerror = resolve;
            } else {
                resolve();
            }
        });
    });
    
    await Promise.all(loadPromises);
    
    const loadTime = performance.now() - startTime;
    const passed = loadTime < TEST_CONFIG.performanceThreshold.loadTime;
    
    TEST_RESULTS.performance.loading = {
        time: Math.round(loadTime),
        passed: passed,
        message: passed ? 'تحميل سريع' : 'تحميل بطيء'
    };
    
    console.log(`🚀 وقت التحميل: ${Math.round(loadTime)}ms (${passed ? '✅' : '❌'})`);
    return TEST_RESULTS.performance.loading;
}

async function testResponsiveness() {
    console.log('📱 اختبار الاستجابة...');
    
    const viewportSizes = [
        { width: 320, height: 568, name: 'Mobile Small' },
        { width: 375, height: 667, name: 'Mobile Medium' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 1920, height: 1080, name: 'Desktop' }
    ];
    
    const results = [];
    
    for (const size of viewportSizes) {
        // Simulate viewport change
        const mediaQuery = `(max-width: ${size.width}px)`;
        const matches = window.matchMedia(mediaQuery).matches;
        
        results.push({
            size: size.name,
            width: size.width,
            responsive: true // Simplified test
        });
    }
    
    const allResponsive = results.every(r => r.responsive);
    
    TEST_RESULTS.performance.responsive = {
        results: results,
        passed: allResponsive,
        message: allResponsive ? 'تصميم متجاوب ممتاز' : 'مشاكل في التصميم المتجاوب'
    };
    
    console.log(`📱 الاستجابة: ${allResponsive ? '✅' : '❌'}`);
    return TEST_RESULTS.performance.responsive;
}

// ===== VISUAL TESTING =====

function runVisualTests() {
    console.log('👁️ بدء الاختبارات البصرية...');
    
    const tests = [
        testBookRendering,
        testAnimationSmoothness,
        testColorContrast,
        testTypography
    ];
    
    return Promise.all(tests.map(test => test()));
}

async function testBookRendering() {
    console.log('📚 اختبار عرض الكتب...');
    
    const books = document.querySelectorAll('.flipbook-card');
    const issues = [];
    
    books.forEach((book, index) => {
        const rect = book.getBoundingClientRect();
        
        if (rect.width === 0 || rect.height === 0) {
            issues.push(`كتاب ${index + 1}: أبعاد غير صحيحة`);
        }
        
        const img = book.querySelector('img');
        if (img && !img.complete && !img.naturalWidth) {
            issues.push(`كتاب ${index + 1}: فشل تحميل الصورة`);
        }
        
        const computedStyle = window.getComputedStyle(book);
        if (computedStyle.opacity === '0' || computedStyle.visibility === 'hidden') {
            issues.push(`كتاب ${index + 1}: غير مرئي`);
        }
    });
    
    const passed = issues.length === 0;
    
    TEST_RESULTS.visual.rendering = {
        booksCount: books.length,
        issues: issues,
        passed: passed,
        message: passed ? 'عرض مثالي للكتب' : `${issues.length} مشكلة في العرض`
    };
    
    console.log(`📚 عرض الكتب: ${books.length} كتاب (${passed ? '✅' : '❌'})`);
    return TEST_RESULTS.visual.rendering;
}

async function testAnimationSmoothness() {
    console.log('🎬 اختبار سلاسة الأنيميشن...');
    
    // Test animation by triggering a book opening
    const firstBook = document.querySelector('.flipbook-card');
    if (!firstBook) {
        TEST_RESULTS.visual.animation = {
            passed: false,
            message: 'لا توجد كتب للاختبار'
        };
        return TEST_RESULTS.visual.animation;
    }
    
    const startTime = performance.now();
    let animationCompleted = false;
    
    // Simulate book opening
    firstBook.click();
    
    return new Promise((resolve) => {
        const checkAnimation = () => {
            const elapsed = performance.now() - startTime;
            
            if (firstBook.classList.contains('open') || elapsed > TEST_CONFIG.testTimeout) {
                animationCompleted = true;
                const passed = elapsed < TEST_CONFIG.performanceThreshold.animationTime;
                
                TEST_RESULTS.visual.animation = {
                    duration: Math.round(elapsed),
                    passed: passed,
                    message: passed ? 'أنيميشن سلس' : 'أنيميشن بطيء'
                };
                
                console.log(`🎬 مدة الأنيميشن: ${Math.round(elapsed)}ms (${passed ? '✅' : '❌'})`);
                resolve(TEST_RESULTS.visual.animation);
            } else {
                requestAnimationFrame(checkAnimation);
            }
        };
        
        requestAnimationFrame(checkAnimation);
    });
}

async function testColorContrast() {
    console.log('🎨 اختبار تباين الألوان...');
    
    // Simplified contrast test
    const textElements = document.querySelectorAll('.book-title, .book-author, .page-content');
    const contrastIssues = [];
    
    textElements.forEach((element, index) => {
        const style = window.getComputedStyle(element);
        const color = style.color;
        const backgroundColor = style.backgroundColor;
        
        // Simplified contrast check (would need more complex calculation in real scenario)
        if (color === backgroundColor) {
            contrastIssues.push(`عنصر ${index + 1}: تباين ضعيف`);
        }
    });
    
    const passed = contrastIssues.length === 0;
    
    TEST_RESULTS.visual.contrast = {
        elementsChecked: textElements.length,
        issues: contrastIssues,
        passed: passed,
        message: passed ? 'تباين ألوان ممتاز' : `${contrastIssues.length} مشكلة في التباين`
    };
    
    console.log(`🎨 تباين الألوان: ${textElements.length} عنصر (${passed ? '✅' : '❌'})`);
    return TEST_RESULTS.visual.contrast;
}

async function testTypography() {
    console.log('📝 اختبار الطباعة...');
    
    const textElements = document.querySelectorAll('.book-title, .book-author, .page-content');
    const typographyIssues = [];
    
    textElements.forEach((element, index) => {
        const style = window.getComputedStyle(element);
        const fontSize = parseFloat(style.fontSize);
        const lineHeight = parseFloat(style.lineHeight);
        
        if (fontSize < 12) {
            typographyIssues.push(`عنصر ${index + 1}: خط صغير جداً`);
        }
        
        if (lineHeight && lineHeight < fontSize) {
            typographyIssues.push(`عنصر ${index + 1}: ارتفاع السطر قصير`);
        }
    });
    
    const passed = typographyIssues.length === 0;
    
    TEST_RESULTS.visual.typography = {
        elementsChecked: textElements.length,
        issues: typographyIssues,
        passed: passed,
        message: passed ? 'طباعة ممتازة' : `${typographyIssues.length} مشكلة في الطباعة`
    };
    
    console.log(`📝 الطباعة: ${textElements.length} عنصر (${passed ? '✅' : '❌'})`);
    return TEST_RESULTS.visual.typography;
}

// ===== INTERACTION TESTING =====

function runInteractionTests() {
    console.log('🖱️ بدء اختبارات التفاعل...');

    const tests = [
        testBookOpening,
        testPageNavigation,
        testKeyboardNavigation,
        testTouchInteraction
    ];

    return Promise.all(tests.map(test => test()));
}

async function testBookOpening() {
    console.log('📖 اختبار فتح الكتاب...');

    const book = document.querySelector('.flipbook-card.closed');
    if (!book) {
        TEST_RESULTS.interaction.opening = {
            passed: false,
            message: 'لا توجد كتب مغلقة للاختبار'
        };
        return TEST_RESULTS.interaction.opening;
    }

    const startTime = performance.now();

    // Simulate click
    book.click();

    return new Promise((resolve) => {
        const checkOpening = () => {
            const elapsed = performance.now() - startTime;

            if (book.classList.contains('open') || elapsed > TEST_CONFIG.testTimeout) {
                const opened = book.classList.contains('open');
                const passed = opened && elapsed < TEST_CONFIG.performanceThreshold.animationTime;

                TEST_RESULTS.interaction.opening = {
                    opened: opened,
                    duration: Math.round(elapsed),
                    passed: passed,
                    message: passed ? 'فتح الكتاب يعمل بشكل مثالي' : 'مشكلة في فتح الكتاب'
                };

                console.log(`📖 فتح الكتاب: ${opened ? 'نجح' : 'فشل'} في ${Math.round(elapsed)}ms (${passed ? '✅' : '❌'})`);
                resolve(TEST_RESULTS.interaction.opening);
            } else {
                requestAnimationFrame(checkOpening);
            }
        };

        requestAnimationFrame(checkOpening);
    });
}

async function testPageNavigation() {
    console.log('📄 اختبار التنقل بين الصفحات...');

    const openBook = document.querySelector('.flipbook-card.open');
    if (!openBook) {
        TEST_RESULTS.interaction.navigation = {
            passed: false,
            message: 'لا يوجد كتاب مفتوح للاختبار'
        };
        return TEST_RESULTS.interaction.navigation;
    }

    const nextBtn = openBook.querySelector('.next-btn');
    const prevBtn = openBook.querySelector('.prev-btn');

    if (!nextBtn || !prevBtn) {
        TEST_RESULTS.interaction.navigation = {
            passed: false,
            message: 'أزرار التنقل غير موجودة'
        };
        return TEST_RESULTS.interaction.navigation;
    }

    const initialPage = parseInt(openBook.getAttribute('data-current-page')) || 0;

    // Test next page
    nextBtn.click();

    return new Promise((resolve) => {
        setTimeout(() => {
            const newPage = parseInt(openBook.getAttribute('data-current-page')) || 0;
            const navigationWorked = newPage > initialPage;

            TEST_RESULTS.interaction.navigation = {
                initialPage: initialPage,
                newPage: newPage,
                passed: navigationWorked,
                message: navigationWorked ? 'التنقل يعمل بشكل مثالي' : 'مشكلة في التنقل'
            };

            console.log(`📄 التنقل: من صفحة ${initialPage} إلى ${newPage} (${navigationWorked ? '✅' : '❌'})`);
            resolve(TEST_RESULTS.interaction.navigation);
        }, 1000);
    });
}

async function testKeyboardNavigation() {
    console.log('⌨️ اختبار التنقل بالكيبورد...');

    const openBook = document.querySelector('.flipbook-card.open');
    if (!openBook) {
        TEST_RESULTS.interaction.keyboard = {
            passed: false,
            message: 'لا يوجد كتاب مفتوح للاختبار'
        };
        return TEST_RESULTS.interaction.keyboard;
    }

    const initialPage = parseInt(openBook.getAttribute('data-current-page')) || 0;

    // Simulate arrow key press
    const arrowEvent = new KeyboardEvent('keydown', {
        key: 'ArrowRight',
        code: 'ArrowRight',
        bubbles: true
    });

    document.dispatchEvent(arrowEvent);

    return new Promise((resolve) => {
        setTimeout(() => {
            const newPage = parseInt(openBook.getAttribute('data-current-page')) || 0;
            const keyboardWorked = newPage !== initialPage;

            TEST_RESULTS.interaction.keyboard = {
                initialPage: initialPage,
                newPage: newPage,
                passed: keyboardWorked,
                message: keyboardWorked ? 'التنقل بالكيبورد يعمل' : 'مشكلة في التنقل بالكيبورد'
            };

            console.log(`⌨️ الكيبورد: من صفحة ${initialPage} إلى ${newPage} (${keyboardWorked ? '✅' : '❌'})`);
            resolve(TEST_RESULTS.interaction.keyboard);
        }, 1000);
    });
}

async function testTouchInteraction() {
    console.log('👆 اختبار التفاعل باللمس...');

    const isTouch = 'ontouchstart' in window;

    if (!isTouch) {
        TEST_RESULTS.interaction.touch = {
            passed: true,
            message: 'الجهاز لا يدعم اللمس'
        };
        return TEST_RESULTS.interaction.touch;
    }

    const book = document.querySelector('.flipbook-card');
    if (!book) {
        TEST_RESULTS.interaction.touch = {
            passed: false,
            message: 'لا توجد كتب للاختبار'
        };
        return TEST_RESULTS.interaction.touch;
    }

    // Simulate touch event
    const touchEvent = new TouchEvent('touchstart', {
        touches: [{
            clientX: 100,
            clientY: 100,
            target: book
        }],
        bubbles: true
    });

    book.dispatchEvent(touchEvent);

    TEST_RESULTS.interaction.touch = {
        passed: true,
        message: 'أحداث اللمس تعمل بشكل صحيح'
    };

    console.log('👆 اللمس: ✅');
    return TEST_RESULTS.interaction.touch;
}

// ===== COMPREHENSIVE TEST RUNNER =====

async function runAllTests() {
    console.log('🧪 بدء الاختبار الشامل لنظام 3D Flipbook...');
    console.log('=' .repeat(50));

    const startTime = performance.now();

    try {
        // Run all test categories
        if (TEST_CONFIG.enablePerformanceTests) {
            await runPerformanceTests();
        }

        if (TEST_CONFIG.enableVisualTests) {
            await runVisualTests();
        }

        if (TEST_CONFIG.enableInteractionTests) {
            await runInteractionTests();
        }

        // Generate final report
        generateTestReport();

        const totalTime = performance.now() - startTime;
        console.log(`🎯 اكتمل الاختبار في ${Math.round(totalTime)}ms`);

    } catch (error) {
        console.error('❌ خطأ أثناء الاختبار:', error);
        TEST_RESULTS.overall = 'failed';
    }
}

function generateTestReport() {
    console.log('📊 تقرير الاختبار النهائي:');
    console.log('=' .repeat(50));

    let totalTests = 0;
    let passedTests = 0;

    // Performance tests
    if (TEST_RESULTS.performance) {
        console.log('⚡ اختبارات الأداء:');
        Object.entries(TEST_RESULTS.performance).forEach(([key, result]) => {
            totalTests++;
            if (result.passed) passedTests++;
            console.log(`  ${result.passed ? '✅' : '❌'} ${key}: ${result.message}`);
        });
    }

    // Visual tests
    if (TEST_RESULTS.visual) {
        console.log('👁️ الاختبارات البصرية:');
        Object.entries(TEST_RESULTS.visual).forEach(([key, result]) => {
            totalTests++;
            if (result.passed) passedTests++;
            console.log(`  ${result.passed ? '✅' : '❌'} ${key}: ${result.message}`);
        });
    }

    // Interaction tests
    if (TEST_RESULTS.interaction) {
        console.log('🖱️ اختبارات التفاعل:');
        Object.entries(TEST_RESULTS.interaction).forEach(([key, result]) => {
            totalTests++;
            if (result.passed) passedTests++;
            console.log(`  ${result.passed ? '✅' : '❌'} ${key}: ${result.message}`);
        });
    }

    // Overall result
    const successRate = (passedTests / totalTests) * 100;
    TEST_RESULTS.overall = successRate >= 80 ? 'excellent' : successRate >= 60 ? 'good' : 'needs-improvement';

    console.log('=' .repeat(50));
    console.log(`📈 النتيجة الإجمالية: ${passedTests}/${totalTests} (${Math.round(successRate)}%)`);

    if (successRate >= 80) {
        console.log('🎉 ممتاز! النظام يعمل بشكل مثالي');
    } else if (successRate >= 60) {
        console.log('👍 جيد! النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة');
    } else {
        console.log('⚠️ يحتاج تحسين! هناك مشاكل تحتاج إلى إصلاح');
    }

    console.log('=' .repeat(50));

    // Store results for external access
    window.FLIPBOOK_TEST_RESULTS = TEST_RESULTS;
}

// Auto-run tests when enabled
if (TEST_CONFIG.enableTesting) {
    // Wait for DOM and flipbook system to be ready
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            runAllTests();
        }, 2000); // Wait 2 seconds for everything to initialize
    });
}

// Export for manual testing
window.FlipbookTester = {
    runAllTests,
    runPerformanceTests,
    runVisualTests,
    runInteractionTests,
    generateTestReport,
    getResults: () => TEST_RESULTS
};
