<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المكتبة الطبية الإلكترونية - اختبار</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 218, 55, 0.2);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 400;
            color: #ffda37;
            text-decoration: none;
        }

        .main-content {
            margin-top: 80px;
            padding: 2rem 0;
        }

        .section {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            margin-bottom: 4rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 25px;
            border-right: 4px solid #ffda37;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 400;
            color: #ffda37;
        }

        .books-grid-container {
            position: relative;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .books-grid {
            display: flex !important;
            gap: 2rem;
            padding: 3rem 0;
            overflow-x: auto;
            scroll-behavior: smooth;
            scrollbar-width: none;
            -ms-overflow-style: none;
            position: relative;
            align-items: flex-start;
            min-height: 500px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 15px;
            border: 1px solid rgba(255, 218, 55, 0.1);
        }

        .books-grid::-webkit-scrollbar {
            display: none;
        }

        .books-grid:empty::after {
            content: "⚠️ لا توجد كتب - تحقق من وحدة التحكم";
            color: #ff6b6b;
            font-size: 1.2rem;
            padding: 3rem;
            background: rgba(255, 107, 107, 0.1);
            border: 2px dashed #ff6b6b;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            text-align: center;
        }

        .debug-info {
            background: rgba(255, 218, 55, 0.1);
            border: 1px solid #ffda37;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .error-log {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff6b6b;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .section-title {
                font-size: 1.8rem;
            }
            
            .books-grid {
                gap: 1rem;
                padding: 2rem 1rem;
            }
            
            .nav-container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="#" class="logo">📚 المكتبة الطبية</a>
            <div style="color: #ffda37; font-size: 0.9rem;">اختبار تحميل الكتب</div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Debug Info -->
        <div class="section">
            <div class="debug-info">
                <h3>🔍 معلومات التشخيص:</h3>
                <div id="debug-log">⏳ جاري التحميل...</div>
            </div>
            <div id="error-log" class="error-log" style="display: none;">
                <h3>❌ سجل الأخطاء:</h3>
                <div id="error-content"></div>
            </div>
        </div>

        <!-- Featured Books Section -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">الكتب المميزة</h2>
            </div>
            <div class="books-grid-container">
                <div id="featured-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Popular Books Section -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">الكتب الشائعة</h2>
            </div>
            <div class="books-grid-container">
                <div id="popular-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Recent Books Section -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">الكتب الحديثة</h2>
            </div>
            <div class="books-grid-container">
                <div id="recent-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Load JavaScript -->
    <script src="script-simple.js"></script>
    
    <!-- Debug Script -->
    <script>
        console.log('🚀 بدء تشخيص الموقع...');
        
        function updateDebugLog(message) {
            const debugLog = document.getElementById('debug-log');
            if (debugLog) {
                debugLog.innerHTML += '<br>' + message;
            }
            console.log(message);
        }
        
        function logError(error) {
            const errorLog = document.getElementById('error-log');
            const errorContent = document.getElementById('error-content');
            if (errorLog && errorContent) {
                errorLog.style.display = 'block';
                errorContent.innerHTML += '<br>' + error;
            }
            console.error(error);
        }
        
        // Check if script loaded
        setTimeout(function() {
            updateDebugLog('✅ تم تحميل HTML');
            
            if (typeof booksData !== 'undefined') {
                updateDebugLog('✅ تم تحميل بيانات الكتب: ' + booksData.length + ' كتاب');
            } else {
                logError('❌ لم يتم تحميل بيانات الكتب');
            }
            
            // Check containers
            const containers = ['featured-books', 'popular-books', 'recent-books'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                if (container) {
                    updateDebugLog('✅ تم العثور على حاوية: ' + id);
                    updateDebugLog('📊 محتوى الحاوية ' + id + ': ' + container.children.length + ' عنصر');
                } else {
                    logError('❌ لم يتم العثور على حاوية: ' + id);
                }
            });
        }, 1000);
        
        // Monitor for errors
        window.addEventListener('error', function(e) {
            logError('❌ خطأ JavaScript: ' + e.message + ' في ' + e.filename + ':' + e.lineno);
        });
        
        updateDebugLog('🔍 تم تشغيل نظام التشخيص');
    </script>
</body>
</html>
