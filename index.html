<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#FFDA37">
    <title>المكتبة الطبية الإلكترونية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Outfit:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=PT+Serif:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="flipbook-3d.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="#" class="logo">
                <i class="fas fa-book-medical"></i>
                المكتبة الطبية
            </a>
            
            <nav>
                <ul class="nav-menu">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#books">الكتب</a></li>
                    <li><a href="#resources">المصادر العلمية</a></li>
                    <li><a href="#about">حولنا</a></li>
                </ul>
            </nav>

            <div class="search-container">
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
                <input type="text" class="search-input" placeholder="ابحث عن كتاب طبي...">
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="mobile-menu-header">
            <a href="#" class="logo">
                <i class="fas fa-book-medical"></i>
                المكتبة الطبية
            </a>
            <button class="mobile-menu-close" id="mobileMenuClose">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <nav>
            <ul class="mobile-nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#books">الكتب</a></li>
                <li><a href="#resources">المصادر العلمية</a></li>
                <li><a href="#about">حولنا</a></li>
            </ul>
        </nav>

        <div class="mobile-search-container">
            <div class="search-container">
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
                <input type="text" class="search-input" placeholder="ابحث عن كتاب طبي...">
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section" id="home">
            <div class="hero-content">
                <h1>مكتبتك الطبية الشاملة</h1>
                <p>اكتشف أحدث الكتب والمراجع الطبية من أفضل المؤلفين والناشرين في العالم</p>
            </div>
        </section>

        <!-- Books Section -->
        <section class="section fade-in books-section" id="books">
            <div class="section-header">
                <h2 class="section-title">الكتب</h2>
                <div class="section-description">
                    <p>مجموعة شاملة من الكتب الطبية المتخصصة</p>
                </div>
            </div>
            <div class="books-container">
                <div class="books-grid" id="books-grid">
                    <!-- Books will be loaded here -->
                </div>
                <div class="load-more-container" id="books-load-more" style="display: none;">
                    <button class="load-more-btn" onclick="loadMoreBooks()">
                        <i class="fas fa-plus"></i>
                        إظهار المزيد
                    </button>
                </div>
            </div>
        </section>

        <!-- Scientific Resources Section -->
        <section class="section fade-in books-section" id="resources">
            <div class="section-header">
                <h2 class="section-title">المصادر العلمية</h2>
                <div class="section-description">
                    <p>مراجع ومصادر علمية متخصصة للباحثين والأطباء</p>
                </div>
            </div>
            <div class="books-container">
                <div class="books-grid" id="resources-grid">
                    <!-- Resources will be loaded here -->
                </div>
                <div class="load-more-container" id="resources-load-more" style="display: none;">
                    <button class="load-more-btn" onclick="loadMoreResources()">
                        <i class="fas fa-plus"></i>
                        إظهار المزيد
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal -->
    <div class="modal" id="book-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">تفاصيل الكتاب</h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modal-body">
                <!-- Book details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="script-fixed.js"></script>
    <script src="flipbook-test.js"></script>

    <!-- Mobile Menu Script -->
    <script>
        // Mobile Menu Toggle
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileMenu = document.getElementById('mobileMenu');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        const mobileMenuClose = document.getElementById('mobileMenuClose');

        function openMobileMenu() {
            mobileMenu.classList.add('active');
            mobileMenuOverlay.classList.add('active');
            mobileMenuToggle.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenu() {
            mobileMenu.classList.remove('active');
            mobileMenuOverlay.classList.remove('active');
            mobileMenuToggle.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Event listeners
        mobileMenuToggle.addEventListener('click', openMobileMenu);
        mobileMenuClose.addEventListener('click', closeMobileMenu);
        mobileMenuOverlay.addEventListener('click', closeMobileMenu);

        // Close menu when clicking on menu links
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-menu a');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', closeMobileMenu);
        });

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768 && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
    </script>
</body>
</html>
