# APEX Admin System Requirements
# FastAPI Core
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database & ORM
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
databases[postgresql]==0.8.0

# Authentication & Security
firebase-admin==6.2.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8

# File Upload & Storage
aiofiles==23.2.1
pillow==10.1.0
boto3==1.34.0

# Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP & CORS
httpx==0.25.2
python-cors==1.7.0

# Caching & Performance
redis==5.0.1
asyncio-throttle==1.0.2

# Monitoring & Logging
structlog==23.2.0
prometheus-client==0.19.0

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
mypy==1.7.1

# Additional utilities
python-slugify==8.0.1
pytz==2023.3
