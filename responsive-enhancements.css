/* تحسينات إضافية للتجاوب والأداء */

/* تحسينات عامة للأداء */
* {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
}

/* تحسين التمرير للأجهزة المحمولة */
.categories {
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
}

.category-btn {
    scroll-snap-align: start;
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .book-cover-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسينات للوضع الأفقي على الهواتف */
@media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
        padding: calc(80px + 2rem) 0 1rem;
        min-height: 90vh;
    }
    
    .hero-content h1 {
        font-size: calc(var(--h1-m-font-size) * 0.7);
        margin-bottom: 0.5rem;
    }
    
    .hero-content p {
        font-size: calc(var(--body-m-font-size) * 0.9);
        margin-bottom: 1rem;
    }
    
    .categories {
        padding: 1rem;
        margin-bottom: 2rem;
    }
    
    .section {
        margin-bottom: 2rem;
    }
    
    .section-header {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1600px) {
    .categories {
        max-width: 1600px;
        padding: 2.5rem;
    }
    
    .category-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
        min-width: 140px;
    }
    
    .books-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 4rem 3rem;
    }
    
    .hero-section {
        padding: calc(80px + 10rem) 0 8rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .header,
    .mobile-menu,
    .mobile-menu-overlay,
    .categories,
    .view-all-btn {
        display: none !important;
    }
    
    .hero-section {
        background: none !important;
        color: #000 !important;
        padding: 2rem 0 !important;
    }
    
    .section-header {
        background: none !important;
        border: 1px solid #ccc !important;
        color: #000 !important;
    }
    
    .book-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* تحسينات لإمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .categories {
        scroll-behavior: auto;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    /* الموقع مصمم بالفعل للوضع المظلم، لكن يمكن إضافة تحسينات إضافية هنا */
    .book-page-content {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        color: #ffffff;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    .category-btn {
        border-width: 3px;
    }
    
    .category-btn.active {
        border-width: 4px;
        box-shadow: 0 0 0 2px rgba(255, 218, 55, 0.8);
    }
    
    .section-header {
        border-width: 2px;
    }
}

/* تحسينات للشاشات الصغيرة جداً (أقل من 320px) */
@media (max-width: 280px) {
    .categories {
        padding: 0.5rem 0.2rem;
        gap: 0.3rem;
    }
    
    .category-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.7rem;
        min-width: 70px;
        border-radius: 12px;
    }
    
    .hero-content {
        padding: 0 0.3rem;
    }
    
    .hero-content h1 {
        font-size: calc(var(--h1-m-font-size) * 0.6);
    }
    
    .section {
        padding: 0 0.3rem;
    }
    
    .section-header {
        padding: 0.5rem 0.2rem;
        border-radius: 8px;
    }
    
    .books-grid {
        padding: 1rem 0;
    }
}

/* تحسينات للتفاعل باللمس - مبسطة */
@media (hover: none) and (pointer: coarse) {
    /* إزالة تأثيرات hover للأجهزة اللمسية */
    .category-btn:hover {
        transform: none;
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(254, 207, 5, 0.3);
        color: #ffffff;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .view-all-btn:hover {
        transform: none;
        background: rgba(255, 255, 255, 0.05);
        color: var(--color-primary);
    }
}

/* تحسينات للشاشات العريضة */
@media (min-aspect-ratio: 16/9) and (min-width: 1200px) {
    .hero-section {
        background-attachment: fixed;
    }
    
    .categories {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* تحسينات للشاشات الطويلة */
@media (max-aspect-ratio: 9/16) {
    .hero-section {
        min-height: 80vh;
    }
    
    .categories {
        margin-bottom: 2rem;
    }
    
    .section {
        margin-bottom: 2rem;
    }
}
