/* Additional Responsive Fixes and Enhancements */

/* Critical Arrow Button Fixes */
.books-nav-arrow {
    /* Force visibility and clickability */
    display: flex !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 9999 !important;
    position: absolute !important;
    
    /* Prevent any interference */
    isolation: isolate !important;
    will-change: transform !important;
    
    /* Enhanced touch targets */
    min-width: 44px !important;
    min-height: 44px !important;
    
    /* Remove any potential blocking */
    overflow: visible !important;
    clip: unset !important;
    clip-path: none !important;
}

/* Ensure container doesn't block arrows */
.books-grid-container {
    overflow: visible !important;
}

.books-grid-container * {
    pointer-events: auto;
}

.books-grid-container *::before,
.books-grid-container *::after {
    pointer-events: none !important;
}

/* Mobile-specific arrow enhancements */
@media (max-width: 768px) {
    .books-nav-arrow {
        width: 50px !important;
        height: 50px !important;
        font-size: 18px !important;
        background: rgba(255, 218, 55, 1) !important;
        border: 3px solid rgba(0, 0, 0, 0.2) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
    }
    
    .books-nav-arrow.prev {
        left: -20px !important;
    }
    
    .books-nav-arrow.next {
        right: -20px !important;
    }
}

/* Small mobile specific */
@media (max-width: 480px) {
    .books-nav-arrow {
        width: 48px !important;
        height: 48px !important;
        font-size: 16px !important;
    }
    
    .books-nav-arrow.prev {
        left: -15px !important;
    }
    
    .books-nav-arrow.next {
        right: -15px !important;
    }
}

/* Enhanced button states */
.books-nav-arrow:hover,
.books-nav-arrow:focus,
.books-nav-arrow:active {
    background: var(--color-primary) !important;
    transform: translateY(-50%) scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5) !important;
    outline: none !important;
}

/* Category buttons responsive fixes */
.category-btn {
    /* Ensure proper touch targets */
    min-height: 44px !important;
    min-width: 80px !important;
    
    /* Enhanced clickability */
    cursor: pointer !important;
    pointer-events: auto !important;
    
    /* Better touch response */
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(255, 218, 55, 0.3) !important;
}

/* Mobile category button adjustments */
@media (max-width: 768px) {
    .categories {
        overflow-x: auto !important;
        flex-wrap: nowrap !important;
        justify-content: flex-start !important;
        padding: 1rem !important;
    }
    
    .category-btn {
        flex-shrink: 0 !important;
        min-width: 100px !important;
        padding: 0.8rem 1.2rem !important;
        font-size: 0.85rem !important;
    }
}

/* Book card responsive adjustments */
.book-card {
    /* Ensure proper sizing */
    flex-shrink: 0 !important;
    
    /* Responsive dimensions */
    width: clamp(200px, 20vw, 280px) !important;
    height: clamp(280px, 28vw, 400px) !important;
}

/* Mobile book card sizes */
@media (max-width: 768px) {
    .book-card {
        width: clamp(180px, 25vw, 220px) !important;
        height: clamp(250px, 35vw, 320px) !important;
    }
}

@media (max-width: 480px) {
    .book-card {
        width: clamp(160px, 30vw, 200px) !important;
        height: clamp(220px, 40vw, 280px) !important;
    }
}

/* Search container responsive fixes */
.search-container {
    /* Ensure proper sizing */
    min-width: 200px !important;
    max-width: 400px !important;
}

.search-input {
    /* Responsive width */
    width: clamp(150px, 20vw, 250px) !important;
    min-width: 150px !important;
}

/* Mobile search adjustments */
@media (max-width: 768px) {
    .search-container {
        min-width: 180px !important;
        max-width: 300px !important;
    }
    
    .search-input {
        width: clamp(120px, 25vw, 200px) !important;
        font-size: 14px !important;
    }
    
    .search-btn {
        width: 40px !important;
        height: 40px !important;
        min-width: 40px !important;
        min-height: 40px !important;
    }
}

/* Hero section responsive text */
.hero-content h1 {
    font-size: clamp(2rem, 6vw, 4rem) !important;
    line-height: 1.2 !important;
}

.hero-content p {
    font-size: clamp(1rem, 2.5vw, 1.25rem) !important;
    line-height: 1.6 !important;
    max-width: min(90vw, 600px) !important;
}

/* Section titles responsive */
.section-title {
    font-size: clamp(1.5rem, 4vw, 3rem) !important;
    line-height: 1.3 !important;
}

/* Button responsive sizing */
.book-action-btn,
.page-nav-btn {
    /* Ensure minimum touch targets */
    min-height: 44px !important;
    min-width: 100px !important;
    
    /* Responsive padding */
    padding: clamp(0.5rem, 2vw, 1rem) clamp(1rem, 3vw, 2rem) !important;
    
    /* Responsive font size */
    font-size: clamp(0.875rem, 1.5vw, 1rem) !important;
}

/* Mobile button adjustments */
@media (max-width: 768px) {
    .book-action-btn,
    .page-nav-btn {
        min-height: 48px !important;
        min-width: 120px !important;
        padding: 0.8rem 1.5rem !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
    }
}

/* Ensure all interactive elements are accessible */
button,
.book-action-btn,
.page-nav-btn,
.category-btn,
.books-nav-arrow,
.search-btn,
.mobile-menu-toggle,
.cover-click-area {
    /* Force accessibility */
    pointer-events: auto !important;
    touch-action: manipulation !important;
    user-select: none !important;
    
    /* Ensure visibility */
    opacity: 1 !important;
    visibility: visible !important;
    
    /* Proper z-index */
    position: relative !important;
    z-index: 10 !important;
}

/* Remove any potential blockers */
*::before,
*::after {
    pointer-events: none !important;
}

/* Re-enable for necessary pseudo-elements */
.category-btn::before,
.book-action-btn::before,
.view-all-btn::before {
    pointer-events: none !important;
}

/* Final mobile optimization */
@media (max-width: 480px) {
    /* Ensure everything is touchable */
    * {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Larger touch targets for small screens */
    .books-nav-arrow,
    .category-btn,
    .book-action-btn,
    .page-nav-btn,
    .search-btn {
        min-height: 48px !important;
        min-width: 48px !important;
    }
}
