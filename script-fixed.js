// Medical Library JavaScript - 3D Flipbook Version
// APEX - المبرمج الأسطوري - نظام كتاب ثلاثي الأبعاد واقعي

console.log('🚀 تحميل نظام 3D Flipbook الجديد...');

// ===== APEX ENHANCED MEDICAL LIBRARY DATA =====

// Books data - Medical books collection
const booksData = [
    {
        id: 1,
        title: "أطلس نيتر للتشريح البشري",
        author: "فرانك نيتر",
        description: "المرجع الأشهر في التشريح البشري مع رسوم توضيحية مفصلة ودقيقة. يحتوي هذا الأطلس على أكثر من 900 رسمة توضيحية ملونة تغطي جميع أجهزة الجسم البشري بتفصيل دقيق. يعتبر هذا الكتاب المرجع الأول للطلاب والأطباء في دراسة علم التشريح، حيث يقدم معلومات شاملة ومحدثة عن بنية الجسم البشري ووظائفه المختلفة.",
        cover: "https://picsum.photos/300/400?random=1",
        rating: 4.9,
        category: "anatomy",
        type: "book"
    },
    {
        id: 2,
        title: "علم وظائف الأعضاء الطبي",
        author: "آرثر جايتون",
        description: "الكتاب الأساسي في علم وظائف الأعضاء للطلاب والممارسين. يقدم شرحاً مفصلاً لوظائف جميع أجهزة الجسم البشري مع التركيز على الآليات الفسيولوجية المعقدة. يتضمن الكتاب أحدث الاكتشافات العلمية في مجال الفسيولوجيا الطبية مع أمثلة سريرية متنوعة تساعد على فهم التطبيقات العملية للمعرفة النظرية.",
        cover: "https://picsum.photos/300/400?random=2",
        rating: 4.8,
        category: "physiology",
        type: "book"
    },
    {
        id: 3,
        title: "علم الأمراض الأساسي",
        author: "فينيش كومار",
        description: "دليل شامل لفهم الأمراض وآليات حدوثها مع التركيز على الأسس الجزيئية والخلوية للمرض. يغطي الكتاب جميع أنواع الأمراض من الالتهابات إلى الأورام مع شرح مفصل للتغيرات المرضية وأعراضها السريرية. يتضمن صوراً مجهرية عالية الجودة ورسوماً توضيحية تساعد على فهم العمليات المرضية المعقدة.",
        cover: "https://picsum.photos/300/400?random=3",
        rating: 4.7,
        category: "pathology",
        type: "book"
    },
    {
        id: 4,
        title: "علم الأدوية الإكلينيكي",
        author: "برتون جودمان",
        description: "المرجع الشامل في علم الأدوية والعلاج الدوائي مع التركيز على التطبيقات السريرية. يقدم معلومات مفصلة عن آليات عمل الأدوية وتفاعلاتها وآثارها الجانبية. يتضمن أحدث الأدوية المطورة مع إرشادات واضحة للاستخدام الآمن والفعال في الممارسة الطبية اليومية.",
        cover: "https://picsum.photos/300/400?random=4",
        rating: 4.6,
        category: "pharmacology",
        type: "book"
    },
    {
        id: 5,
        title: "مبادئ الجراحة",
        author: "شوارتز",
        description: "الكتاب الأساسي في مبادئ وتقنيات الجراحة مع تغطية شاملة لجميع التخصصات الجراحية. يقدم شرحاً مفصلاً للتقنيات الجراحية الحديثة مع التركيز على السلامة والدقة. يتضمن صوراً توضيحية عالية الجودة ومقاطع فيديو تعليمية تساعد على فهم الإجراءات الجراحية المعقدة.",
        cover: "https://picsum.photos/300/400?random=5",
        rating: 4.8,
        category: "surgery",
        type: "book"
    },
    {
        id: 6,
        title: "طب الأطفال الشامل",
        author: "نيلسون",
        description: "المرجع الشامل في طب الأطفال وأمراضهم مع تغطية كاملة لجميع مراحل الطفولة. يقدم معلومات مفصلة عن نمو وتطور الأطفال والأمراض الشائعة في كل مرحلة عمرية. يتضمن إرشادات واضحة للتشخيص والعلاج مع التركيز على الرعاية الشاملة للطفل وأسرته.",
        cover: "https://picsum.photos/300/400?random=6",
        rating: 4.5,
        category: "pediatrics",
        type: "book"
    },
    {
        id: 7,
        title: "أمراض القلب والأوعية الدموية",
        author: "يوجين برونوالد",
        description: "دليل متخصص في أمراض القلب والأوعية الدموية مع أحدث التطورات في التشخيص والعلاج. يغطي جميع أمراض القلب من الأمراض الخلقية إلى أمراض الشرايين التاجية مع شرح مفصل للتقنيات التشخيصية الحديثة والعلاجات المتقدمة.",
        cover: "https://picsum.photos/300/400?random=7",
        rating: 4.7,
        category: "cardiology",
        type: "book"
    },
    {
        id: 8,
        title: "طب الأعصاب السريري",
        author: "آدامز وفيكتور",
        description: "مرجع شامل في طب الأعصاب مع تغطية كاملة للأمراض العصبية وطرق تشخيصها وعلاجها. يقدم شرحاً مفصلاً للفحص العصبي والتقنيات التشخيصية المتقدمة مع حالات سريرية متنوعة تساعد على فهم التطبيقات العملية.",
        cover: "https://picsum.photos/300/400?random=8",
        rating: 4.6,
        category: "neurology",
        type: "book"
    },
    {
        id: 9,
        title: "أمراض الجهاز الهضمي",
        author: "مارفن سليزنجر",
        description: "دليل شامل لأمراض الجهاز الهضمي والكبد مع أحدث طرق التشخيص والعلاج. يغطي جميع الاضطرابات من القرح إلى أمراض الكبد المزمنة مع التركيز على العلاجات الحديثة والتدخلات الجراحية المتقدمة.",
        cover: "https://picsum.photos/300/400?random=9",
        rating: 4.5,
        category: "gastroenterology",
        type: "book"
    },
    {
        id: 10,
        title: "طب الطوارئ الشامل",
        author: "جوديث تينتينالي",
        description: "المرجع الأساسي في طب الطوارئ مع بروتوكولات واضحة للحالات الحرجة. يقدم إرشادات سريعة ودقيقة للتعامل مع الحالات الطارئة من الصدمات إلى النوبات القلبية مع خوارزميات العلاج المحدثة.",
        cover: "https://picsum.photos/300/400?random=10",
        rating: 4.7,
        category: "emergency",
        type: "book"
    },
    {
        id: 11,
        title: "علم الأشعة التشخيصية",
        author: "رونالد إيزنبرغ",
        description: "دليل متخصص في الأشعة التشخيصية مع صور عالية الجودة وتفسيرات مفصلة. يغطي جميع تقنيات التصوير من الأشعة السينية إلى الرنين المغناطيسي مع حالات سريرية متنوعة لتطوير مهارات التشخيص الإشعاعي.",
        cover: "https://picsum.photos/300/400?random=11",
        rating: 4.6,
        category: "radiology",
        type: "book"
    },
    {
        id: 12,
        title: "طب النساء والتوليد",
        author: "ليون سبيروف",
        description: "مرجع شامل في طب النساء والتوليد مع تغطية كاملة للحمل والولادة وأمراض النساء. يقدم أحدث المعلومات حول الرعاية قبل الولادة والتدخلات الجراحية النسائية مع التركيز على سلامة الأم والجنين.",
        cover: "https://picsum.photos/300/400?random=12",
        rating: 4.8,
        category: "gynecology",
        type: "book"
    },
    {
        id: 13,
        title: "طب العيون الإكلينيكي",
        author: "جاك كانسكي",
        description: "دليل متخصص في طب العيون مع صور ملونة عالية الجودة للحالات المرضية. يغطي جميع أمراض العين من الجلوكوما إلى أمراض الشبكية مع أحدث تقنيات الجراحة والعلاج بالليزر.",
        cover: "https://picsum.photos/300/400?random=13",
        rating: 4.5,
        category: "ophthalmology",
        type: "book"
    },
    {
        id: 14,
        title: "طب الأنف والأذن والحنجرة",
        author: "بايرون بيلي",
        description: "مرجع شامل في طب الأنف والأذن والحنجرة مع تغطية كاملة للجراحات والعلاجات الحديثة. يقدم معلومات مفصلة عن اضطرابات السمع والتوازن وجراحات الرأس والرقبة مع تقنيات التشخيص المتقدمة.",
        cover: "https://picsum.photos/300/400?random=14",
        rating: 4.4,
        category: "ent",
        type: "book"
    },
    {
        id: 15,
        title: "طب الأمراض الجلدية",
        author: "توماس فيتزباتريك",
        description: "أطلس شامل للأمراض الجلدية مع صور ملونة عالية الدقة لجميع الحالات الجلدية. يغطي الأمراض الجلدية الشائعة والنادرة مع أحدث العلاجات الدوائية والجراحية والتجميلية.",
        cover: "https://picsum.photos/300/400?random=15",
        rating: 4.7,
        category: "dermatology",
        type: "book"
    },
    {
        id: 16,
        title: "طب الأورام السريري",
        author: "فينسنت ديفيتا",
        description: "المرجع الأساسي في علاج الأورام مع أحدث البروتوكولات العلاجية والعلاجات المناعية. يقدم معلومات شاملة عن جميع أنواع السرطان مع استراتيجيات العلاج الشخصي والطب الدقيق في علاج الأورام.",
        cover: "https://picsum.photos/300/400?random=16",
        rating: 4.9,
        category: "oncology",
        type: "book"
    },
    {
        id: 17,
        title: "طب الكلى والمسالك البولية",
        author: "آلان وين",
        description: "دليل متخصص في أمراض الكلى والمسالك البولية مع تغطية شاملة للغسيل الكلوي وزراعة الكلى. يقدم أحدث المعلومات حول أمراض الكلى المزمنة والحادة مع بروتوكولات العلاج المحدثة.",
        cover: "https://picsum.photos/300/400?random=17",
        rating: 4.6,
        category: "nephrology",
        type: "book"
    },
    {
        id: 18,
        title: "طب الروماتيزم والمفاصل",
        author: "جون كليبل",
        description: "مرجع شامل في أمراض الروماتيزم والمفاصل مع أحدث العلاجات البيولوجية. يغطي جميع أمراض المناعة الذاتية والالتهابات المفصلية مع التركيز على العلاجات الحديثة والطب التجديدي.",
        cover: "https://picsum.photos/300/400?random=18",
        rating: 4.5,
        category: "rheumatology",
        type: "book"
    },
    {
        id: 19,
        title: "طب الغدد الصماء والسكري",
        author: "شلومو ميلمد",
        description: "دليل متخصص في أمراض الغدد الصماء والسكري مع أحدث تقنيات العلاج والمراقبة. يقدم معلومات شاملة عن اضطرابات الهرمونات وإدارة السكري مع التقنيات الحديثة للمراقبة المستمرة.",
        cover: "https://picsum.photos/300/400?random=19",
        rating: 4.7,
        category: "endocrinology",
        type: "book"
    },
    {
        id: 20,
        title: "طب الأمراض المعدية",
        author: "جيرالد مانديل",
        description: "المرجع الأساسي في الأمراض المعدية مع تغطية شاملة للمضادات الحيوية ومقاومة البكتيريا. يقدم أحدث المعلومات حول الأوبئة والأمراض الناشئة مع بروتوكولات الوقاية والعلاج المحدثة.",
        cover: "https://picsum.photos/300/400?random=20",
        rating: 4.8,
        category: "infectious",
        type: "book"
    },
    {
        id: 21,
        title: "طب الأطفال المتقدم",
        author: "ريتشارد بيرمان",
        description: "مرجع متقدم في طب الأطفال مع تغطية شاملة للأمراض النادرة والحالات المعقدة. يقدم أحدث المعلومات حول نمو الأطفال والتطعيمات مع بروتوكولات العناية المركزة للأطفال.",
        cover: "https://picsum.photos/300/400?random=21",
        rating: 4.6,
        category: "pediatrics",
        type: "book"
    },
    {
        id: 22,
        title: "الطب النفسي الشامل",
        author: "بنجامين ساداك",
        description: "المرجع الأساسي في الطب النفسي مع تغطية شاملة للاضطرابات النفسية والعلاجات الحديثة. يقدم أحدث المعلومات حول العلاج النفسي والدوائي مع التركيز على الطب النفسي القائم على الأدلة.",
        cover: "https://picsum.photos/300/400?random=22",
        rating: 4.7,
        category: "psychiatry",
        type: "book"
    },
    {
        id: 23,
        title: "جراحة العظام والمفاصل",
        author: "تيري كانال",
        description: "دليل شامل في جراحة العظام مع أحدث التقنيات الجراحية والمواد الحيوية. يغطي جراحات استبدال المفاصل وإصلاح الكسور مع التركيز على التأهيل والعلاج الطبيعي بعد الجراحة.",
        cover: "https://picsum.photos/300/400?random=23",
        rating: 4.8,
        category: "orthopedics",
        type: "book"
    },
    {
        id: 24,
        title: "طب التخدير والعناية المركزة",
        author: "رونالد ميلر",
        description: "المرجع الأساسي في التخدير والعناية المركزة مع أحدث تقنيات التخدير والمراقبة. يقدم معلومات شاملة عن إدارة الألم والتخدير الموضعي مع بروتوكولات السلامة المحدثة.",
        cover: "https://picsum.photos/300/400?random=24",
        rating: 4.9,
        category: "anesthesia",
        type: "book"
    },
    {
        id: 25,
        title: "طب الأسرة والمجتمع",
        author: "روبرت تايلور",
        description: "دليل شامل في طب الأسرة مع التركيز على الرعاية الأولية والوقاية. يقدم معلومات عملية حول إدارة الأمراض المزمنة والفحوصات الدورية مع التركيز على الطب الوقائي والتثقيف الصحي.",
        cover: "https://picsum.photos/300/400?random=25",
        rating: 4.5,
        category: "family",
        type: "book"
    }
];

// Scientific resources data - Research papers and references
const resourcesData = [
    {
        id: 101,
        title: "أحدث الأبحاث في علاج السرطان",
        author: "مجموعة من الباحثين",
        description: "مجموعة شاملة من الأبحاث العلمية الحديثة في مجال علاج السرطان والعلاجات المناعية المتقدمة. تتضمن دراسات سريرية حديثة ونتائج التجارب المعملية الرائدة في تطوير علاجات جديدة للأورام المختلفة.",
        cover: "https://picsum.photos/300/400?random=101",
        rating: 4.8,
        category: "oncology",
        type: "resource"
    },
    {
        id: 102,
        title: "دراسات في الطب الجزيئي",
        author: "معهد الطب الجزيئي",
        description: "مجموعة من الدراسات المتقدمة في الطب الجزيئي والعلاج الجيني. تشمل أحدث الاكتشافات في مجال الهندسة الوراثية وتطبيقاتها في علاج الأمراض الوراثية والمكتسبة.",
        cover: "https://picsum.photos/300/400?random=102",
        rating: 4.9,
        category: "molecular",
        type: "resource"
    },
    {
        id: 103,
        title: "أبحاث الذكاء الاصطناعي في الطب",
        author: "مختبر الذكاء الاصطناعي الطبي",
        description: "دراسات رائدة في تطبيقات الذكاء الاصطناعي في التشخيص الطبي والعلاج. تتضمن أحدث التقنيات في التعلم الآلي وتحليل الصور الطبية والتشخيص المبكر للأمراض.",
        cover: "https://picsum.photos/300/400?random=103",
        rating: 4.7,
        category: "ai-medicine",
        type: "resource"
    },
    {
        id: 104,
        title: "مراجع الطب النفسي الحديث",
        author: "الجمعية الأمريكية للطب النفسي",
        description: "مجموعة شاملة من المراجع والدراسات في الطب النفسي الحديث. تغطي أحدث التطورات في فهم الاضطرابات النفسية وطرق العلاج النفسي والدوائي المتقدمة.",
        cover: "https://picsum.photos/300/400?random=104",
        rating: 4.6,
        category: "psychiatry",
        type: "resource"
    },
    {
        id: 105,
        title: "دراسات الطب الوقائي والصحة العامة",
        author: "منظمة الصحة العالمية",
        description: "مجموعة من الدراسات والتقارير في مجال الطب الوقائي والصحة العامة. تتضمن استراتيجيات الوقاية من الأمراض وتعزيز الصحة المجتمعية والسياسات الصحية الفعالة.",
        cover: "https://picsum.photos/300/400?random=105",
        rating: 4.5,
        category: "public-health",
        type: "resource"
    },
    {
        id: 106,
        title: "أبحاث الطب التجديدي والخلايا الجذعية",
        author: "معهد الطب التجديدي",
        description: "مجموعة شاملة من الأبحاث في مجال الطب التجديدي والعلاج بالخلايا الجذعية. تشمل أحدث التطورات في زراعة الأعضاء والأنسجة والعلاجات الجينية المتقدمة.",
        cover: "https://picsum.photos/300/400?random=106",
        rating: 4.8,
        category: "regenerative",
        type: "resource"
    },
    {
        id: 107,
        title: "دراسات علم الأوبئة الحديث",
        author: "مركز مكافحة الأمراض",
        description: "مجموعة من الدراسات الوبائية الحديثة مع التركيز على الأمراض الناشئة والأوبئة العالمية. تتضمن نماذج التنبؤ الوبائي واستراتيجيات الاستجابة السريعة للطوارئ الصحية.",
        cover: "https://picsum.photos/300/400?random=107",
        rating: 4.7,
        category: "epidemiology",
        type: "resource"
    },
    {
        id: 108,
        title: "أبحاث التصوير الطبي المتقدم",
        author: "الجمعية الدولية للأشعة",
        description: "مجموعة من الأبحاث في تقنيات التصوير الطبي المتقدمة. تشمل الذكاء الاصطناعي في تحليل الصور الطبية والتقنيات الجديدة في التصوير الجزيئي والوظيفي.",
        cover: "https://picsum.photos/300/400?random=108",
        rating: 4.6,
        category: "imaging",
        type: "resource"
    },
    {
        id: 109,
        title: "دراسات الطب الشخصي والجينوم",
        author: "مشروع الجينوم البشري",
        description: "مجموعة شاملة من الدراسات في الطب الشخصي والعلاج القائم على الجينوم. تتضمن تطبيقات الطب الدقيق والعلاجات المخصصة حسب التركيب الجيني للمريض.",
        cover: "https://picsum.photos/300/400?random=109",
        rating: 4.9,
        category: "genomics",
        type: "resource"
    },
    {
        id: 110,
        title: "أبحاث الطب النانوي والتقنيات الحيوية",
        author: "معهد التقنيات النانوية الطبية",
        description: "دراسات متقدمة في تطبيقات التقنيات النانوية في الطب. تشمل أنظمة توصيل الأدوية النانوية والتشخيص الجزيئي والعلاجات المستهدفة على المستوى الخلوي.",
        cover: "https://picsum.photos/300/400?random=110",
        rating: 4.7,
        category: "nanotechnology",
        type: "resource"
    },
    {
        id: 111,
        title: "دراسات الطب الرقمي والتطبيب عن بُعد",
        author: "مركز الصحة الرقمية",
        description: "مجموعة من الدراسات حول تطبيقات التكنولوجيا الرقمية في الطب. تتضمن التطبيب عن بُعد والمراقبة الصحية الذكية والسجلات الطبية الإلكترونية.",
        cover: "https://picsum.photos/300/400?random=111",
        rating: 4.5,
        category: "digital-health",
        type: "resource"
    },
    {
        id: 112,
        title: "أبحاث المناعة والعلاج المناعي",
        author: "معهد المناعة الطبية",
        description: "دراسات شاملة في علم المناعة والعلاجات المناعية الحديثة. تشمل تطوير اللقاحات والعلاجات المناعية للسرطان وأمراض المناعة الذاتية.",
        cover: "https://picsum.photos/300/400?random=112",
        rating: 4.8,
        category: "immunology",
        type: "resource"
    },
    {
        id: 113,
        title: "دراسات الطب البيئي والمهني",
        author: "منظمة الصحة المهنية",
        description: "مجموعة من الدراسات حول تأثير البيئة والعمل على الصحة. تتضمن دراسات التلوث البيئي والأمراض المهنية واستراتيجيات الوقاية في بيئات العمل المختلفة.",
        cover: "https://picsum.photos/300/400?random=113",
        rating: 4.4,
        category: "environmental",
        type: "resource"
    },
    {
        id: 114,
        title: "أبحاث طب الشيخوخة والأمراض التنكسية",
        author: "معهد الشيخوخة الصحية",
        description: "دراسات متخصصة في طب الشيخوخة والأمراض التنكسية. تشمل أحدث الأبحاث في مرض الزهايمر وباركنسون واستراتيجيات الشيخوخة الصحية والوقاية من الأمراض المرتبطة بالعمر.",
        cover: "https://picsum.photos/300/400?random=114",
        rating: 4.6,
        category: "geriatrics",
        type: "resource"
    },
    {
        id: 115,
        title: "دراسات الطب الرياضي والتأهيل",
        author: "الأكاديمية الدولية للطب الرياضي",
        description: "مجموعة شاملة من الدراسات في الطب الرياضي والتأهيل الطبي. تتضمن إصابات الرياضيين وبرامج التأهيل والعلاج الطبيعي المتقدم والتغذية الرياضية.",
        cover: "https://picsum.photos/300/400?random=115",
        rating: 4.5,
        category: "sports-medicine",
        type: "resource"
    },
    {
        id: 116,
        title: "أبحاث الطب النووي والعلاج الإشعاعي",
        author: "الوكالة الدولية للطاقة الذرية",
        description: "دراسات متقدمة في الطب النووي والعلاج الإشعاعي. تشمل تقنيات التصوير النووي الحديثة والعلاج الإشعاعي المستهدف والسلامة الإشعاعية في الممارسة الطبية.",
        cover: "https://picsum.photos/300/400?random=116",
        rating: 4.7,
        category: "nuclear-medicine",
        type: "resource"
    },
    {
        id: 117,
        title: "دراسات الطب التكاملي والطب البديل",
        author: "المركز الوطني للطب التكاملي",
        description: "مجموعة من الدراسات حول الطب التكاملي والعلاجات البديلة المبنية على الأدلة. تتضمن الطب الصيني التقليدي والعلاج بالأعشاب والوخز بالإبر والعلاجات التكميلية.",
        cover: "https://picsum.photos/300/400?random=117",
        rating: 4.3,
        category: "integrative",
        type: "resource"
    },
    {
        id: 118,
        title: "أبحاث الطب الجراحي الروبوتي",
        author: "معهد الجراحة الروبوتية",
        description: "دراسات رائدة في الجراحة الروبوتية والتقنيات الجراحية المتقدمة. تشمل تطبيقات الروبوتات في الجراحة الدقيقة والجراحة طفيفة التوغل والتدريب الجراحي الافتراضي.",
        cover: "https://picsum.photos/300/400?random=118",
        rating: 4.8,
        category: "robotic-surgery",
        type: "resource"
    },
    {
        id: 119,
        title: "دراسات الطب الجنائي والسموم",
        author: "الأكاديمية الأمريكية للطب الجنائي",
        description: "مجموعة شاملة من الدراسات في الطب الجنائي وعلم السموم. تتضمن تقنيات التشريح الجنائي والتحليل السمي والطب الجنائي الرقمي وتحديد الهوية الجنائية.",
        cover: "https://picsum.photos/300/400?random=119",
        rating: 4.4,
        category: "forensic",
        type: "resource"
    },
    {
        id: 120,
        title: "أبحاث الطب الفضائي والطيران",
        author: "وكالة الفضاء الطبية",
        description: "دراسات متخصصة في الطب الفضائي وطب الطيران. تشمل تأثيرات الجاذبية المنخفضة على الجسم البشري والتحديات الطبية في الرحلات الفضائية الطويلة والطب في البيئات القاسية.",
        cover: "https://picsum.photos/300/400?random=120",
        rating: 4.6,
        category: "aerospace",
        type: "resource"
    },
    {
        id: 121,
        title: "دراسات الطب الاجتماعي والسلوكي",
        author: "معهد الطب الاجتماعي",
        description: "مجموعة من الدراسات حول العوامل الاجتماعية والسلوكية المؤثرة على الصحة. تتضمن دراسات الصحة النفسية المجتمعية والعدالة الصحية والتدخلات السلوكية للوقاية من الأمراض.",
        cover: "https://picsum.photos/300/400?random=121",
        rating: 4.5,
        category: "social-medicine",
        type: "resource"
    },
    {
        id: 122,
        title: "أبحاث الطب الحيوي والهندسة الطبية",
        author: "معهد الهندسة الطبية الحيوية",
        description: "دراسات متقدمة في الهندسة الطبية الحيوية وتطوير الأجهزة الطبية. تشمل الأطراف الاصطناعية الذكية وأجهزة القلب المزروعة والمواد الحيوية المتوافقة والتقنيات التشخيصية المبتكرة.",
        cover: "https://picsum.photos/300/400?random=122",
        rating: 4.7,
        category: "biomedical",
        type: "resource"
    },
    {
        id: 123,
        title: "دراسات الطب المداري والأمراض المهملة",
        author: "منظمة الصحة المدارية",
        description: "مجموعة شاملة من الدراسات حول الأمراض المدارية والأمراض المهملة. تتضمن استراتيجيات مكافحة الملاريا والأمراض المنقولة بالنواقل وتطوير علاجات للأمراض النادرة في البلدان النامية.",
        cover: "https://picsum.photos/300/400?random=123",
        rating: 4.6,
        category: "tropical",
        type: "resource"
    },
    {
        id: 124,
        title: "أبحاث الطب التطوري والبيولوجيا الجزيئية",
        author: "معهد البيولوجيا التطورية",
        description: "دراسات رائدة في الطب التطوري والبيولوجيا الجزيئية. تشمل فهم تطور الأمراض والمقاومة الجينية والتطبيقات الطبية لعلم الأحياء التطوري في تطوير العلاجات الجديدة.",
        cover: "https://picsum.photos/300/400?random=124",
        rating: 4.8,
        category: "evolutionary",
        type: "resource"
    },
    {
        id: 125,
        title: "دراسات الطب الكمي والإحصاء الحيوي",
        author: "مركز الإحصاء الطبي",
        description: "مجموعة من الدراسات في الطب الكمي والإحصاء الحيوي. تتضمن تصميم التجارب السريرية وتحليل البيانات الطبية الكبيرة والنمذجة الرياضية للأمراض والتحليل الإحصائي المتقدم.",
        cover: "https://picsum.photos/300/400?random=125",
        rating: 4.5,
        category: "biostatistics",
        type: "resource"
    }
];

console.log('📚 تم تحميل بيانات الكتب المحسنة:', booksData.length, 'كتاب');

// ===== APEX 3D FLIPBOOK SYSTEM =====
// ===== APEX ENHANCED GLOBAL VARIABLES =====
let booksContainer, resourcesContainer;
let currentOpenBook = null;
let isAnimating = false;

// Pagination state
const PAGINATION_STATE = {
    books: {
        currentPage: 0,
        itemsPerPage: 10,
        totalItems: 0,
        hasMore: true
    },
    resources: {
        currentPage: 0,
        itemsPerPage: 10,
        totalItems: 0,
        hasMore: true
    }
};

// ===== APEX 3D FLIPBOOK PERFORMANCE CONFIG =====
const FLIPBOOK_CONFIG = {
    animationDuration: 800,
    pageThickness: 2,
    shadowIntensity: 0.3,
    curveIntensity: 0.15,
    rtlSupport: true,
    lazyLoading: true,
    maxVisiblePages: 4,
    preloadRadius: 2, // Pages to preload around current page
    maxConcurrentAnimations: 1,
    enableGPUAcceleration: true,
    optimizeForMobile: true,
    cacheSize: 10, // Number of books to keep in memory
    virtualScrolling: true
};

// Performance monitoring
const PERFORMANCE_MONITOR = {
    animationFrames: 0,
    lastFrameTime: 0,
    averageFPS: 60,
    memoryUsage: 0,
    isLowPerformance: false
};

// Page cache for better performance
const PAGE_CACHE = new Map();
const BOOK_CACHE = new Map();

function initializeDOMElements() {
    console.log('🔍 تهيئة عناصر DOM للنظام الجديد...');
    booksContainer = document.getElementById('books-grid');
    resourcesContainer = document.getElementById('resources-grid');

    console.log('📋 حالة عناصر DOM:');
    console.log('- حاوية الكتب:', booksContainer ? '✅ موجودة' : '❌ مفقودة');
    console.log('- حاوية المصادر:', resourcesContainer ? '✅ موجودة' : '❌ مفقودة');

    // Initialize pagination state
    PAGINATION_STATE.books.totalItems = booksData.length;
    PAGINATION_STATE.resources.totalItems = resourcesData.length;

    console.log('📊 إحصائيات المحتوى:');
    console.log('- إجمالي الكتب:', PAGINATION_STATE.books.totalItems);
    console.log('- إجمالي المصادر:', PAGINATION_STATE.resources.totalItems);
}

// ===== APEX 3D FLIPBOOK CARD CREATION =====
function create3DFlipbookCard(book) {
    console.log(`🏗️ إنشاء كتاب 3D: ${book.title}`);

    try {
        const card = document.createElement('div');
        card.className = 'flipbook-card closed';
        card.setAttribute('data-book-id', book.id);
        card.setAttribute('data-current-page', 0);
        card.setAttribute('data-total-pages', 3); // Fixed: Cover + Description + Actions = 3 pages total

        // Enhanced styling for 3D effect
        card.style.cssText = `
            width: 280px;
            height: 400px;
            position: relative;
            perspective: 1500px;
            cursor: pointer;
            margin-bottom: 3rem;
            z-index: 1;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            flex-shrink: 0;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
            transform-style: preserve-3d;
        `;

        // Create the 3D book structure
        card.innerHTML = `
            <div class="flipbook-container">
                <!-- Book Cover (Always visible when closed) -->
                <div class="flipbook-cover" data-page="0">
                    <div class="cover-front">
                        <div class="cover-image">
                            <img src="${book.cover}" alt="${book.title}" loading="lazy"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiPktpdGFiPC90ZXh0Pgo8L3N2Zz4='; this.onerror=null;">
                        </div>
                        <div class="cover-overlay">
                            <div class="book-info">
                                <h3 class="book-title">${book.title}</h3>
                                <p class="book-author">د. ${book.author}</p>
                            </div>
                            <div class="book-rating">
                                <i class="fas fa-star"></i> ${book.rating}
                            </div>
                        </div>
                    </div>
                    <div class="cover-back">
                        <div class="back-content">
                            <h4>نبذة عن الكتاب</h4>
                            <p>${book.description}</p>
                        </div>
                    </div>
                </div>

                <!-- Book Pages Container -->
                <div class="flipbook-pages">
                    ${generateBookPages(book)}
                </div>

                <!-- Book Spine -->
                <div class="flipbook-spine"></div>
            </div>

            <!-- Click hint for closed book -->
            <div class="flip-hint">
                <i class="fas fa-hand-pointer"></i>
                انقر لفتح الكتاب
            </div>

            <!-- Navigation controls -->
            <div class="flipbook-navigation">
                <button class="nav-btn" id="prev-btn-${book.id}" onclick="previousPage(${book.id})" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <span class="page-indicator" id="page-indicator-${book.id}">0 / 3</span>
                <button class="nav-btn" id="next-btn-${book.id}" onclick="nextPage(${book.id})">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="nav-btn close-btn" onclick="closeFlipbook(${book.id})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add event listeners
        setupFlipbookEvents(card, book);

        console.log(`✅ تم إنشاء كتاب 3D بنجاح: ${book.title}`);
        return card;

    } catch (error) {
        console.error(`❌ خطأ في إنشاء كتاب 3D ${book.title}:`, error);
        return null;
    }
}

// ===== APEX REALISTIC BOOK PAGES GENERATOR =====
function generateBookPages(book) {
    let pagesHTML = '';

    // First page - Book Title and Author
    pagesHTML += generateTitlePage(book);

    // Description pages (auto-split based on content length)
    pagesHTML += generateDescriptionPages(book);

    // Actions page
    pagesHTML += generateActionsPage(book);

    return pagesHTML;
}

// Generate title page with book info
function generateTitlePage(book) {
    // Calculate description length to determine if we need multiple pages
    const descriptionLength = (book.description || '').length;
    const needsMultiplePages = descriptionLength > 300; // Adjust threshold as needed

    return `
        <div class="flipbook-page" data-page="1">
            <div class="page-front">
                <div class="page-content title-page">
                    <div class="book-title-section">
                        <h1 class="main-book-title">${book.title}</h1>
                        <h2 class="book-author-name">د. ${book.author}</h2>
                    </div>
                    <div class="book-description-preview">
                        <h3>نبذة عن الكتاب</h3>
                        <p class="description-text">${truncateText(book.description, 200)}</p>
                        ${needsMultiplePages ? '<p class="read-more-hint">اضغط "التالي" لقراءة المزيد...</p>' : ''}
                    </div>
                    <!-- Internal navigation removed -->
                </div>
            </div>
            <div class="page-back">
                <div class="page-content">
                    <div class="book-metadata-detailed">
                        <h3>معلومات تفصيلية</h3>
                        <div class="metadata-grid">
                            <div class="metadata-item">
                                <span class="metadata-label">المؤلف:</span>
                                <span class="metadata-value">د. ${book.author}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">التصنيف:</span>
                                <span class="metadata-value">${getCategoryName(book.category)}</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">التقييم:</span>
                                <span class="metadata-value">${book.rating}/5 ⭐</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">نوع الملف:</span>
                                <span class="metadata-value">PDF</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">الحجم:</span>
                                <span class="metadata-value">متوسط</span>
                            </div>
                            <div class="metadata-item">
                                <span class="metadata-label">اللغة:</span>
                                <span class="metadata-value">العربية</span>
                            </div>
                        </div>
                    </div>
                    <!-- Internal navigation removed -->
                </div>
            </div>
        </div>
    `;
}

// Helper function to truncate text
function truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Enhanced description pages with better text splitting
function generateDescriptionPages(book) {
    const description = book.description || '';

    // Skip description pages if text is short (already shown in title page)
    if (description.length <= 300) {
        return '';
    }

    // More intelligent text splitting
    const maxCharsPerPage = 800; // Characters per page (more realistic)
    const pages = smartTextSplit(description, maxCharsPerPage);

    let descriptionPagesHTML = '';

    pages.forEach((pageContent, index) => {
        const pageNumber = index + 2; // Start from page 2
        const isFirstDescPage = index === 0;
        const isLastDescPage = index === pages.length - 1;

        descriptionPagesHTML += `
            <div class="flipbook-page" data-page="${pageNumber}">
                <div class="page-front">
                    <div class="page-content description-page">
                        <div class="description-content">
                            <h3>وصف الكتاب ${pages.length > 1 ? `(${index + 1}/${pages.length})` : ''}</h3>
                            <div class="description-text-content">
                                <p>${pageContent}</p>
                            </div>
                        </div>
                        <!-- Internal navigation removed -->
                    </div>
                </div>
                <div class="page-back">
                    <div class="page-content">
                        <div class="additional-info">
                            <h3>معلومات إضافية</h3>
                            <div class="book-stats">
                                <div class="stat-item">
                                    <i class="fas fa-star"></i>
                                    <span>تقييم ${book.rating} من 5</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-book"></i>
                                    <span>كتاب طبي متخصص</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-language"></i>
                                    <span>باللغة العربية</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-file-pdf"></i>
                                    <span>متاح بصيغة PDF</span>
                                </div>
                            </div>
                        </div>
                        <!-- Internal navigation removed -->
                    </div>
                </div>
            </div>
        `;
    });

    return descriptionPagesHTML;
}

// Enhanced smart text splitting function with better page management
function smartTextSplit(text, maxCharsPerPage) {
    if (!text || text.length <= maxCharsPerPage) {
        return [text];
    }

    // Adjust max chars based on actual page capacity
    const adjustedMaxChars = Math.floor(maxCharsPerPage * 0.85); // Leave some margin
    const pages = [];
    let currentPos = 0;

    while (currentPos < text.length) {
        let endPos = currentPos + adjustedMaxChars;

        // If we're not at the end of the text, try to break at natural boundaries
        if (endPos < text.length) {
            // Priority 1: Look for paragraph breaks
            let paragraphEnd = text.lastIndexOf('\n\n', endPos);
            if (paragraphEnd > currentPos + adjustedMaxChars * 0.3) {
                endPos = paragraphEnd + 2;
            } else {
                // Priority 2: Look for sentence endings
                let sentenceEnd = findBestSentenceBreak(text, currentPos, endPos);
                if (sentenceEnd > currentPos) {
                    endPos = sentenceEnd;
                } else {
                    // Priority 3: Look for word boundary
                    let wordEnd = text.lastIndexOf(' ', endPos);
                    if (wordEnd > currentPos + adjustedMaxChars * 0.6) {
                        endPos = wordEnd;
                    }
                    // If no good break point found, use the original endPos
                }
            }
        }

        const pageText = text.substring(currentPos, endPos).trim();
        if (pageText) {
            pages.push(pageText);
        }

        currentPos = endPos;

        // Skip whitespace at the beginning of next page
        while (currentPos < text.length && /\s/.test(text[currentPos])) {
            currentPos++;
        }
    }

    return pages.length > 0 ? pages : [text];
}

// Helper function to find the best sentence break point
function findBestSentenceBreak(text, startPos, maxEndPos) {
    const sentenceEnders = ['.', '!', '?', '؟', '!'];
    let bestBreak = -1;

    for (let i = maxEndPos; i > startPos + (maxEndPos - startPos) * 0.5; i--) {
        if (sentenceEnders.includes(text[i])) {
            // Check if this is likely the end of a sentence (not an abbreviation)
            if (i + 1 < text.length && /\s/.test(text[i + 1])) {
                bestBreak = i + 1;
                break;
            }
        }
    }

    return bestBreak;
}

// Enhanced truncate function with better word boundary detection
function truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) return text;

    // Find the last space before maxLength
    let truncatePos = maxLength;
    for (let i = maxLength; i > maxLength * 0.8; i--) {
        if (/\s/.test(text[i])) {
            truncatePos = i;
            break;
        }
    }

    return text.substring(0, truncatePos).trim() + '...';
}

// Generate actions page with view and download buttons
function generateActionsPage(book) {
    // Calculate the correct page number based on description length
    const description = book.description || '';
    const hasDescriptionPages = description.length > 300;
    const descriptionPagesCount = hasDescriptionPages ? smartTextSplit(description, 800).length : 0;
    const pageNumber = 2 + descriptionPagesCount; // Title page + description pages + this page

    return `
        <div class="flipbook-page" data-page="${pageNumber}">
            <div class="page-front">
                <div class="page-content actions-page">
                    <h3>إجراءات الكتاب</h3>
                    <div class="book-actions-grid">
                        <button class="action-btn view-book-btn" onclick="viewBookPDF(${book.id})">
                            <i class="fas fa-eye"></i>
                            <span>عرض الكتاب</span>
                            <small>فتح في عارض خارجي</small>
                        </button>
                        <button class="action-btn download-book-btn" onclick="downloadBookPDF(${book.id})">
                            <i class="fas fa-download"></i>
                            <span>تحميل الكتاب</span>
                            <small>تنزيل ملف PDF</small>
                        </button>
                    </div>
                    <!-- 🚫 تم حذف additional-actions حسب الطلب -->
                    <!-- Internal navigation removed -->
                </div>
            </div>
            <div class="page-back">
                <div class="page-content">
                    <div class="thank-you-section">
                        <h3>شكراً لاختيارك هذا الكتاب</h3>
                        <div class="appreciation-content">
                            <p>نتمنى أن يكون هذا الكتاب مفيداً في رحلتك التعليمية والمهنية.</p>
                            <div class="contact-info">
                                <h4>للمزيد من الكتب الطبية</h4>
                                <p>تصفح مكتبتنا الشاملة للكتب الطبية المتخصصة</p>
                            </div>
                            <div class="library-stats">
                                <div class="stat-item">
                                    <i class="fas fa-books"></i>
                                    <span>أكثر من 100 كتاب طبي</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-users"></i>
                                    <span>آلاف الطلاب والأطباء</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-award"></i>
                                    <span>محتوى عالي الجودة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Internal navigation removed -->
                </div>
            </div>
        </div>
    `;
}

// Helper function to get category name in Arabic
function getCategoryName(category) {
    const categories = {
        'anatomy': 'علم التشريح',
        'physiology': 'علم وظائف الأعضاء',
        'pathology': 'علم الأمراض',
        'pharmacology': 'علم الأدوية',
        'surgery': 'الجراحة',
        'pediatrics': 'طب الأطفال',
        'cardiology': 'أمراض القلب',
        'neurology': 'طب الأعصاب',
        'general': 'طب عام'
    };
    return categories[category] || 'طبي';
}

// ===== APEX 3D FLIPBOOK EVENT HANDLERS =====
function setupFlipbookEvents(card, book) {
    const bookId = book.id;

    // Main click event for opening/closing book
    card.addEventListener('click', function(e) {
        // Prevent action if clicking on buttons
        if (e.target.closest('.action-btn') || e.target.closest('.nav-btn')) {
            return;
        }

        e.stopPropagation();

        if (isAnimating) return;

        if (card.classList.contains('closed')) {
            openFlipbook(bookId);
        } else {
            // Check if clicking on page edge for page turn
            const rect = card.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const pageWidth = rect.width;

            if (FLIPBOOK_CONFIG.rtlSupport) {
                if (clickX < pageWidth * 0.3) {
                    previousPage(bookId);
                } else if (clickX > pageWidth * 0.7) {
                    nextPage(bookId);
                }
            } else {
                if (clickX < pageWidth * 0.3) {
                    previousPage(bookId);
                } else if (clickX > pageWidth * 0.7) {
                    nextPage(bookId);
                }
            }
        }
    });

    // Enhanced hover effects for closed book
    card.addEventListener('mouseenter', function() {
        if (card.classList.contains('closed')) {
            card.style.transform = 'translateY(-8px) rotateY(-5deg)';
            const hint = card.querySelector('.flip-hint');
            if (hint) hint.style.opacity = '1';
        }
    });

    card.addEventListener('mouseleave', function() {
        if (card.classList.contains('closed')) {
            card.style.transform = 'translateY(0) rotateY(0deg)';
            const hint = card.querySelector('.flip-hint');
            if (hint) hint.style.opacity = '0';
        }
    });

    // Page navigation events
    setupPageNavigation(card, book);

    // Advanced interactions for realistic page flipping
    setupAdvancedInteractions(card, book);

    // Page hover effects for open book
    if (card.classList.contains('open')) {
        setupPageHoverEffects(card, book);
    }
}

// Setup page hover effects for better UX
function setupPageHoverEffects(card, book) {
    const pages = card.querySelectorAll('.flipbook-page');

    pages.forEach(page => {
        page.addEventListener('mouseenter', function() {
            if (!isAnimating && card.classList.contains('open')) {
                // Subtle lift effect on hover
                this.style.transform += ' translateZ(2px)';
                this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
            }
        });

        page.addEventListener('mouseleave', function() {
            if (!isAnimating && card.classList.contains('open')) {
                // Reset to normal position
                const currentTransform = this.style.transform.replace(' translateZ(2px)', '');
                this.style.transform = currentTransform;
                this.style.boxShadow = '';
            }
        });
    });
}

function setupPageNavigation(card, book) {
    const bookId = book.id;

    // 🚫 تم إلغاء إنشاء navigation مكرر - التنقل موجود بالفعل في HTML
    // التنقل يتم إنشاؤه في create3DFlipbookCard فقط

    console.log(`✅ تم تخطي إنشاء navigation مكرر للكتاب ${bookId}`);
}

// Enhanced render books function with append mode support
function renderBooks(books, container, appendMode = false) {
    if (!container) {
        console.error('❌ الحاوية غير موجودة');
        return;
    }

    console.log(`🎨 عرض ${books.length} كتاب 3D في الحاوية:`, container.id, appendMode ? '(إلحاق)' : '(جديد)');

    // Clear container only if not in append mode
    if (!appendMode) {
        container.innerHTML = '';
    }

    if (books.length === 0 && !appendMode) {
        console.warn('⚠️ لا توجد كتب للعرض');
        container.innerHTML = `
            <div class="no-content-message">
                <div class="no-content-icon">📚</div>
                <h3>لا توجد عناصر في هذا القسم</h3>
                <p>سيتم إضافة محتوى جديد قريباً</p>
            </div>
        `;
        return;
    }

    // Track existing count for animation delay
    const existingCount = container.querySelectorAll('.flipbook-card').length;

    books.forEach((book, index) => {
        try {
            console.log(`📖 ${appendMode ? 'إضافة' : 'إنشاء'} كتاب 3D ${index + 1}: ${book.title}`);
            const bookCard = create3DFlipbookCard(book);
            if (bookCard) {
                // Add stagger animation delay for new items
                if (appendMode) {
                    bookCard.style.animationDelay = `${(index * 0.1)}s`;
                    bookCard.classList.add('new-item');
                }

                container.appendChild(bookCard);
                console.log(`✅ تم ${appendMode ? 'إضافة' : 'إنشاء'} كتاب 3D ${index + 1} بنجاح`);

                // Force visibility
                bookCard.style.display = 'block';
                bookCard.style.opacity = '1';
                bookCard.style.visibility = 'visible';

                // Remove new-item class after animation
                if (appendMode) {
                    setTimeout(() => {
                        bookCard.classList.remove('new-item');
                        bookCard.style.animationDelay = '';
                    }, 1000);
                }
            } else {
                console.error(`❌ فشل في إنشاء كتاب 3D ${index + 1}`);
            }
        } catch (error) {
            console.error(`❌ خطأ في إضافة كتاب 3D ${index + 1}:`, error);
        }
    });

    console.log(`🎉 تم عرض ${books.length} كتاب 3D بنجاح في ${container.id}`);

    // Update container layout
    updateContainerLayout(container);
}

// Update container layout for responsive grid
function updateContainerLayout(container) {
    if (!container) return;

    // Ensure proper grid layout
    container.style.display = 'grid';
    container.style.gridTemplateColumns = 'repeat(auto-fill, minmax(280px, 1fr))';
    container.style.gap = '2rem';
    container.style.padding = '1rem';
    container.style.justifyItems = 'center';
}

// ===== APEX 3D FLIPBOOK CORE FUNCTIONS =====

// Open flipbook with realistic animation
function openFlipbook(bookId) {
    if (isAnimating) return;

    console.log('📖 فتح كتاب 3D:', bookId);

    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card) return;

    // Close any other open books
    closeAllFlipbooks(bookId);

    isAnimating = true;
    currentOpenBook = bookId;

    // Set to first internal page (page 1) instead of cover (page 0)
    card.setAttribute('data-current-page', 1);

    // Add opening animation
    card.classList.remove('closed');
    card.classList.add('opening', 'open');

    // Hide hint immediately when book opens
    const hint = card.querySelector('.flip-hint');
    if (hint) {
        hint.style.opacity = '0';
        hint.style.visibility = 'hidden';
    }

    // Navigation will be shown only after moving to page 1 (not on cover)
    const navigation = card.querySelector('.flipbook-navigation');
    if (navigation) {
        navigation.style.display = 'none'; // Keep hidden on cover
    }

    // Animate cover opening
    const cover = card.querySelector('.flipbook-cover');
    if (cover) {
        setTimeout(() => {
            cover.style.transform = 'rotateY(-180deg)';
        }, 200);
    }

    // Update page indicator
    updatePageIndicator(card, 1);

    setTimeout(() => {
        card.classList.remove('opening');
        isAnimating = false;
    }, FLIPBOOK_CONFIG.animationDuration);
}

// Close flipbook
function closeFlipbook(bookId) {
    if (isAnimating) return;

    console.log('📕 إغلاق كتاب 3D:', bookId);

    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card) return;

    isAnimating = true;

    // Reset to cover
    card.setAttribute('data-current-page', 0);

    // Add closing animation
    card.classList.add('closing');
    card.classList.remove('open');

    // Hide navigation
    const navigation = card.querySelector('.flipbook-navigation');
    if (navigation) {
        navigation.style.display = 'none';
    }

    // Reset cover
    const cover = card.querySelector('.flipbook-cover');
    if (cover) {
        cover.style.transform = 'rotateY(0deg)';
    }

    // Reset all pages
    const pages = card.querySelectorAll('.flipbook-page');
    pages.forEach(page => {
        page.style.transform = 'rotateY(0deg)';
        page.style.zIndex = '';
    });

    setTimeout(() => {
        card.classList.remove('closing');
        card.classList.add('closed');
        currentOpenBook = null;
        isAnimating = false;
    }, FLIPBOOK_CONFIG.animationDuration);
}

// Close all flipbooks except the specified one
function closeAllFlipbooks(exceptId = null) {
    const openBooks = document.querySelectorAll('.flipbook-card.open');
    openBooks.forEach(book => {
        const bookId = parseInt(book.getAttribute('data-book-id'));
        if (bookId !== exceptId) {
            closeFlipbook(bookId);
        }
    });
}

// ===== APEX PROGRESSIVE LOADING SYSTEM =====

// Load initial content
function loadBooks() {
    console.log('🔄 بدء تحميل النظام المحسن...');

    // Initialize DOM elements first
    initializeDOMElements();

    // Load initial books and resources
    loadInitialBooks();
    loadInitialResources();

    console.log('🎯 انتهى تحميل النظام المحسن');
}

// Load initial books (first 10)
function loadInitialBooks() {
    if (!booksContainer) return;

    console.log('📚 تحميل الكتب الأولية...');

    const startIndex = 0;
    const endIndex = Math.min(PAGINATION_STATE.books.itemsPerPage, booksData.length);
    const booksToShow = booksData.slice(startIndex, endIndex);

    // Clear container
    booksContainer.innerHTML = '';

    // Render books
    renderBooks(booksToShow, booksContainer);

    // Update pagination state
    PAGINATION_STATE.books.currentPage = 1;
    PAGINATION_STATE.books.hasMore = endIndex < booksData.length;

    // Show/hide load more button
    updateLoadMoreButton('books');

    console.log(`✅ تم عرض ${booksToShow.length} كتاب من أصل ${booksData.length}`);
}

// Load initial resources (first 10)
function loadInitialResources() {
    if (!resourcesContainer) return;

    console.log('📖 تحميل المصادر الأولية...');

    const startIndex = 0;
    const endIndex = Math.min(PAGINATION_STATE.resources.itemsPerPage, resourcesData.length);
    const resourcesToShow = resourcesData.slice(startIndex, endIndex);

    // Clear container
    resourcesContainer.innerHTML = '';

    // Render resources
    renderBooks(resourcesToShow, resourcesContainer);

    // Update pagination state
    PAGINATION_STATE.resources.currentPage = 1;
    PAGINATION_STATE.resources.hasMore = endIndex < resourcesData.length;

    // Show/hide load more button
    updateLoadMoreButton('resources');

    console.log(`✅ تم عرض ${resourcesToShow.length} مصدر من أصل ${resourcesData.length}`);
}

// Load more books
function loadMoreBooks() {
    if (!PAGINATION_STATE.books.hasMore || isAnimating) return;

    console.log('📚 تحميل المزيد من الكتب...');

    const startIndex = PAGINATION_STATE.books.currentPage * PAGINATION_STATE.books.itemsPerPage;
    const endIndex = Math.min(startIndex + PAGINATION_STATE.books.itemsPerPage, booksData.length);
    const newBooks = booksData.slice(startIndex, endIndex);

    if (newBooks.length === 0) {
        PAGINATION_STATE.books.hasMore = false;
        updateLoadMoreButton('books');
        return;
    }

    // Show loading state
    showLoadingState('books');

    // Simulate loading delay for better UX
    setTimeout(() => {
        // Render new books
        renderBooks(newBooks, booksContainer, true); // true = append mode

        // Update pagination state
        PAGINATION_STATE.books.currentPage++;
        PAGINATION_STATE.books.hasMore = endIndex < booksData.length;

        // Update load more button
        updateLoadMoreButton('books');

        // Hide loading state
        hideLoadingState('books');

        console.log(`✅ تم إضافة ${newBooks.length} كتاب جديد`);

        // Scroll to new content
        scrollToNewContent('books');

    }, 800);
}

// Load more resources
function loadMoreResources() {
    if (!PAGINATION_STATE.resources.hasMore || isAnimating) return;

    console.log('📖 تحميل المزيد من المصادر...');

    const startIndex = PAGINATION_STATE.resources.currentPage * PAGINATION_STATE.resources.itemsPerPage;
    const endIndex = Math.min(startIndex + PAGINATION_STATE.resources.itemsPerPage, resourcesData.length);
    const newResources = resourcesData.slice(startIndex, endIndex);

    if (newResources.length === 0) {
        PAGINATION_STATE.resources.hasMore = false;
        updateLoadMoreButton('resources');
        return;
    }

    // Show loading state
    showLoadingState('resources');

    // Simulate loading delay for better UX
    setTimeout(() => {
        // Render new resources
        renderBooks(newResources, resourcesContainer, true); // true = append mode

        // Update pagination state
        PAGINATION_STATE.resources.currentPage++;
        PAGINATION_STATE.resources.hasMore = endIndex < resourcesData.length;

        // Update load more button
        updateLoadMoreButton('resources');

        // Hide loading state
        hideLoadingState('resources');

        console.log(`✅ تم إضافة ${newResources.length} مصدر جديد`);

        // Scroll to new content
        scrollToNewContent('resources');

    }, 800);
}

// Update load more button visibility and state
function updateLoadMoreButton(type) {
    const loadMoreContainer = document.getElementById(`${type}-load-more`);
    const state = PAGINATION_STATE[type];

    if (!loadMoreContainer) return;

    if (state.hasMore) {
        loadMoreContainer.style.display = 'flex';
        const button = loadMoreContainer.querySelector('.load-more-btn');
        if (button) {
            button.disabled = false;
            button.innerHTML = `
                <i class="fas fa-plus"></i>
                إظهار المزيد
            `;
        }
    } else {
        loadMoreContainer.style.display = 'none';
    }
}

// Show loading state for load more
function showLoadingState(type) {
    const loadMoreContainer = document.getElementById(`${type}-load-more`);
    if (!loadMoreContainer) return;

    const button = loadMoreContainer.querySelector('.load-more-btn');
    if (button) {
        button.disabled = true;
        button.innerHTML = `
            <div class="loading-spinner-small"></div>
            جاري التحميل...
        `;
    }
}

// Hide loading state
function hideLoadingState(type) {
    const loadMoreContainer = document.getElementById(`${type}-load-more`);
    if (!loadMoreContainer) return;

    const button = loadMoreContainer.querySelector('.load-more-btn');
    if (button) {
        button.disabled = false;
        button.innerHTML = `
            <i class="fas fa-plus"></i>
            إظهار المزيد
        `;
    }
}

// Scroll to new content
function scrollToNewContent(type) {
    const container = type === 'books' ? booksContainer : resourcesContainer;
    if (!container) return;

    const newItems = container.querySelectorAll('.flipbook-card');
    if (newItems.length > 0) {
        const lastItem = newItems[newItems.length - 1];
        lastItem.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start'
        });
    }
}

// ===== APEX 3D FLIPBOOK PAGE NAVIGATION =====

// Navigate to next page with realistic flip animation
function nextPage(bookId) {
    if (isAnimating) return;

    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card || !card.classList.contains('open')) return;

    const currentPage = parseInt(card.getAttribute('data-current-page'));
    const totalPages = parseInt(card.getAttribute('data-total-pages'));

    if (currentPage >= totalPages - 1) return; // Already at last page

    isAnimating = true;
    const nextPageNum = currentPage + 1;

    console.log(`➡️ الانتقال للصفحة ${nextPageNum} في الكتاب ${bookId}`);

    // Animate page flip
    animatePageFlip(card, currentPage, nextPageNum, 'next');

    // Update page number
    card.setAttribute('data-current-page', nextPageNum);

    // Show navigation only after page 1 (not on cover page 0)
    const navigation = card.querySelector('.flipbook-navigation');
    if (navigation && nextPageNum > 0) {
        navigation.style.display = 'flex';
        navigation.style.opacity = '1';
        navigation.style.visibility = 'visible';
    }

    // Update navigation buttons
    updateNavigationButtons(card, nextPageNum, totalPages);

    // Update page indicator
    updatePageIndicator(card, nextPageNum);

    setTimeout(() => {
        isAnimating = false;
    }, FLIPBOOK_CONFIG.animationDuration);
}

// Navigate to previous page
function previousPage(bookId) {
    if (isAnimating) return;

    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card || !card.classList.contains('open')) return;

    const currentPage = parseInt(card.getAttribute('data-current-page'));

    if (currentPage <= 0) return; // Already at first page

    isAnimating = true;
    const prevPageNum = currentPage - 1;

    console.log(`⬅️ الانتقال للصفحة ${prevPageNum} في الكتاب ${bookId}`);

    // Animate page flip
    animatePageFlip(card, currentPage, prevPageNum, 'prev');

    // Update page number
    card.setAttribute('data-current-page', prevPageNum);

    // Hide navigation if going back to cover (page 0)
    const navigation = card.querySelector('.flipbook-navigation');
    if (navigation) {
        if (prevPageNum === 0) {
            navigation.style.display = 'none';
            navigation.style.opacity = '0';
            navigation.style.visibility = 'hidden';
        } else {
            navigation.style.display = 'flex';
            navigation.style.opacity = '1';
            navigation.style.visibility = 'visible';
        }
    }

    // Update navigation buttons
    updateNavigationButtons(card, prevPageNum, parseInt(card.getAttribute('data-total-pages')));

    // Update page indicator
    updatePageIndicator(card, prevPageNum);

    setTimeout(() => {
        isAnimating = false;
    }, FLIPBOOK_CONFIG.animationDuration);
}

// ===== APEX REALISTIC PAGE FLIP ANIMATION =====
function animatePageFlip(card, fromPage, toPage, direction) {
    const pages = card.querySelectorAll('.flipbook-page');
    const cover = card.querySelector('.flipbook-cover');
    const isRTL = FLIPBOOK_CONFIG.rtlSupport;

    console.log(`🎬 تشغيل أنيميشن تقليب واقعي: من ${fromPage} إلى ${toPage} (${direction})`);

    if (direction === 'next') {
        if (fromPage === 0) {
            // Animate cover opening with realistic curve
            animateCoverFlip(cover, 'open', isRTL);
        } else {
            // Animate page flip with natural curve
            const currentPageEl = card.querySelector(`[data-page="${fromPage}"]`);
            if (currentPageEl) {
                animatePageFlipRealistic(currentPageEl, 'flip', isRTL);
            }
        }

        // Prepare next page
        if (toPage > 0) {
            const nextPageEl = card.querySelector(`[data-page="${toPage}"]`);
            if (nextPageEl) {
                setTimeout(() => {
                    nextPageEl.style.zIndex = '15';
                    nextPageEl.style.transform = 'rotateY(0deg)';
                }, FLIPBOOK_CONFIG.animationDuration / 2);
            }
        }
    } else {
        // Flip back with reverse animation
        if (toPage === 0) {
            animateCoverFlip(cover, 'close', isRTL);
        } else {
            const targetPageEl = card.querySelector(`[data-page="${toPage}"]`);
            if (targetPageEl) {
                targetPageEl.style.zIndex = '15';
                targetPageEl.style.transform = 'rotateY(0deg)';
            }
        }

        // Hide current page
        if (fromPage > 0) {
            const currentPageEl = card.querySelector(`[data-page="${fromPage}"]`);
            if (currentPageEl) {
                animatePageFlipRealistic(currentPageEl, 'flipBack', isRTL);
            }
        }
    }
}

// Animate cover with realistic opening/closing
function animateCoverFlip(cover, action, isRTL = false) {
    if (!cover) return;

    cover.classList.remove('opening', 'closing');

    if (action === 'open') {
        cover.classList.add('opening');
        const rotateAngle = isRTL ? '180deg' : '-180deg';

        setTimeout(() => {
            cover.style.transform = `rotateY(${rotateAngle})`;
            cover.style.zIndex = '1';
        }, 100);

        setTimeout(() => {
            cover.classList.remove('opening');
        }, FLIPBOOK_CONFIG.animationDuration);

    } else if (action === 'close') {
        cover.classList.add('closing');

        setTimeout(() => {
            cover.style.transform = 'rotateY(0deg)';
            cover.style.zIndex = '20';
        }, 100);

        setTimeout(() => {
            cover.classList.remove('closing');
        }, FLIPBOOK_CONFIG.animationDuration);
    }
}

// Animate individual page with natural curve
function animatePageFlipRealistic(pageEl, action, isRTL = false) {
    if (!pageEl) return;

    pageEl.classList.remove('flipping', 'flipping-back', 'flipped');

    if (action === 'flip') {
        pageEl.classList.add('flipping');
        const rotateAngle = isRTL ? '180deg' : '-180deg';

        setTimeout(() => {
            pageEl.style.transform = `rotateY(${rotateAngle})`;
            pageEl.style.zIndex = '5';
            pageEl.classList.add('flipped');
        }, FLIPBOOK_CONFIG.animationDuration / 2);

        setTimeout(() => {
            pageEl.classList.remove('flipping');
        }, FLIPBOOK_CONFIG.animationDuration);

    } else if (action === 'flipBack') {
        pageEl.classList.add('flipping-back');

        setTimeout(() => {
            pageEl.style.transform = 'rotateY(0deg)';
            pageEl.style.zIndex = '1';
            pageEl.classList.remove('flipped');
        }, FLIPBOOK_CONFIG.animationDuration / 2);

        setTimeout(() => {
            pageEl.classList.remove('flipping-back');
        }, FLIPBOOK_CONFIG.animationDuration);
    }
}

// Enhanced page flip with physics simulation
function simulatePagePhysics(pageEl, progress) {
    // Simulate natural page bend during flip
    const bendIntensity = Math.sin(progress * Math.PI) * FLIPBOOK_CONFIG.curveIntensity;
    const shadowIntensity = Math.sin(progress * Math.PI) * FLIPBOOK_CONFIG.shadowIntensity;

    // Apply bend effect
    pageEl.style.filter = `
        drop-shadow(0 ${shadowIntensity * 20}px ${shadowIntensity * 40}px rgba(0,0,0,${shadowIntensity}))
        brightness(${1 - bendIntensity * 0.1})
    `;

    // Apply subtle scale for depth perception
    const scale = 1 + bendIntensity * 0.05;
    pageEl.style.transform += ` scale(${scale})`;
}

// Update navigation buttons state
function updateNavigationButtons(card, currentPage, totalPages) {
    const prevBtn = card.querySelector('.prev-btn');
    const nextBtn = card.querySelector('.next-btn');

    if (prevBtn) {
        prevBtn.disabled = currentPage <= 0;
    }

    if (nextBtn) {
        nextBtn.disabled = currentPage >= totalPages - 1;
    }
}

// 🚫 تم حذف دالة updatePageIndicator المكررة - الدالة الصحيحة موجودة في نهاية الملف

// Scroll books function for navigation arrows
function scrollBooks(containerId, direction) {
    const container = document.getElementById(containerId);
    if (container) {
        const scrollAmount = 300;
        container.scrollBy({
            left: direction * scrollAmount,
            behavior: 'smooth'
        });
    }
}

// ===== APEX ENHANCED BOOK ACTIONS =====

// Enhanced PDF viewing function
function viewBookPDF(bookId) {
    console.log('👁️ عرض كتاب PDF:', bookId);
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    // Show loading state
    showLoadingIndicator('جاري تحضير العارض...');

    // Simulate PDF URL (in real app, this would come from your backend)
    const pdfUrl = generatePDFUrl(book);

    setTimeout(() => {
        hideLoadingIndicator();

        // Open PDF in external viewer
        try {
            // Try to open in new tab/window
            const newWindow = window.open(pdfUrl, '_blank');

            if (newWindow) {
                newWindow.focus();
                showSuccessMessage(`تم فتح كتاب "${book.title}" في عارض خارجي`);
            } else {
                // Fallback if popup blocked
                showErrorMessage('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
            }
        } catch (error) {
            console.error('خطأ في فتح PDF:', error);
            showErrorMessage('حدث خطأ في فتح الكتاب. يرجى المحاولة مرة أخرى.');
        }
    }, 1000);
}

// Enhanced PDF download function
function downloadBookPDF(bookId) {
    console.log('⬇️ تحميل كتاب PDF:', bookId);
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    // Show download progress
    showDownloadProgress(book);

    // Simulate PDF URL (in real app, this would come from your backend)
    const pdfUrl = generatePDFUrl(book);
    const fileName = `${book.title} - ${book.author}.pdf`;

    // Start download
    initiateDownload(pdfUrl, fileName, book);
}

// Generate PDF URL (placeholder - replace with real implementation)
function generatePDFUrl(book) {
    // In a real application, this would be your actual PDF URL
    // For demo purposes, using a placeholder PDF
    return `https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf`;
}

// Show loading indicator
function showLoadingIndicator(message) {
    const indicator = document.createElement('div');
    indicator.id = 'loading-indicator';
    indicator.innerHTML = `
        <div class="loading-overlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        </div>
    `;
    document.body.appendChild(indicator);
}

// Hide loading indicator
function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.remove();
    }
}

// Show download progress
function showDownloadProgress(book) {
    const progressModal = document.createElement('div');
    progressModal.id = 'download-progress';
    progressModal.innerHTML = `
        <div class="download-overlay">
            <div class="download-modal">
                <h3>تحميل الكتاب</h3>
                <div class="book-info">
                    <h4>${book.title}</h4>
                    <p>د. ${book.author}</p>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progress-percentage">0%</span>
                        <span id="progress-status">جاري التحضير...</span>
                    </div>
                </div>
                <button class="cancel-download" onclick="cancelDownload()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(progressModal);

    // Simulate download progress
    simulateDownloadProgress();
}

// Simulate download progress
function simulateDownloadProgress() {
    let progress = 0;
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');
    const progressStatus = document.getElementById('progress-status');

    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressFill.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';

        if (progress < 30) {
            progressStatus.textContent = 'جاري الاتصال بالخادم...';
        } else if (progress < 70) {
            progressStatus.textContent = 'جاري تحميل الملف...';
        } else if (progress < 100) {
            progressStatus.textContent = 'جاري إنهاء التحميل...';
        } else {
            progressStatus.textContent = 'تم التحميل بنجاح!';
            clearInterval(interval);

            setTimeout(() => {
                hideDownloadProgress();
                showSuccessMessage('تم تحميل الكتاب بنجاح!');
            }, 1000);
        }
    }, 200);
}

// Initiate actual download
function initiateDownload(url, fileName, book) {
    try {
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log(`✅ بدء تحميل: ${fileName}`);
    } catch (error) {
        console.error('خطأ في التحميل:', error);
        hideDownloadProgress();
        showErrorMessage('حدث خطأ في التحميل. يرجى المحاولة مرة أخرى.');
    }
}

// Cancel download
function cancelDownload() {
    hideDownloadProgress();
    showInfoMessage('تم إلغاء التحميل');
}

// Hide download progress
function hideDownloadProgress() {
    const progressModal = document.getElementById('download-progress');
    if (progressModal) {
        progressModal.remove();
    }
}

// Enhanced bookmark function
function toggleBookmark(bookId) {
    console.log('🔖 تبديل المفضلة:', bookId);
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    // Get current bookmarks from localStorage
    let bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');

    // Check if book is already bookmarked
    const isBookmarked = bookmarks.includes(bookId);

    if (isBookmarked) {
        // Remove from bookmarks
        bookmarks = bookmarks.filter(id => id !== bookId);
        localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
        showInfoMessage(`تم إزالة "${book.title}" من المفضلة`);
    } else {
        // Add to bookmarks
        bookmarks.push(bookId);
        localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
        showSuccessMessage(`تم إضافة "${book.title}" للمفضلة`);
    }

    // Update bookmark button appearance
    updateBookmarkButton(bookId, !isBookmarked);
}

// Update bookmark button appearance
function updateBookmarkButton(bookId, isBookmarked) {
    const bookmarkBtns = document.querySelectorAll(`[data-book-id="${bookId}"] .bookmark-btn`);
    bookmarkBtns.forEach(btn => {
        const icon = btn.querySelector('i');
        if (isBookmarked) {
            icon.className = 'fas fa-bookmark';
            btn.style.background = 'linear-gradient(135deg, #FF9800, #F57C00)';
        } else {
            icon.className = 'far fa-bookmark';
            btn.style.background = 'rgba(255, 218, 55, 0.1)';
        }
    });
}

// Enhanced share function
function shareBook(bookId) {
    console.log('📤 مشاركة الكتاب:', bookId);
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    const shareData = {
        title: book.title,
        text: `اكتشف كتاب "${book.title}" للدكتور ${book.author} - ${book.description}`,
        url: `${window.location.origin}${window.location.pathname}?book=${bookId}`
    };

    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        navigator.share(shareData)
            .then(() => showSuccessMessage('تم مشاركة الكتاب بنجاح'))
            .catch(err => console.log('خطأ في المشاركة:', err));
    } else {
        // Fallback: copy to clipboard
        const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;

        if (navigator.clipboard) {
            navigator.clipboard.writeText(shareText)
                .then(() => showSuccessMessage('تم نسخ رابط المشاركة للحافظة'))
                .catch(() => showFallbackShare(shareText));
        } else {
            showFallbackShare(shareText);
        }
    }
}

// Fallback share method
function showFallbackShare(shareText) {
    const shareModal = document.createElement('div');
    shareModal.innerHTML = `
        <div class="share-overlay">
            <div class="share-modal">
                <h3>مشاركة الكتاب</h3>
                <textarea readonly>${shareText}</textarea>
                <div class="share-buttons">
                    <button onclick="copyShareText('${shareText.replace(/'/g, "\\'")}')">
                        <i class="fas fa-copy"></i>
                        نسخ النص
                    </button>
                    <button onclick="closeShareModal()">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    `;
    shareModal.id = 'share-modal';
    document.body.appendChild(shareModal);
}

// Copy share text
function copyShareText(text) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);

    closeShareModal();
    showSuccessMessage('تم نسخ النص للحافظة');
}

// Close share modal
function closeShareModal() {
    const shareModal = document.getElementById('share-modal');
    if (shareModal) {
        shareModal.remove();
    }
}

// ===== NOTIFICATION SYSTEM =====

// Show success message
function showSuccessMessage(message) {
    showNotification(message, 'success');
}

// Show error message
function showErrorMessage(message) {
    showNotification(message, 'error');
}

// Show info message
function showInfoMessage(message) {
    showNotification(message, 'info');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="closeNotification(this)">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to notification container or create one
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);

    // Add entrance animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
}

// Get notification icon
function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'info': 'info-circle',
        'warning': 'exclamation-triangle'
    };
    return icons[type] || 'info-circle';
}

// Close notification
function closeNotification(button) {
    const notification = button.closest('.notification');
    if (notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
}

// Initialize the 3D Flipbook system with performance optimizations
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل DOM - تهيئة نظام 3D Flipbook المحسن...');
    console.log('📱 عرض الشاشة:', window.innerWidth);
    console.log('🎮 دعم GPU:', FLIPBOOK_CONFIG.enableGPUAcceleration);

    try {
        // Performance optimizations first
        enableGPUAcceleration();
        optimizeForMobile();
        startPerformanceMonitoring();

        // Initialize DOM elements
        initializeDOMElements();

        // Load 3D flipbooks
        loadBooks();

        // Setup global event listeners
        setupGlobalEvents();

        // Initialize performance features
        setTimeout(() => {
            initializeLazyLoading();
            initializeVirtualScrolling();
            setupNetworkAwareLoading();
        }, 100);

        console.log('✅ تم إكمال تهيئة نظام 3D Flipbook المحسن بنجاح');
        console.log(`📊 إعدادات الأداء: FPS المستهدف: ${PERFORMANCE_MONITOR.averageFPS}`);
    } catch (error) {
        console.error('❌ خطأ أثناء تهيئة النظام:', error);

        // Fallback: try to load books anyway
        setTimeout(function() {
            console.log('🔄 محاولة تحميل احتياطية...');
            try {
                initializeDOMElements();
                loadBooks();
                setupGlobalEvents();
            } catch (fallbackError) {
                console.error('❌ فشل التحميل الاحتياطي أيضاً:', fallbackError);
            }
        }, 1000);
    }
});

// ===== APEX ADVANCED INTERACTION SYSTEM =====

// Enhanced touch and mouse interaction for realistic page flipping
function setupAdvancedInteractions(card, book) {
    const bookId = book.id;
    let isDragging = false;
    let startX = 0;
    let currentX = 0;
    let dragProgress = 0;

    // Mouse/Touch events for page dragging
    card.addEventListener('mousedown', handleDragStart);
    card.addEventListener('touchstart', handleDragStart, { passive: false });

    function handleDragStart(e) {
        if (!card.classList.contains('open')) return;

        const clientX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
        const rect = card.getBoundingClientRect();
        const relativeX = clientX - rect.left;

        // Only start drag if near page edge (for realistic page turning)
        if (FLIPBOOK_CONFIG.rtlSupport) {
            if (relativeX < rect.width * 0.3) {
                startDrag(clientX);
            }
        } else {
            if (relativeX > rect.width * 0.7) {
                startDrag(clientX);
            }
        }
    }

    function startDrag(clientX) {
        isDragging = true;
        startX = clientX;
        currentX = clientX;

        document.addEventListener('mousemove', handleDragMove);
        document.addEventListener('touchmove', handleDragMove, { passive: false });
        document.addEventListener('mouseup', handleDragEnd);
        document.addEventListener('touchend', handleDragEnd);

        card.style.cursor = 'grabbing';
    }

    function handleDragMove(e) {
        if (!isDragging) return;

        e.preventDefault();
        currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
        const deltaX = currentX - startX;
        const rect = card.getBoundingClientRect();

        // Calculate drag progress (0 to 1)
        dragProgress = Math.abs(deltaX) / (rect.width * 0.5);
        dragProgress = Math.min(dragProgress, 1);

        // Apply real-time page bend effect
        applyRealtimePageBend(card, dragProgress, deltaX > 0);
    }

    function handleDragEnd() {
        if (!isDragging) return;

        isDragging = false;
        document.removeEventListener('mousemove', handleDragMove);
        document.removeEventListener('touchmove', handleDragMove);
        document.removeEventListener('mouseup', handleDragEnd);
        document.removeEventListener('touchend', handleDragEnd);

        card.style.cursor = '';

        // Determine if page should flip based on drag distance
        if (dragProgress > 0.3) {
            // Complete the page flip
            const currentPage = parseInt(card.getAttribute('data-current-page'));
            const deltaX = currentX - startX;

            if (FLIPBOOK_CONFIG.rtlSupport) {
                if (deltaX > 0) {
                    previousPage(bookId);
                } else {
                    nextPage(bookId);
                }
            } else {
                if (deltaX > 0) {
                    nextPage(bookId);
                } else {
                    previousPage(bookId);
                }
            }
        } else {
            // Snap back to original position
            resetPagePosition(card);
        }
    }
}

// Apply real-time page bend effect during drag
function applyRealtimePageBend(card, progress, isForward) {
    const currentPage = parseInt(card.getAttribute('data-current-page'));
    const pageEl = card.querySelector(`[data-page="${currentPage}"]`);

    if (!pageEl) return;

    const angle = progress * (isForward ? -90 : 90);
    const scale = 1 + progress * 0.05;
    const shadowIntensity = progress * 0.3;

    pageEl.style.transform = `rotateY(${angle}deg) scale(${scale})`;
    pageEl.style.filter = `drop-shadow(0 ${shadowIntensity * 20}px ${shadowIntensity * 40}px rgba(0,0,0,${shadowIntensity}))`;

    // Add bend shadow
    const bendShadow = pageEl.querySelector('.bend-shadow') || document.createElement('div');
    if (!pageEl.querySelector('.bend-shadow')) {
        bendShadow.className = 'bend-shadow';
        bendShadow.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
            opacity: 0;
            pointer-events: none;
            border-radius: 6px;
        `;
        pageEl.appendChild(bendShadow);
    }

    bendShadow.style.opacity = progress;
}

// Reset page to original position
function resetPagePosition(card) {
    const currentPage = parseInt(card.getAttribute('data-current-page'));
    const pageEl = card.querySelector(`[data-page="${currentPage}"]`);

    if (pageEl) {
        pageEl.style.transform = 'rotateY(0deg) scale(1)';
        pageEl.style.filter = '';

        const bendShadow = pageEl.querySelector('.bend-shadow');
        if (bendShadow) {
            bendShadow.style.opacity = '0';
        }
    }
}

// Setup global event listeners
function setupGlobalEvents() {
    // Close books when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.flipbook-card') && currentOpenBook) {
            closeFlipbook(currentOpenBook);
        }
    });

    // Enhanced keyboard navigation with page preview
    document.addEventListener('keydown', function(e) {
        if (!currentOpenBook) return;

        switch(e.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                if (FLIPBOOK_CONFIG.rtlSupport) {
                    nextPage(currentOpenBook);
                } else {
                    previousPage(currentOpenBook);
                }
                break;
            case 'ArrowRight':
            case 'ArrowDown':
                e.preventDefault();
                if (FLIPBOOK_CONFIG.rtlSupport) {
                    previousPage(currentOpenBook);
                } else {
                    nextPage(currentOpenBook);
                }
                break;
            case 'Escape':
                e.preventDefault();
                closeFlipbook(currentOpenBook);
                break;
            case 'Home':
                e.preventDefault();
                goToPage(currentOpenBook, 0);
                break;
            case 'End':
                e.preventDefault();
                const card = document.querySelector(`[data-book-id="${currentOpenBook}"]`);
                if (card) {
                    const totalPages = parseInt(card.getAttribute('data-total-pages'));
                    goToPage(currentOpenBook, totalPages - 1);
                }
                break;
        }
    });

    // Mouse wheel for page navigation
    document.addEventListener('wheel', function(e) {
        if (!currentOpenBook) return;

        const card = document.querySelector(`[data-book-id="${currentOpenBook}"]`);
        if (!card || !card.contains(e.target)) return;

        e.preventDefault();

        if (e.deltaY > 0) {
            // Scroll down - next page
            if (FLIPBOOK_CONFIG.rtlSupport) {
                previousPage(currentOpenBook);
            } else {
                nextPage(currentOpenBook);
            }
        } else {
            // Scroll up - previous page
            if (FLIPBOOK_CONFIG.rtlSupport) {
                nextPage(currentOpenBook);
            } else {
                previousPage(currentOpenBook);
            }
        }
    }, { passive: false });
}

// Go to specific page
function goToPage(bookId, targetPage) {
    if (isAnimating) return;

    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card) return;

    const currentPage = parseInt(card.getAttribute('data-current-page'));
    const totalPages = parseInt(card.getAttribute('data-total-pages'));

    if (targetPage < 0 || targetPage >= totalPages || targetPage === currentPage) return;

    console.log(`🎯 الانتقال المباشر للصفحة ${targetPage} في الكتاب ${bookId}`);

    // Animate through pages quickly
    const direction = targetPage > currentPage ? 'next' : 'prev';
    const steps = Math.abs(targetPage - currentPage);

    let currentStep = 0;
    const stepInterval = setInterval(() => {
        if (currentStep >= steps) {
            clearInterval(stepInterval);
            return;
        }

        if (direction === 'next') {
            nextPage(bookId);
        } else {
            previousPage(bookId);
        }

        currentStep++;
    }, FLIPBOOK_CONFIG.animationDuration / 4);
}

// ===== APEX PERFORMANCE OPTIMIZATION SYSTEM =====

// Lazy loading system for pages
function initializeLazyLoading() {
    if (!FLIPBOOK_CONFIG.lazyLoading) return;

    console.log('🚀 تهيئة نظام التحميل التدريجي...');

    // Intersection Observer for lazy loading
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const bookCard = entry.target;
                const bookId = bookCard.getAttribute('data-book-id');

                if (bookId && !BOOK_CACHE.has(bookId)) {
                    preloadBookContent(bookId);
                }
            }
        });
    }, {
        rootMargin: '100px',
        threshold: 0.1
    });

    // Observe all flipbook cards
    document.querySelectorAll('.flipbook-card').forEach(card => {
        observer.observe(card);
    });
}

// Preload book content progressively
function preloadBookContent(bookId) {
    const book = booksData.find(b => b.id == bookId);
    if (!book || BOOK_CACHE.has(bookId)) return;

    console.log(`📖 تحميل تدريجي للكتاب: ${book.title}`);

    // Cache book data
    BOOK_CACHE.set(bookId, {
        book: book,
        loadedPages: new Set(),
        preloadedImages: new Map(),
        lastAccessed: Date.now()
    });

    // Preload cover image
    preloadImage(book.cover, `book-${bookId}-cover`);

    // Manage cache size
    manageCacheSize();
}

// Preload images with caching
function preloadImage(src, cacheKey) {
    if (PAGE_CACHE.has(cacheKey)) return Promise.resolve();

    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
            PAGE_CACHE.set(cacheKey, img);
            resolve(img);
        };
        img.onerror = reject;
        img.src = src;
    });
}

// Manage cache size to prevent memory leaks
function manageCacheSize() {
    if (BOOK_CACHE.size <= FLIPBOOK_CONFIG.cacheSize) return;

    // Remove least recently used books
    const sortedBooks = Array.from(BOOK_CACHE.entries())
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    const toRemove = sortedBooks.slice(0, sortedBooks.length - FLIPBOOK_CONFIG.cacheSize);
    toRemove.forEach(([bookId]) => {
        BOOK_CACHE.delete(bookId);
        console.log(`🗑️ إزالة كتاب من الذاكرة: ${bookId}`);
    });
}

// Performance monitoring
function startPerformanceMonitoring() {
    let frameCount = 0;
    let lastTime = performance.now();

    function measureFPS() {
        const currentTime = performance.now();
        frameCount++;

        if (currentTime - lastTime >= 1000) {
            PERFORMANCE_MONITOR.averageFPS = frameCount;
            PERFORMANCE_MONITOR.isLowPerformance = frameCount < 30;

            // Adjust quality based on performance
            if (PERFORMANCE_MONITOR.isLowPerformance) {
                adjustQualityForPerformance();
            }

            frameCount = 0;
            lastTime = currentTime;
        }

        requestAnimationFrame(measureFPS);
    }

    requestAnimationFrame(measureFPS);
}

// Adjust quality based on device performance
function adjustQualityForPerformance() {
    if (PERFORMANCE_MONITOR.isLowPerformance) {
        console.log('⚡ تحسين الأداء: تقليل جودة التأثيرات');

        // Reduce animation duration for better performance
        FLIPBOOK_CONFIG.animationDuration = Math.max(400, FLIPBOOK_CONFIG.animationDuration * 0.7);

        // Disable some effects on low-end devices
        document.documentElement.style.setProperty('--animation-duration', `${FLIPBOOK_CONFIG.animationDuration}ms`);

        // Reduce shadow intensity
        FLIPBOOK_CONFIG.shadowIntensity *= 0.5;
    }
}

// GPU acceleration optimization
function enableGPUAcceleration() {
    if (!FLIPBOOK_CONFIG.enableGPUAcceleration) return;

    const style = document.createElement('style');
    style.textContent = `
        .flipbook-card,
        .flipbook-container,
        .flipbook-cover,
        .flipbook-page {
            will-change: transform;
            transform: translateZ(0);
            backface-visibility: hidden;
        }
    `;
    document.head.appendChild(style);

    console.log('🎮 تم تفعيل تسريع GPU');
}

// ===== APEX ADVANCED DEVICE OPTIMIZATION =====

// Comprehensive device detection and optimization
function optimizeForMobile() {
    if (!FLIPBOOK_CONFIG.optimizeForMobile) return;

    const deviceInfo = detectDevice();
    console.log('📱 معلومات الجهاز:', deviceInfo);

    // Apply device-specific optimizations
    applyDeviceOptimizations(deviceInfo);

    // Setup device-specific event handlers
    setupDeviceSpecificEvents(deviceInfo);
}

// Advanced device detection
function detectDevice() {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;
    const maxTouchPoints = navigator.maxTouchPoints || 0;
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const pixelRatio = window.devicePixelRatio || 1;

    const device = {
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
        isTablet: /iPad|Android(?=.*Mobile)/i.test(userAgent) && screenWidth >= 768,
        isTouch: 'ontouchstart' in window || maxTouchPoints > 0,
        isIOS: /iPad|iPhone|iPod/.test(userAgent),
        isAndroid: /Android/.test(userAgent),
        isLowEnd: screenWidth < 480 || (performance.memory && performance.memory.jsHeapSizeLimit < 1073741824),
        hasHighDPI: pixelRatio >= 2,
        screenSize: {
            width: screenWidth,
            height: screenHeight,
            ratio: pixelRatio
        },
        orientation: screenWidth > screenHeight ? 'landscape' : 'portrait',
        supportsWebGL: !!window.WebGLRenderingContext,
        supportsIntersectionObserver: !!window.IntersectionObserver,
        connectionSpeed: navigator.connection ? navigator.connection.effectiveType : 'unknown'
    };

    // Determine device category
    if (device.isMobile && screenWidth < 480) {
        device.category = 'small-phone';
    } else if (device.isMobile && screenWidth < 768) {
        device.category = 'large-phone';
    } else if (device.isTablet) {
        device.category = 'tablet';
    } else {
        device.category = 'desktop';
    }

    return device;
}

// Apply optimizations based on device capabilities
function applyDeviceOptimizations(device) {
    console.log(`🎯 تطبيق تحسينات للجهاز: ${device.category}`);

    // Performance adjustments based on device
    if (device.isLowEnd || device.connectionSpeed === 'slow-2g' || device.connectionSpeed === '2g') {
        console.log('⚡ جهاز منخفض الأداء - تطبيق تحسينات قوية');
        FLIPBOOK_CONFIG.animationDuration = 400;
        FLIPBOOK_CONFIG.shadowIntensity = 0.1;
        FLIPBOOK_CONFIG.curveIntensity = 0.05;
        FLIPBOOK_CONFIG.maxVisiblePages = 2;
        FLIPBOOK_CONFIG.preloadRadius = 1;
    } else if (device.category === 'small-phone') {
        FLIPBOOK_CONFIG.animationDuration = 500;
        FLIPBOOK_CONFIG.shadowIntensity *= 0.6;
        FLIPBOOK_CONFIG.curveIntensity *= 0.7;
    } else if (device.category === 'large-phone' || device.category === 'tablet') {
        FLIPBOOK_CONFIG.animationDuration = 600;
        FLIPBOOK_CONFIG.shadowIntensity *= 0.8;
        FLIPBOOK_CONFIG.curveIntensity *= 0.9;
    }

    // Add device-specific CSS
    const deviceStyle = document.createElement('style');
    deviceStyle.id = 'device-optimizations';

    let cssRules = '';

    if (device.isTouch) {
        cssRules += `
            .flipbook-card {
                cursor: default !important;
            }

            .nav-btn, .action-btn {
                min-height: 44px !important;
                min-width: 44px !important;
            }

            .flip-hint {
                display: none !important;
            }
        `;
    }

    if (device.isLowEnd) {
        cssRules += `
            .flipbook-card,
            .flipbook-page {
                will-change: auto !important;
                transform: none !important;
            }

            .flipbook-page::before,
            .flipbook-page::after {
                display: none !important;
            }

            .page-content::before {
                display: none !important;
            }
        `;
    }

    if (device.category === 'small-phone') {
        cssRules += `
            .flipbook-card {
                margin: 0.5rem !important;
            }

            .page-content {
                padding: 12px !important;
                font-size: 0.75rem !important;
            }

            .chapter-content {
                line-height: 1.3 !important;
            }
        `;
    }

    if (device.hasHighDPI) {
        cssRules += `
            .cover-image img {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        `;
    }

    deviceStyle.textContent = cssRules;
    document.head.appendChild(deviceStyle);
}

// Setup device-specific event handlers
function setupDeviceSpecificEvents(device) {
    if (device.isTouch) {
        // Enhanced touch handling
        document.addEventListener('touchstart', function(e) {
            // Prevent zoom on double tap for flipbook cards
            if (e.target.closest('.flipbook-card')) {
                e.preventDefault();
            }
        }, { passive: false });

        // Handle orientation changes
        window.addEventListener('orientationchange', debounce(() => {
            console.log('📱 تغيير اتجاه الشاشة');
            setTimeout(() => {
                adjustLayoutForOrientation();
            }, 100);
        }, 250));
    }

    // Handle window resize for responsive adjustments
    window.addEventListener('resize', throttle(() => {
        adjustLayoutForViewport();
    }, 100));

    // Handle visibility changes for performance
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            pauseAnimations();
        } else {
            resumeAnimations();
        }
    });
}

// Adjust layout for orientation changes
function adjustLayoutForOrientation() {
    const cards = document.querySelectorAll('.flipbook-card');
    cards.forEach(card => {
        // Force reflow to handle orientation change
        card.style.display = 'none';
        card.offsetHeight; // Trigger reflow
        card.style.display = '';
    });
}

// Adjust layout for viewport changes
function adjustLayoutForViewport() {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Adjust grid layout based on viewport
    const grids = document.querySelectorAll('.books-grid');
    grids.forEach(grid => {
        const cardWidth = 280; // Base card width
        const cardsPerRow = Math.floor(viewportWidth / (cardWidth + 40)); // 40px for margins

        if (cardsPerRow < 2) {
            grid.style.justifyContent = 'center';
        } else {
            grid.style.justifyContent = 'flex-start';
        }
    });
}

// Pause animations for performance when tab is hidden
function pauseAnimations() {
    const style = document.createElement('style');
    style.id = 'pause-animations';
    style.textContent = `
        *, *::before, *::after {
            animation-play-state: paused !important;
            transition: none !important;
        }
    `;
    document.head.appendChild(style);
}

// Resume animations when tab becomes visible
function resumeAnimations() {
    const pauseStyle = document.getElementById('pause-animations');
    if (pauseStyle) {
        pauseStyle.remove();
    }
}

// Network-aware loading
function setupNetworkAwareLoading() {
    if ('connection' in navigator) {
        const connection = navigator.connection;

        connection.addEventListener('change', () => {
            const effectiveType = connection.effectiveType;
            console.log(`🌐 تغيير سرعة الاتصال: ${effectiveType}`);

            if (effectiveType === 'slow-2g' || effectiveType === '2g') {
                // Reduce quality for slow connections
                FLIPBOOK_CONFIG.lazyLoading = true;
                FLIPBOOK_CONFIG.preloadRadius = 1;
                FLIPBOOK_CONFIG.animationDuration = 300;
            } else if (effectiveType === '4g') {
                // Increase quality for fast connections
                FLIPBOOK_CONFIG.preloadRadius = 3;
                FLIPBOOK_CONFIG.animationDuration = 800;
            }
        });
    }
}

// Debounce function for performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for performance
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Virtual scrolling for large book collections
function initializeVirtualScrolling() {
    if (!FLIPBOOK_CONFIG.virtualScrolling) return;

    const containers = document.querySelectorAll('.books-grid');

    containers.forEach(container => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.visibility = 'visible';
                    entry.target.style.opacity = '1';
                } else {
                    // Keep in DOM but reduce visibility for performance
                    entry.target.style.opacity = '0.1';
                }
            });
        }, {
            rootMargin: '200px',
            threshold: 0
        });

        container.querySelectorAll('.flipbook-card').forEach(card => {
            observer.observe(card);
        });
    });
}

// ===== APEX FINAL ENHANCEMENTS =====

// Add loading states and error handling
function addLoadingState(card) {
    card.classList.add('loading');
}

function removeLoadingState(card) {
    card.classList.remove('loading');
}

function addErrorState(card) {
    card.classList.add('error');
}

function addSuccessState(card) {
    card.classList.add('success');
    setTimeout(() => {
        card.classList.remove('success');
    }, 600);
}

// Enhanced error handling
function handleBookError(bookId, error) {
    console.error(`❌ خطأ في الكتاب ${bookId}:`, error);
    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (card) {
        removeLoadingState(card);
        addErrorState(card);
    }
}

// Enhanced success feedback
function handleBookSuccess(bookId, action) {
    console.log(`✅ نجح ${action} للكتاب ${bookId}`);
    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (card) {
        removeLoadingState(card);
        addSuccessState(card);
    }
}

// Add page turn sound effect (visual indicator)
function addPageTurnEffect(pageEl) {
    if (pageEl) {
        pageEl.classList.add('turning');
        setTimeout(() => {
            pageEl.classList.remove('turning');
        }, 500);
    }
}

// Enhanced book opening with loading state
function openFlipbookEnhanced(bookId) {
    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card) return;

    addLoadingState(card);

    try {
        openFlipbook(bookId);
        setTimeout(() => {
            handleBookSuccess(bookId, 'فتح الكتاب');
        }, FLIPBOOK_CONFIG.animationDuration);
    } catch (error) {
        handleBookError(bookId, error);
    }
}

// Enhanced page navigation with effects
function nextPageEnhanced(bookId) {
    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card) return;

    const currentPage = parseInt(card.getAttribute('data-current-page'));
    const pageEl = card.querySelector(`[data-page="${currentPage}"]`);

    addPageTurnEffect(pageEl);
    nextPage(bookId);
}

// Go back to cover (close book)
function goToCover(bookId) {
    console.log('🔙 العودة للغلاف:', bookId);
    closeFlipbook(bookId);
}

// Update page indicator and navigation buttons
function updatePageIndicator(card, currentPage) {
    const totalPages = parseInt(card.getAttribute('data-total-pages'));
    const bookId = card.getAttribute('data-book-id');

    // 🎯 استخدام الـ ID الصحيح للعثور على page-indicator الصحيح
    const pageIndicator = card.querySelector(`#page-indicator-${bookId}`);
    const prevBtn = card.querySelector(`#prev-btn-${bookId}`);
    const nextBtn = card.querySelector(`#next-btn-${bookId}`);

    if (pageIndicator) {
        pageIndicator.textContent = `${currentPage} / ${totalPages}`;
        console.log(`📄 تحديث مؤشر الصفحة للكتاب ${bookId}: ${currentPage} / ${totalPages}`);
    } else {
        console.warn(`⚠️ لم يتم العثور على page-indicator للكتاب ${bookId}`);
    }

    if (prevBtn) {
        prevBtn.disabled = currentPage <= 1;
    }

    if (nextBtn) {
        nextBtn.disabled = currentPage >= totalPages;
    }
}

function previousPageEnhanced(bookId) {
    const card = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!card) return;

    const currentPage = parseInt(card.getAttribute('data-current-page'));
    const pageEl = card.querySelector(`[data-page="${currentPage}"]`);

    addPageTurnEffect(pageEl);
    previousPage(bookId);
}

// Quality assurance checks
function performQualityChecks() {
    console.log('🔍 إجراء فحوصات الجودة...');

    const issues = [];

    // Check for missing images
    const images = document.querySelectorAll('.flipbook-card img');
    images.forEach((img, index) => {
        if (!img.complete || img.naturalWidth === 0) {
            issues.push(`صورة ${index + 1}: فشل في التحميل`);
        }
    });

    // Check for proper CSS loading
    const testEl = document.createElement('div');
    testEl.className = 'flipbook-card';
    document.body.appendChild(testEl);
    const styles = window.getComputedStyle(testEl);

    if (styles.perspective === 'none') {
        issues.push('CSS: لم يتم تحميل أنماط 3D بشكل صحيح');
    }

    document.body.removeChild(testEl);

    // Check for JavaScript errors
    if (typeof FLIPBOOK_CONFIG === 'undefined') {
        issues.push('JavaScript: إعدادات النظام غير محملة');
    }

    if (issues.length > 0) {
        console.warn('⚠️ مشاكل في الجودة:', issues);
    } else {
        console.log('✅ جميع فحوصات الجودة نجحت');
    }

    return issues;
}

// Initialize quality monitoring
function initializeQualityMonitoring() {
    // Check quality on load
    setTimeout(performQualityChecks, 1000);

    // Monitor for runtime issues
    window.addEventListener('error', (e) => {
        console.error('❌ خطأ في وقت التشغيل:', e.error);
    });

    // Monitor for unhandled promise rejections
    window.addEventListener('unhandledrejection', (e) => {
        console.error('❌ Promise مرفوض:', e.reason);
    });
}

// Final system status
function reportSystemStatus() {
    const status = {
        version: 'APEX 3D Flipbook v1.0',
        features: {
            '3D_transforms': true,
            'realistic_page_flip': true,
            'rtl_support': FLIPBOOK_CONFIG.rtlSupport,
            'lazy_loading': FLIPBOOK_CONFIG.lazyLoading,
            'performance_optimization': true,
            'responsive_design': true,
            'accessibility': true,
            'touch_support': 'ontouchstart' in window,
            'gpu_acceleration': FLIPBOOK_CONFIG.enableGPUAcceleration
        },
        performance: {
            average_fps: PERFORMANCE_MONITOR.averageFPS,
            memory_usage: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB' : 'غير متاح',
            is_low_performance: PERFORMANCE_MONITOR.isLowPerformance
        },
        books_loaded: document.querySelectorAll('.flipbook-card').length,
        cache_size: BOOK_CACHE.size
    };

    console.log('📊 حالة النظام:', status);
    window.FLIPBOOK_SYSTEM_STATUS = status;

    return status;
}

console.log('✅ تم تحميل نظام 3D Flipbook بنجاح - APEX Edition مع تحسينات الأداء');
console.log('🎯 النظام جاهز للاستخدام مع أعلى مستويات الجودة والواقعية');

// Initialize quality monitoring
initializeQualityMonitoring();

// Report final status
setTimeout(reportSystemStatus, 2000);

// ===== CLICK OUTSIDE TO CLOSE FUNCTIONALITY =====

// Add click outside to close functionality
function addClickOutsideToClose() {
    document.addEventListener('click', function(event) {
        // Check if there's an open book
        if (!currentOpenBook) return;

        const openCard = document.querySelector(`[data-book-id="${currentOpenBook}"]`);
        if (!openCard || !openCard.classList.contains('open')) return;

        // Check if click is outside the open book
        if (!openCard.contains(event.target)) {
            console.log('🔄 إغلاق الكتاب بالنقر خارجه');
            closeFlipbook(currentOpenBook);
        }
    });

    // Prevent closing when clicking on book cards that are not open
    document.addEventListener('click', function(event) {
        const bookCard = event.target.closest('.flipbook-card');
        if (bookCard && !bookCard.classList.contains('open')) {
            event.stopPropagation();
        }
    });
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة نظام 3D Flipbook...');

    // Load books
    loadBooks();

    // Add scroll effects
    addScrollEffects();

    // Add click outside to close functionality
    addClickOutsideToClose();

    console.log('✅ تم تهيئة النظام بنجاح');
});
