// Minimal Medical Library JavaScript for Testing

console.log('🚀 تحميل script-minimal.js...');

// Simple books data
const booksData = [
    {
        id: 1,
        title: "أطلس نيتر للتشريح البشري",
        author: "فرانك نيتر",
        description: "المرجع الأشهر في التشريح البشري",
        cover: "https://picsum.photos/300/400?random=1",
        rating: 4.9,
        featured: true,
        popular: true
    },
    {
        id: 2,
        title: "علم وظائف الأعضاء الطبي",
        author: "آرثر جايتون",
        description: "الكتاب الأساسي في علم وظائف الأعضاء",
        cover: "https://picsum.photos/300/400?random=2",
        rating: 4.8,
        featured: true,
        popular: false
    },
    {
        id: 3,
        title: "علم الأمراض الأساسي",
        author: "فينيش كومار",
        description: "دليل شامل لفهم الأمراض",
        cover: "https://picsum.photos/300/400?random=3",
        rating: 4.7,
        featured: false,
        popular: true
    }
];

console.log('📚 تم تحميل بيانات الكتب:', booksData.length);

// Create simple book card
function createSimpleBookCard(book) {
    console.log('🏗️ إنشاء كتاب:', book.title);
    
    const card = document.createElement('div');
    card.className = 'book-card';
    card.style.cssText = `
        width: 280px;
        height: 400px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 218, 55, 0.3);
        border-radius: 10px;
        padding: 15px;
        margin: 10px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        text-align: center;
        cursor: pointer;
        transition: transform 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    `;
    
    card.innerHTML = `
        <div style="width: 100%; height: 250px; background: #333; border-radius: 8px; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; color: #ffda37; font-size: 18px;">
            📖 ${book.title.substring(0, 20)}...
        </div>
        <h3 style="color: #ffda37; margin: 0 0 10px 0; font-size: 16px; font-weight: bold; line-height: 1.3;">
            ${book.title}
        </h3>
        <p style="color: rgba(255, 255, 255, 0.8); margin: 0 0 10px 0; font-size: 14px;">
            ${book.author}
        </p>
        <p style="color: rgba(255, 255, 255, 0.6); margin: 0 0 15px 0; font-size: 12px; flex: 1;">
            ${book.description}
        </p>
        <div style="color: #ffda37; font-size: 14px; font-weight: bold;">
            ⭐ ${book.rating}
        </div>
    `;
    
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
    
    console.log('✅ تم إنشاء كتاب:', book.title);
    return card;
}

// Render books
function renderBooks(books, container) {
    console.log('🎨 عرض الكتب في:', container.id);
    console.log('📊 عدد الكتب:', books.length);
    
    if (!container) {
        console.error('❌ الحاوية غير موجودة');
        return;
    }
    
    // Clear container
    container.innerHTML = '';
    
    if (books.length === 0) {
        container.innerHTML = `
            <div style="
                width: 100%; 
                padding: 40px; 
                text-align: center; 
                color: #ff6b6b; 
                font-size: 18px;
                background: rgba(255, 107, 107, 0.1);
                border: 2px dashed #ff6b6b;
                border-radius: 10px;
            ">
                ⚠️ لا توجد كتب في هذا القسم
            </div>
        `;
        return;
    }
    
    // Add books
    books.forEach((book, index) => {
        try {
            console.log(`📖 إضافة كتاب ${index + 1}:`, book.title);
            const bookCard = createSimpleBookCard(book);
            container.appendChild(bookCard);
            console.log(`✅ تم إضافة كتاب ${index + 1} بنجاح`);
        } catch (error) {
            console.error(`❌ خطأ في إضافة كتاب ${index + 1}:`, error);
        }
    });
    
    console.log(`🎉 تم عرض ${books.length} كتاب في ${container.id}`);
}

// Load books
function loadBooks() {
    console.log('🔄 بدء تحميل الكتب...');
    
    // Get containers
    const featuredContainer = document.getElementById('featured-books');
    const popularContainer = document.getElementById('popular-books');
    const recentContainer = document.getElementById('recent-books');
    
    console.log('📦 الحاويات:');
    console.log('- المميزة:', featuredContainer ? '✅ موجودة' : '❌ مفقودة');
    console.log('- الشائعة:', popularContainer ? '✅ موجودة' : '❌ مفقودة');
    console.log('- الحديثة:', recentContainer ? '✅ موجودة' : '❌ مفقودة');
    
    // Filter books
    const featuredBooks = booksData.filter(book => book.featured);
    const popularBooks = booksData.filter(book => book.popular);
    const recentBooks = booksData.slice(-2); // Last 2 books
    
    console.log('📊 إحصائيات:');
    console.log('- كتب مميزة:', featuredBooks.length);
    console.log('- كتب شائعة:', popularBooks.length);
    console.log('- كتب حديثة:', recentBooks.length);
    
    // Render books
    if (featuredContainer) {
        renderBooks(featuredBooks, featuredContainer);
    }
    
    if (popularContainer) {
        renderBooks(popularBooks, popularContainer);
    }
    
    if (recentContainer) {
        renderBooks(recentBooks, recentContainer);
    }
    
    console.log('🎯 انتهى تحميل الكتب');
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل DOM');
    setTimeout(loadBooks, 100);
});

// Also try immediate load
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log('✅ DOM جاهز، تحميل فوري');
    setTimeout(loadBooks, 100);
}

console.log('✅ تم تحميل script-minimal.js بنجاح');
