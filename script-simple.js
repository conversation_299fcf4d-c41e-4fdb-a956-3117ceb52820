// Medical Library JavaScript - Simplified Version

console.log('🚀 تحميل script-simple.js...');

// Sample medical books data
const booksData = [
    {
        id: 1,
        title: "أطلس نيتر للتشريح البشري",
        author: "فرانك نيتر",
        description: "المرجع الأشهر في التشريح البشري مع رسوم توضيحية مفصلة",
        cover: "https://picsum.photos/300/400?random=1",
        rating: 4.9,
        category: "anatomy",
        featured: true,
        popular: true
    },
    {
        id: 2,
        title: "علم وظائف الأعضاء الطبي",
        author: "آرثر جايتون",
        description: "الكتاب الأساسي في علم وظائف الأعضاء للطلاب والممارسين",
        cover: "https://picsum.photos/300/400?random=2",
        rating: 4.8,
        category: "physiology",
        featured: true,
        popular: false
    },
    {
        id: 3,
        title: "علم الأمراض الأساسي",
        author: "فينيش كومار",
        description: "دليل شامل لفهم الأمراض وآليات حدوثها",
        cover: "https://picsum.photos/300/400?random=3",
        rating: 4.7,
        category: "pathology",
        featured: false,
        popular: true
    },
    {
        id: 4,
        title: "علم الأدوية الإكلينيكي",
        author: "برتون جودمان",
        description: "المرجع الشامل في علم الأدوية والعلاج الدوائي",
        cover: "https://picsum.photos/300/400?random=4",
        rating: 4.6,
        category: "pharmacology",
        featured: true,
        popular: true
    },
    {
        id: 5,
        title: "مبادئ الجراحة",
        author: "شوارتز",
        description: "الكتاب الأساسي في مبادئ وتقنيات الجراحة",
        cover: "https://picsum.photos/300/400?random=5",
        rating: 4.8,
        category: "surgery",
        featured: true,
        popular: true
    }
];

console.log('📚 تم تحميل بيانات الكتب:', booksData.length, 'كتاب');

// DOM Elements
let featuredBooksContainer, popularBooksContainer, recentBooksContainer;

// Simple book card creation
function createSimpleBookCard(book) {
    console.log('🏗️ إنشاء كتاب:', book.title);
    
    const card = document.createElement('div');
    card.className = 'book-card';
    card.style.cssText = `
        width: 280px;
        height: 400px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 218, 55, 0.3);
        border-radius: 10px;
        padding: 15px;
        margin: 10px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        text-align: center;
        cursor: pointer;
        transition: transform 0.3s ease;
    `;
    
    card.innerHTML = `
        <img src="${book.cover}" alt="${book.title}" 
             style="width: 100%; height: 250px; object-fit: cover; border-radius: 8px; margin-bottom: 10px;"
             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgdmlld0JveD0iMCAwIDI1MCAyNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyNTAiIGhlaWdodD0iMjUwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEyNSIgeT0iMTI1IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiPktpdGFiPC90ZXh0Pgo8L3N2Zz4=';">
        <h3 style="color: #ffda37; margin: 10px 0 5px 0; font-size: 16px; font-weight: bold;">${book.title}</h3>
        <p style="color: rgba(255, 255, 255, 0.8); margin: 0 0 5px 0; font-size: 14px;">${book.author}</p>
        <p style="color: rgba(255, 255, 255, 0.6); margin: 0 0 10px 0; font-size: 12px;">${book.description}</p>
        <div style="color: #ffda37; font-size: 14px; margin-top: auto;">⭐ ${book.rating}</div>
    `;
    
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
    });
    
    return card;
}

// Render books in container
function renderBooks(books, container) {
    if (!container) {
        console.error('❌ الحاوية غير موجودة');
        return;
    }
    
    console.log(`🎨 عرض ${books.length} كتاب في الحاوية`);
    container.innerHTML = '';
    
    if (books.length === 0) {
        container.innerHTML = '<div style="padding: 40px; text-align: center; color: #ffda37;">لا توجد كتب متاحة</div>';
        return;
    }
    
    books.forEach((book, index) => {
        console.log(`📖 إضافة كتاب ${index + 1}: ${book.title}`);
        const bookCard = createSimpleBookCard(book);
        container.appendChild(bookCard);
    });
    
    console.log(`✅ تم عرض ${books.length} كتاب بنجاح`);
}

// Load books into containers
function loadBooks() {
    console.log('🔄 بدء تحميل الكتب...');
    
    // Get containers
    featuredBooksContainer = document.getElementById('featured-books');
    popularBooksContainer = document.getElementById('popular-books');
    recentBooksContainer = document.getElementById('recent-books');
    
    console.log('📦 الحاويات:');
    console.log('- المميزة:', featuredBooksContainer ? '✅' : '❌');
    console.log('- الشائعة:', popularBooksContainer ? '✅' : '❌');
    console.log('- الحديثة:', recentBooksContainer ? '✅' : '❌');
    
    // Filter books
    const featuredBooks = booksData.filter(book => book.featured);
    const popularBooks = booksData.filter(book => book.popular);
    const recentBooks = booksData.slice(-3); // Last 3 books
    
    console.log('📊 إحصائيات الكتب:');
    console.log('- مميزة:', featuredBooks.length);
    console.log('- شائعة:', popularBooks.length);
    console.log('- حديثة:', recentBooks.length);
    
    // Render books
    if (featuredBooksContainer) {
        renderBooks(featuredBooks, featuredBooksContainer);
    }
    
    if (popularBooksContainer) {
        renderBooks(popularBooks, popularBooksContainer);
    }
    
    if (recentBooksContainer) {
        renderBooks(recentBooks, recentBooksContainer);
    }
    
    console.log('🎉 انتهى تحميل الكتب');
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل DOM');
    loadBooks();
});

// Also try immediate load
if (document.readyState === 'loading') {
    console.log('⏳ DOM قيد التحميل...');
} else {
    console.log('✅ DOM جاهز، تحميل فوري');
    setTimeout(loadBooks, 100);
}

console.log('✅ تم تحميل script-simple.js بنجاح');
