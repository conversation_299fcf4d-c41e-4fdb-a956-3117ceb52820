// Medical Library JavaScript

// Sample medical books data
const booksData = [
    {
        id: 1,
        title: "أطلس نيتر للتشريح البشري",
        author: "فرانك نيتر",
        description: "المرجع الأشهر في التشريح البشري مع رسوم توضيحية مفصلة",
        fullDescription: "يعتبر أطلس نيتر للتشريح البشري المرجع الأساسي لطلاب الطب والأطباء في جميع أنحاء العالم. يحتوي على أكثر من 900 رسمة توضيحية مفصلة تغطي جميع أجهزة الجسم البشري بدقة عالية.",
        cover: "https://picsum.photos/300/400?random=1",
        rating: 4.9,
        category: "anatomy",
        featured: true,
        popular: true
    },
    {
        id: 2,
        title: "علم وظائف الأعضاء الطبي",
        author: "آرثر جايتون",
        description: "الكتاب الأساسي في علم وظائف الأعضاء للطلاب والممارسين",
        fullDescription: "كتاب جايتون في علم وظائف الأعضاء هو المرجع الأكثر شمولية في هذا المجال. يشرح بوضوح كيفية عمل أجهزة الجسم المختلفة والتفاعلات المعقدة بينها.",
        cover: "https://picsum.photos/300/400?random=2",
        rating: 4.8,
        category: "physiology",
        featured: true,
        popular: false
    },
    {
        id: 3,
        title: "علم الأمراض الأساسي",
        author: "فينيش كومار",
        description: "دليل شامل لفهم الأمراض وآليات حدوثها",
        fullDescription: "يقدم هذا الكتاب فهماً عميقاً لعلم الأمراض من خلال شرح الآليات الأساسية للمرض والتغيرات التي تحدث على المستوى الخلوي والنسيجي.",
        cover: "https://picsum.photos/300/400?random=3",
        rating: 4.7,
        category: "pathology",
        featured: false,
        popular: true
    },
    {
        id: 4,
        title: "علم الأدوية الإكلينيكي",
        author: "برتون جودمان",
        description: "المرجع الشامل في علم الأدوية والعلاج الدوائي",
        fullDescription: "كتاب جودمان وجيلمان في علم الأدوية يعتبر المرجع الذهبي في هذا المجال، يغطي جميع جوانب العلاج الدوائي والتفاعلات الدوائية.",
        cover: "https://picsum.photos/300/400?random=4",
        rating: 4.6,
        category: "pharmacology",
        featured: true,
        popular: true
    },
    {
        id: 5,
        title: "أساسيات الجراحة",
        author: "شوارتز",
        description: "الدليل الأساسي للمبادئ الجراحية والتقنيات الحديثة",
        fullDescription: "يغطي هذا الكتاب جميع المبادئ الأساسية في الجراحة، من التقنيات الأساسية إلى الإجراءات المعقدة، مع التركيز على أحدث التطورات في المجال.",
        cover: "https://picsum.photos/300/400?random=5",
        rating: 4.8,
        category: "surgery",
        featured: false,
        popular: true
    },
    {
        id: 6,
        title: "الطب الباطني هاريسون",
        author: "دينيس كاسبر",
        description: "المرجع الأشمل في الطب الباطني والتشخيص",
        fullDescription: "كتاب هاريسون في الطب الباطني هو المرجع الأكثر شمولية في هذا التخصص، يغطي جميع الأمراض الباطنية مع أحدث طرق التشخيص والعلاج.",
        cover: "https://picsum.photos/300/400?random=6",
        rating: 4.9,
        category: "internal",
        featured: true,
        popular: true
    },
    {
        id: 7,
        title: "طب الأطفال نيلسون",
        author: "روبرت كليجمان",
        description: "الكتاب الأساسي في طب الأطفال وأمراض الطفولة",
        fullDescription: "يعتبر كتاب نيلسون المرجع الأساسي في طب الأطفال، يغطي جميع جوانب صحة الطفل من الولادة حتى المراهقة مع التركيز على الوقاية والعلاج.",
        cover: "https://picsum.photos/300/400?random=7",
        rating: 4.7,
        category: "pediatrics",
        featured: false,
        popular: false
    },
    {
        id: 8,
        title: "أساسيات الكيمياء الحيوية",
        author: "ديفيد نيلسون",
        description: "مقدمة شاملة للكيمياء الحيوية والعمليات الخلوية",
        fullDescription: "يقدم هذا الكتاب فهماً عميقاً للعمليات الكيميائية الحيوية في الجسم، من الاستقلاب إلى التعبير الجيني والتنظيم الخلوي.",
        cover: "https://picsum.photos/300/400?random=8",
        rating: 4.5,
        category: "anatomy",
        featured: false,
        popular: false
    },
    {
        id: 9,
        title: "علم الأحياء الدقيقة الطبية",
        author: "مايكل ويلكوكس",
        description: "دراسة شاملة للميكروبات والأمراض المعدية",
        fullDescription: "يغطي هذا الكتاب جميع جوانب علم الأحياء الدقيقة الطبية، من البكتيريا والفيروسات إلى الفطريات والطفيليات، مع التركيز على التشخيص والعلاج.",
        cover: "https://picsum.photos/300/400?random=9",
        rating: 4.6,
        category: "pathology",
        featured: true,
        popular: true
    },
    {
        id: 10,
        title: "علم النفس الطبي",
        author: "سارة جونسون",
        description: "فهم الجوانب النفسية في الممارسة الطبية",
        fullDescription: "يستكشف هذا الكتاب العلاقة بين الصحة النفسية والجسدية، ويقدم أدوات عملية للتعامل مع المرضى في مختلف الحالات النفسية.",
        cover: "https://picsum.photos/300/400?random=10",
        rating: 4.4,
        category: "internal",
        featured: false,
        popular: true
    },
    {
        id: 11,
        title: "جراحة القلب والأوعية الدموية",
        author: "أحمد الخالدي",
        description: "دليل شامل لجراحة القلب والشرايين",
        fullDescription: "يقدم هذا الكتاب نظرة شاملة على جراحة القلب والأوعية الدموية، من التقنيات التقليدية إلى أحدث الابتكارات في الجراحة طفيفة التوغل.",
        cover: "https://picsum.photos/300/400?random=11",
        rating: 4.8,
        category: "surgery",
        featured: true,
        popular: true
    },
    {
        id: 12,
        title: "علم الوراثة الطبية",
        author: "ليندا روبرتس",
        description: "أساسيات الوراثة في الطب الحديث",
        fullDescription: "يشرح هذا الكتاب مبادئ علم الوراثة الطبية وتطبيقاتها في التشخيص والعلاج، مع التركيز على الطب الشخصي والعلاج الجيني.",
        cover: "https://picsum.photos/300/400?random=12",
        rating: 4.5,
        category: "anatomy",
        featured: false,
        popular: false
    },
    {
        id: 13,
        title: "طب الطوارئ الإكلينيكي",
        author: "مارك ديفيس",
        description: "دليل سريع للتعامل مع الحالات الطارئة",
        fullDescription: "كتاب عملي يقدم بروتوكولات واضحة للتعامل مع الحالات الطارئة، من الإنعاش القلبي الرئوي إلى إدارة الصدمات والحوادث.",
        cover: "https://picsum.photos/300/400?random=13",
        rating: 4.7,
        category: "internal",
        featured: false,
        popular: true
    },
    {
        id: 14,
        title: "علم الأدوية السريري",
        author: "جيمس ويلسون",
        description: "تطبيق علم الأدوية في الممارسة السريرية",
        fullDescription: "يربط هذا الكتاب بين المبادئ الأساسية لعلم الأدوية والتطبيق العملي في العيادة، مع التركيز على الجرعات والتفاعلات الدوائية.",
        cover: "https://picsum.photos/300/400?random=14",
        rating: 4.6,
        category: "pharmacology",
        featured: true,
        popular: false
    },
    {
        id: 15,
        title: "طب الأسرة والمجتمع",
        author: "فاطمة العلي",
        description: "الرعاية الصحية الأولية والطب الوقائي",
        fullDescription: "يغطي هذا الكتاب مبادئ طب الأسرة والرعاية الصحية الأولية، مع التركيز على الوقاية والتعامل مع الأمراض المزمنة في المجتمع.",
        cover: "https://picsum.photos/300/400?random=15",
        rating: 4.3,
        category: "internal",
        featured: false,
        popular: false
    }
];

// DOM Elements - Will be initialized when DOM is ready
let featuredBooksContainer, popularBooksContainer, recentBooksContainer;
let categoryButtons, searchInput, modal;

function initializeDOMElements() {
    console.log('🔍 Looking for DOM elements...');
    featuredBooksContainer = document.getElementById('featured-books');
    popularBooksContainer = document.getElementById('popular-books');
    recentBooksContainer = document.getElementById('recent-books');
    categoryButtons = document.querySelectorAll('.category-btn');
    searchInput = document.querySelector('.search-input');
    modal = document.getElementById('book-modal');

    console.log('📋 DOM Elements Status:');
    console.log('- Featured container:', featuredBooksContainer ? '✅ Found' : '❌ Not found');
    console.log('- Popular container:', popularBooksContainer ? '✅ Found' : '❌ Not found');
    console.log('- Recent container:', recentBooksContainer ? '✅ Found' : '❌ Not found');
    console.log('- Category buttons:', categoryButtons.length, 'found');
    console.log('- Search input:', searchInput ? '✅ Found' : '❌ Not found');
    console.log('- Modal:', modal ? '✅ Found' : '❌ Not found');
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM Content Loaded - Initializing page...');
    console.log('📱 Screen width:', window.innerWidth);

    try {
        // Initialize DOM elements first
        initializeDOMElements();

        // Load books
        loadBooks();

        // Setup basic event listeners only
        if (typeof setupEventListeners === 'function') {
            setupEventListeners();
        }

        console.log('✅ Page initialization completed successfully');
    } catch (error) {
        console.error('❌ Error during page initialization:', error);

        // Fallback: try to load books anyway
        setTimeout(function() {
            console.log('🔄 Attempting fallback book loading...');
            try {
                initializeDOMElements();
                loadBooks();
            } catch (fallbackError) {
                console.error('❌ Fallback loading also failed:', fallbackError);
            }
        }, 1000);
    }
});

// Setup mobile-specific enhancements
function setupMobileEnhancements() {
    // Ensure mobile menu overlay doesn't interfere
    const overlay = document.getElementById('mobileMenuOverlay');
    if (overlay) {
        overlay.style.display = 'none';
        overlay.style.visibility = 'hidden';
        overlay.style.pointerEvents = 'none';
    }

    // Add touch event delegation for better button handling
    document.addEventListener('touchstart', function(e) {
        const target = e.target.closest('button, .book-action-btn, .page-nav-btn, .category-btn, .books-nav-arrow');
        if (target) {
            target.style.transform = target.style.transform.replace('scale(1)', 'scale(0.95)');
        }
    });

    document.addEventListener('touchend', function(e) {
        const target = e.target.closest('button, .book-action-btn, .page-nav-btn, .category-btn, .books-nav-arrow');
        if (target) {
            target.style.transform = target.style.transform.replace('scale(0.95)', 'scale(1)');
        }
    });

    // Prevent zoom on double tap for buttons
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(e) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            const target = e.target.closest('button, .book-action-btn, .page-nav-btn, .category-btn, .books-nav-arrow');
            if (target) {
                e.preventDefault();
            }
        }
        lastTouchEnd = now;
    }, false);
}

// Load books into containers
function loadBooks() {
    console.log('🔄 Loading books...');
    console.log('📚 Total books data:', booksData.length);

    // Initialize DOM elements first
    initializeDOMElements();

    // Check if containers exist
    console.log('📦 Featured container:', featuredBooksContainer);
    console.log('📦 Popular container:', popularBooksContainer);
    console.log('📦 Recent container:', recentBooksContainer);

    const featuredBooks = booksData.filter(book => book.featured);
    const popularBooks = booksData.filter(book => book.popular);
    const recentBooks = booksData.slice(-4); // Last 4 books as recent

    console.log('⭐ Featured books:', featuredBooks.length);
    console.log('🔥 Popular books:', popularBooks.length);
    console.log('🆕 Recent books:', recentBooks.length);

    if (featuredBooksContainer) {
        renderBooks(featuredBooks, featuredBooksContainer);
        console.log('✅ Featured books rendered');
    } else {
        console.error('❌ Featured books container not found!');
    }

    if (popularBooksContainer) {
        renderBooks(popularBooks, popularBooksContainer);
        console.log('✅ Popular books rendered');
    } else {
        console.error('❌ Popular books container not found!');
    }

    if (recentBooksContainer) {
        renderBooks(recentBooks, recentBooksContainer);
        console.log('✅ Recent books rendered');
    } else {
        console.error('❌ Recent books container not found!');
    }

    // Initialize navigation arrows after books are loaded
    setTimeout(initializeArrowStates, 100);
    console.log('🎯 Books loading completed');
}

// Render books in a container
function renderBooks(books, container) {
    if (!container) {
        console.error('❌ Container is null or undefined');
        return;
    }

    console.log(`🎨 Rendering ${books.length} books in container:`, container.id);
    container.innerHTML = '';

    if (books.length === 0) {
        console.warn('⚠️ No books to render');
        return;
    }

    books.forEach((book, index) => {
        try {
            console.log(`📖 Creating book card ${index + 1}:`, book.title);
            const bookCard = createBookCard(book);
            container.appendChild(bookCard);
            console.log(`✅ Book card ${index + 1} added successfully`);
        } catch (error) {
            console.error(`❌ Error creating book card ${index + 1}:`, error);
        }
    });

    console.log(`🎉 Successfully rendered ${books.length} books in ${container.id}`);
}

// Create ultra-realistic 3D book with page flipping
function createBookCard(book) {
    console.log(`🏗️ Creating book card for: ${book.title}`);

    try {
        const card = document.createElement('div');
        card.className = 'book-card cover-mode'; // Start in cover mode

        // Add inline styles for guaranteed visibility
        card.style.cssText = `
            width: 280px;
            height: 400px;
            position: relative;
            perspective: 2000px;
            cursor: pointer;
            margin-bottom: 3rem;
            z-index: 1;
            transition: all 0.4s ease;
            flex-shrink: 0;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        `;
        card.setAttribute('data-book-id', book.id);
        card.setAttribute('data-current-page', 'cover'); // Start with cover
        card.setAttribute('data-view-mode', 'cover'); // Track view mode

    // Split description for multiple pages if needed
    const fullDesc = book.fullDescription || book.description || 'وصف الكتاب غير متوفر';
    const descriptionWords = fullDesc.split(' ');
    const wordsPerPage = 80; // Adjust based on page size
    const page2Description = descriptionWords.slice(0, wordsPerPage).join(' ');
    const page3Description = descriptionWords.slice(wordsPerPage).join(' ');
    const hasMultiplePages = descriptionWords.length > wordsPerPage;

    card.innerHTML = `
        <div class="book-container">
            <div class="book-pages-container">
                <!-- Cover Page - Clean and Simple -->
                <div class="book-page book-cover-page">
                    <div class="book-cover-wrapper">
                        <div class="book-spine"></div>
                        <div class="book-pages"></div>
                        <div class="book-cover-image">
                            <img src="${book.cover}" alt="${book.title}" loading="lazy"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiPktpdGFiPC90ZXh0Pgo8L3N2Zz4='; this.onerror=null;">
                        </div>
                        <!-- Book Info Overlay - Transparent bar in bottom third -->
                        <div class="book-info">
                            <h3 class="book-title">${book.title}</h3>
                            <p class="book-author">د. ${book.author}</p>
                        </div>
                        <!-- Book Rating - Transparent badge -->
                        <div class="book-rating">
                            <i class="fas fa-star"></i> ${book.rating}
                        </div>
                        <!-- Cover Click Area - No buttons visible -->
                        <div class="cover-click-area" onclick="openBookFromCover(${book.id})">
                            <div class="cover-hint">انقر لفتح الكتاب</div>
                        </div>
                    </div>
                </div>

                <!-- Page 1 - Description (Hidden initially) -->
                <div class="book-page book-page-1 internal-page">
                    <div class="book-page-content">
                        <div class="page-content">
                            <h3 class="page-title">${book.title}</h3>
                            <div class="page-description">
                                <p><strong>المؤلف:</strong> د. ${book.author}</p>
                                <p><strong>التقييم:</strong> ${book.rating}/5 ⭐</p>
                                <br>
                                <p>${page2Description}${hasMultiplePages ? '...' : ''}</p>
                            </div>
                            <div class="page-navigation">
                                <button class="page-nav-btn back-to-cover" onclick="backToCover(${book.id})">
                                    <i class="fas fa-arrow-right"></i> العودة للغلاف
                                </button>
                                ${hasMultiplePages ?
                                    `<button class="page-nav-btn" onclick="nextPage(${book.id})">
                                        التالي <i class="fas fa-arrow-left"></i>
                                    </button>` :
                                    `<button class="page-nav-btn" onclick="nextPage(${book.id})">
                                        الإجراءات <i class="fas fa-arrow-left"></i>
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page 2 - Actions or More Description (Hidden initially) -->
                <div class="book-page book-page-2 internal-page">
                    <div class="book-page-content">
                        ${hasMultiplePages ?
                            `<div class="page-content">
                                <h3 class="page-title">تكملة الوصف</h3>
                                <div class="page-description">
                                    <p>${page3Description}</p>
                                </div>
                                <div class="page-navigation">
                                    <button class="page-nav-btn" onclick="previousPage(${book.id})">
                                        <i class="fas fa-arrow-right"></i> السابق
                                    </button>
                                    <button class="page-nav-btn" onclick="nextPage(${book.id})">
                                        الإجراءات <i class="fas fa-arrow-left"></i>
                                    </button>
                                </div>
                            </div>` :
                            `<div class="page-content actions-page">
                                <h3 class="page-title">اختر الإجراء</h3>
                                <div class="page-actions">
                                    <button class="book-action-btn read" onclick="readBook(${book.id})">
                                        <i class="fas fa-book-open"></i>
                                        قراءة مباشرة
                                    </button>
                                    <button class="book-action-btn download" onclick="downloadBook(${book.id})">
                                        <i class="fas fa-download"></i>
                                        تحميل الكتاب
                                    </button>
                                </div>
                                <div class="page-navigation">
                                    <button class="page-nav-btn" onclick="previousPage(${book.id})">
                                        <i class="fas fa-arrow-right"></i> السابق
                                    </button>
                                    <button class="page-nav-btn close-book" onclick="backToCover(${book.id})">
                                        العودة للغلاف <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>`
                        }
                    </div>
                </div>

                ${hasMultiplePages ?
                    `<!-- Page 3 - Actions for multi-page books (Hidden initially) -->
                    <div class="book-page book-page-3 internal-page">
                        <div class="book-page-content">
                            <div class="page-content actions-page">
                                <h3 class="page-title">اختر الإجراء</h3>
                                <div class="page-actions">
                                    <button class="book-action-btn read" onclick="readBook(${book.id})">
                                        <i class="fas fa-book-open"></i>
                                        قراءة مباشرة
                                    </button>
                                    <button class="book-action-btn download" onclick="downloadBook(${book.id})">
                                        <i class="fas fa-download"></i>
                                        تحميل الكتاب
                                    </button>
                                </div>
                                <div class="page-navigation">
                                    <button class="page-nav-btn" onclick="previousPage(${book.id})">
                                        <i class="fas fa-arrow-right"></i> السابق
                                    </button>
                                    <button class="page-nav-btn close-book" onclick="backToCover(${book.id})">
                                        العودة للغلاف <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>` : ''
                }
            </div>
        </div>
    `;

    // تم إزالة جميع event listeners المشكلة - استخدام onclick فقط

    console.log(`✅ Book card created successfully for: ${book.title}`);
    return card;

    } catch (error) {
        console.error(`❌ Error creating book card for ${book.title}:`, error);
        // Return a simple fallback card
        const fallbackCard = document.createElement('div');
        fallbackCard.className = 'book-card error-card';
        fallbackCard.innerHTML = `<div style="padding: 20px; text-align: center; color: red;">Error loading: ${book.title}</div>`;
        return fallbackCard;
    }
}

// Enhanced function to open book with smooth animation and positioning
function openBookFromCover(bookId) {
    const bookCard = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!bookCard) return;

    // Check if book needs to be scrolled into view
    const rect = bookCard.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const cardCenter = rect.left + rect.width / 2;
    const viewportCenter = viewportWidth / 2;

    // If book is not centered or partially outside viewport
    if (Math.abs(cardCenter - viewportCenter) > 100 || rect.left < 50 || rect.right > viewportWidth - 50) {
        // Smooth scroll to center the book
        bookCard.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
        });

        // Wait for scroll animation to complete
        setTimeout(() => {
            performBookOpening(bookCard, bookId);
        }, 600);
    } else {
        // Book is already well positioned, open immediately
        performBookOpening(bookCard, bookId);
    }
}

// Separate function to handle the actual book opening with animation
function performBookOpening(bookCard, bookId) {
    // Close other open books first
    document.querySelectorAll('.book-card.internal-mode').forEach(card => {
        const otherBookId = card.getAttribute('data-book-id');
        if (otherBookId !== bookId.toString()) {
            backToCover(parseInt(otherBookId));
        }
    });

    // Add opening animation class
    bookCard.classList.add('opening-animation');

    // Play page flip sound
    playPageFlipSound();

    // Start the opening sequence with smooth transition
    setTimeout(() => {
        bookCard.setAttribute('data-view-mode', 'internal');
        bookCard.setAttribute('data-current-page', '1');
        bookCard.classList.remove('cover-mode');
        bookCard.classList.add('internal-mode');

        // Add class to grid to indicate a book is open
        const booksGrid = bookCard.closest('.books-grid');
        if (booksGrid) {
            booksGrid.classList.add('has-open-book');
        }

        // Smooth transition to first page
        setTimeout(() => {
            bookCard.classList.add('page-1-open');
            bookCard.classList.remove('opening-animation');
        }, 200);

    }, 100);
}

// New function to go back to cover
function backToCover(bookId) {
    const bookCard = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!bookCard) return;

    // Switch back to cover mode
    bookCard.setAttribute('data-view-mode', 'cover');
    bookCard.setAttribute('data-current-page', 'cover');
    bookCard.classList.remove('internal-mode', 'page-1-open', 'page-2-open', 'page-3-open');
    bookCard.classList.add('cover-mode');

    // Remove class from grid
    const booksGrid = bookCard.closest('.books-grid');
    if (booksGrid) {
        // Check if any other books are still open
        const hasOtherOpenBooks = booksGrid.querySelector('.book-card.internal-mode');
        if (!hasOtherOpenBooks) {
            booksGrid.classList.remove('has-open-book');
        }
    }

    playBookCloseSound();
}

// Updated open book function (now handled by openBookFromCover)
function openBook(bookId) {
    // This function is now replaced by openBookFromCover
    openBookFromCover(bookId);
}

// Navigate to next page (updated for new system)
function nextPage(bookId) {
    const bookCard = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!bookCard) return;

    const viewMode = bookCard.getAttribute('data-view-mode');
    if (viewMode !== 'internal') return; // Only work in internal mode

    const currentPage = parseInt(bookCard.getAttribute('data-current-page'));
    const hasPage3 = bookCard.querySelector('.book-page-3');
    const maxPage = hasPage3 ? 3 : 2;

    if (currentPage < maxPage) {
        const nextPageNum = currentPage + 1;
        bookCard.setAttribute('data-current-page', nextPageNum.toString());

        // Remove previous page classes and add new one
        bookCard.classList.remove('page-1-open', 'page-2-open', 'page-3-open');

        if (nextPageNum === 1) {
            bookCard.classList.add('page-1-open');
        } else if (nextPageNum === 2) {
            bookCard.classList.add('page-2-open');
        } else if (nextPageNum === 3) {
            bookCard.classList.add('page-3-open');
        }

        playPageFlipSound();
    }
}

// Navigate to previous page (updated for new system)
function previousPage(bookId) {
    const bookCard = document.querySelector(`[data-book-id="${bookId}"]`);
    if (!bookCard) return;

    const viewMode = bookCard.getAttribute('data-view-mode');
    if (viewMode !== 'internal') return; // Only work in internal mode

    const currentPage = parseInt(bookCard.getAttribute('data-current-page'));

    if (currentPage > 1) {
        const prevPageNum = currentPage - 1;
        bookCard.setAttribute('data-current-page', prevPageNum.toString());

        // Remove all page classes and add appropriate one
        bookCard.classList.remove('page-1-open', 'page-2-open', 'page-3-open');

        if (prevPageNum === 1) {
            bookCard.classList.add('page-1-open');
        } else if (prevPageNum === 2) {
            bookCard.classList.add('page-2-open');
        }

        playPageFlipSound();
    }
}

// Close book completely (updated to use backToCover)
function closeBook(bookId) {
    backToCover(bookId);
}

// Enhanced page flip sound
function playPageFlipSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // Page flip sound: quick high-low frequency sweep
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (e) {
        // Silent fallback
    }
}

// Close books when clicking outside (updated for new system)
document.addEventListener('click', function(e) {
    if (!e.target.closest('.book-card')) {
        document.querySelectorAll('.book-card.internal-mode').forEach(card => {
            const bookId = card.getAttribute('data-book-id');
            backToCover(bookId);
        });
    }
});

// Enhanced book close sound
function playBookCloseSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(150, audioContext.currentTime + 0.2);

        gainNode.gain.setValueAtTime(0.03, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (e) {
        // Silent fallback
    }
}

// Setup event listeners
function setupEventListeners() {
    console.log('🔧 Setting up event listeners...');

    // Category filtering
    if (categoryButtons && categoryButtons.length > 0) {
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                const category = this.dataset.category;
                filterBooksByCategory(category);

                // Update active category
                categoryButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });
        console.log('✅ Category buttons event listeners set up');
    } else {
        console.log('⚠️ No category buttons found');
    }

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            searchBooks(searchTerm);
        });
        console.log('✅ Search input event listener set up');
    } else {
        console.log('⚠️ Search input not found');
    }

    // Modal close on outside click
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
        console.log('✅ Modal event listener set up');
    } else {
        console.log('⚠️ Modal not found');
    }

    // Smooth scrolling for navigation
    document.querySelectorAll('.nav-menu a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// Filter books by category
function filterBooksByCategory(category) {
    let filteredBooks;
    if (category === 'all') {
        filteredBooks = booksData;
    } else {
        filteredBooks = booksData.filter(book => book.category === category);
    }

    // Update all sections with filtered books
    renderBooks(filteredBooks.filter(book => book.featured), featuredBooksContainer);
    renderBooks(filteredBooks.filter(book => book.popular), popularBooksContainer);
    renderBooks(filteredBooks.slice(-4), recentBooksContainer);
}

// Search books
function searchBooks(searchTerm) {
    if (searchTerm === '') {
        loadBooks();
        return;
    }

    const filteredBooks = booksData.filter(book =>
        book.title.toLowerCase().includes(searchTerm) ||
        book.author.toLowerCase().includes(searchTerm) ||
        book.description.toLowerCase().includes(searchTerm)
    );

    renderBooks(filteredBooks, featuredBooksContainer);
    renderBooks(filteredBooks, popularBooksContainer);
    renderBooks(filteredBooks, recentBooksContainer);
}

// Open book modal
function openBookModal(bookId) {
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    document.getElementById('modal-title').textContent = book.title;
    document.getElementById('modal-body').innerHTML = `
        <div style="display: flex; gap: 2rem; margin-bottom: 2rem;">
            <img src="${book.cover}" alt="${book.title}"
                 style="width: 200px; height: 280px; object-fit: cover; border-radius: 10px;"
                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjI4MCIgdmlld0JveD0iMCAwIDIwMCAyODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjgwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTQwIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiPktpdGFiPC90ZXh0Pgo8L3N2Zz4='; this.onerror=null;">
            <div style="flex: 1;">
                <h4 style="color: var(--color-primary); margin-bottom: 0.5rem;">المؤلف</h4>
                <p style="margin-bottom: 1rem;">د. ${book.author}</p>

                <h4 style="color: var(--color-primary); margin-bottom: 0.5rem;">التقييم</h4>
                <p style="margin-bottom: 1rem;">
                    <i class="fas fa-star" style="color: var(--color-primary);"></i> ${book.rating}/5
                </p>

                <h4 style="color: var(--color-primary); margin-bottom: 0.5rem;">التصنيف</h4>
                <p style="margin-bottom: 1rem;">${getCategoryName(book.category)}</p>
            </div>
        </div>

        <h4 style="color: var(--color-primary); margin-bottom: 1rem;">وصف الكتاب</h4>
        <p style="line-height: 1.8; margin-bottom: 2rem;">${book.fullDescription}</p>

        <div style="display: flex; gap: 1rem;">
            <button class="read-btn" style="flex: 1;" onclick="readBook(${book.id})">
                <i class="fas fa-book-open"></i>
                بدء القراءة
            </button>
            <button class="bookmark-btn" onclick="toggleBookmark(${book.id}, this)"
                    style="width: auto; padding: 0 1.5rem; border-radius: 25px;">
                <i class="fas fa-bookmark"></i>
                حفظ
            </button>
        </div>
    `;

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModal() {
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// Toggle bookmark
function toggleBookmark(bookId, button) {
    button.classList.toggle('active');
    const isBookmarked = button.classList.contains('active');

    // Here you would typically save to localStorage or send to server
    if (isBookmarked) {
        showNotification('تم حفظ الكتاب في المفضلة');
    } else {
        showNotification('تم إزالة الكتاب من المفضلة');
    }
}

// Read book function with page-based animation
function readBook(bookId) {
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    // Show loading animation
    showNotification('جاري فتح الكتاب للقراءة...');

    // Add reading effect
    const bookCard = document.querySelector(`[data-book-id="${bookId}"]`);
    if (bookCard) {
        // Create reading animation - book flies up and opens
        bookCard.style.transform = 'translateY(-50px) scale(1.2) rotateX(10deg)';
        bookCard.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
        bookCard.style.zIndex = '1000';

        setTimeout(() => {
            bookCard.style.transform = 'translateY(-100px) scale(0.8) rotateY(-90deg)';

            setTimeout(() => {
                // Here you would typically redirect to the book reader
                showNotification(`📖 بدء قراءة: ${book.title}`);

                // Reset book position
                bookCard.style.transform = '';
                bookCard.style.transition = '';
                bookCard.style.zIndex = '';

                // Close the book
                closeBook(bookId);
            }, 1000);
        }, 600);
    }

    // Simulate book opening process
    setTimeout(() => {
        showNotification('استمتع بالقراءة! 📚✨');
    }, 2500);
}

// Download book function with enhanced animation
function downloadBook(bookId) {
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;

    // Show download animation
    showNotification('جاري تحضير التحميل...');

    // Add download effect
    const bookCard = document.querySelector(`[data-book-id="${bookId}"]`);
    if (bookCard) {
        // Create download animation overlay
        const downloadOverlay = document.createElement('div');
        downloadOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(76, 175, 80, 0.9);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            z-index: 1000;
            animation: downloadFadeIn 0.5s ease-in-out;
        `;

        downloadOverlay.innerHTML = `
            <div style="font-size: 3rem; margin-bottom: 1rem; animation: downloadBounce 1s ease-in-out infinite;">
                <i class="fas fa-download"></i>
            </div>
            <div style="font-weight: 600;">جاري التحميل...</div>
            <div style="width: 80%; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; margin-top: 1rem; overflow: hidden;">
                <div style="width: 0%; height: 100%; background: white; border-radius: 2px; animation: downloadProgress 2s ease-in-out forwards;"></div>
            </div>
        `;

        bookCard.querySelector('.book-container').appendChild(downloadOverlay);

        setTimeout(() => {
            downloadOverlay.remove();
            showNotification(`📥 تم تحميل: ${book.title}`);

            // Simulate actual download (you would replace this with real download logic)
            const link = document.createElement('a');
            link.href = '#'; // Replace with actual book file URL
            link.download = `${book.title}.pdf`;
            // link.click(); // Uncomment when you have real files

            // Close the book after download
            setTimeout(() => {
                closeBook(bookId);
            }, 1000);
        }, 2500);
    }
}

// Add enhanced download animations to CSS
const downloadStyle = document.createElement('style');
downloadStyle.textContent = `
    @keyframes downloadFadeIn {
        0% {
            opacity: 0;
            transform: scale(0.8);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes downloadBounce {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    @keyframes downloadProgress {
        0% {
            width: 0%;
        }
        100% {
            width: 100%;
        }
    }
`;
document.head.appendChild(downloadStyle);

// Get category name in Arabic
function getCategoryName(category) {
    const categories = {
        'anatomy': 'التشريح',
        'physiology': 'علم وظائف الأعضاء',
        'pathology': 'علم الأمراض',
        'pharmacology': 'علم الأدوية',
        'surgery': 'الجراحة',
        'internal': 'الطب الباطني',
        'pediatrics': 'طب الأطفال'
    };
    return categories[category] || category;
}

// Show notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: linear-gradient(135deg, var(--color-primary), #e6b800);
        color: #000000;
        padding: 1rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        z-index: 3000;
        box-shadow: 0 10px 30px rgba(254, 207, 5, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Setup scroll animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    document.querySelectorAll('.fade-in').forEach(el => {
        observer.observe(el);
    });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.classList.contains('active')) {
        closeModal();
    }
    if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        searchInput.focus();
    }
});

// Enhanced Books Horizontal Scroll Navigation - FIXED for all devices
function scrollBooks(gridId, direction) {
    const grid = document.getElementById(gridId);
    if (!grid) {
        console.error('Grid not found:', gridId);
        return;
    }

    const bookCard = grid.querySelector('.book-card');
    if (!bookCard) {
        console.error('No book cards found in grid:', gridId);
        return;
    }

    // Calculate responsive scroll amount
    const cardWidth = bookCard.offsetWidth;
    const computedStyle = window.getComputedStyle(grid);
    const gap = parseInt(computedStyle.gap) || 32;

    // Adjust scroll amount based on screen size
    const screenWidth = window.innerWidth;
    let scrollMultiplier = 1;

    if (screenWidth <= 480) {
        scrollMultiplier = 1.2; // Scroll more on small screens
    } else if (screenWidth <= 768) {
        scrollMultiplier = 1.1;
    }

    const scrollAmount = (cardWidth + gap) * scrollMultiplier;
    const currentScroll = grid.scrollLeft;
    const newScroll = currentScroll + (scrollAmount * direction);

    // Enhanced smooth scroll with better mobile support
    grid.scrollTo({
        left: Math.max(0, newScroll),
        behavior: 'smooth'
    });

    // Enhanced visual feedback with better animation
    const container = grid.closest('.books-grid-container');
    const arrow = container?.querySelector(direction > 0 ? '.books-nav-arrow.next' : '.books-nav-arrow.prev');

    if (arrow) {
        // Add active class for better visual feedback
        arrow.classList.add('active');
        arrow.style.transform = 'translateY(-50%) scale(0.9)';
        arrow.style.background = 'var(--color-primary-dark)';

        setTimeout(() => {
            arrow.style.transform = 'translateY(-50%) scale(1)';
            arrow.style.background = '';
            arrow.classList.remove('active');
        }, 200);
    }

    // Update arrow states based on scroll position
    updateArrowStates(grid, container);
}

// Helper function to update arrow states
function updateArrowStates(grid, container) {
    if (!grid || !container) return;

    const prevArrow = container.querySelector('.books-nav-arrow.prev');
    const nextArrow = container.querySelector('.books-nav-arrow.next');

    if (prevArrow && nextArrow) {
        const isAtStart = grid.scrollLeft <= 0;
        const isAtEnd = grid.scrollLeft >= (grid.scrollWidth - grid.clientWidth - 1);

        prevArrow.style.opacity = isAtStart ? '0.5' : '1';
        nextArrow.style.opacity = isAtEnd ? '0.5' : '1';

        prevArrow.disabled = isAtStart;
        nextArrow.disabled = isAtEnd;
    }
}

// Enhanced button interaction and accessibility
document.addEventListener('DOMContentLoaded', function() {
    // Fix all navigation arrows
    const navArrows = document.querySelectorAll('.books-nav-arrow');
    navArrows.forEach(arrow => {
        // Ensure proper cursor and interaction
        arrow.style.cursor = 'pointer';
        arrow.style.pointerEvents = 'auto';
        arrow.style.userSelect = 'none';

        // Add enhanced touch support
        arrow.addEventListener('touchstart', function(e) {
            e.preventDefault();
            this.style.transform = 'translateY(-50%) scale(0.95)';
        }, { passive: false });

        arrow.addEventListener('touchend', function(e) {
            e.preventDefault();
            this.style.transform = 'translateY(-50%) scale(1)';
            // Trigger click after touch
            setTimeout(() => {
                this.click();
            }, 50);
        }, { passive: false });

        // Add keyboard support
        arrow.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });

        // Make focusable
        if (!arrow.hasAttribute('tabindex')) {
            arrow.setAttribute('tabindex', '0');
        }
    });

    // Fix category buttons
    const categoryBtns = document.querySelectorAll('.category-btn');
    categoryBtns.forEach(btn => {
        btn.style.cursor = 'pointer';
        btn.style.pointerEvents = 'auto';

        // Enhanced touch feedback
        btn.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        }, { passive: true });

        btn.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        }, { passive: true });
    });

    // Fix search button
    const searchBtns = document.querySelectorAll('.search-btn');
    searchBtns.forEach(btn => {
        btn.style.cursor = 'pointer';
        btn.style.pointerEvents = 'auto';

        btn.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        }, { passive: true });

        btn.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        }, { passive: true });
    });

    // Initialize arrow states for all grids
    const grids = document.querySelectorAll('.books-grid');
    grids.forEach(grid => {
        const container = grid.closest('.books-grid-container');
        updateArrowStates(grid, container);

        // Update on scroll
        grid.addEventListener('scroll', () => {
            updateArrowStates(grid, container);
        });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        grids.forEach(grid => {
            const container = grid.closest('.books-grid-container');
            updateArrowStates(grid, container);
        });
    });
});

// Prevent any interference with button clicks
document.addEventListener('click', function(e) {
    // Ensure navigation arrows work
    if (e.target.closest('.books-nav-arrow')) {
        e.stopPropagation();
    }

    // Ensure category buttons work
    if (e.target.closest('.category-btn')) {
        e.stopPropagation();
    }

    // Ensure search button works
    if (e.target.closest('.search-btn')) {
        e.stopPropagation();
    }
}, true);

    // Update arrow states
    setTimeout(() => updateArrowStates(gridId), 300);
}

// Update navigation arrow states - Enhanced with better mobile support
function updateArrowStates(gridId) {
    const grid = document.getElementById(gridId);
    if (!grid) return;

    const container = grid.closest('.books-grid-container');
    if (!container) return;

    const prevBtn = container.querySelector('.books-nav-arrow.prev');
    const nextBtn = container.querySelector('.books-nav-arrow.next');

    if (!prevBtn || !nextBtn) return;

    const isAtStart = grid.scrollLeft <= 5; // Small tolerance for mobile
    const isAtEnd = grid.scrollLeft >= (grid.scrollWidth - grid.clientWidth - 5);

    // Update disabled state
    prevBtn.disabled = isAtStart;
    nextBtn.disabled = isAtEnd;

    // Update visual state
    prevBtn.style.opacity = isAtStart ? '0.3' : '1';
    nextBtn.style.opacity = isAtEnd ? '0.3' : '1';

    // Ensure buttons remain clickable
    prevBtn.style.pointerEvents = isAtStart ? 'none' : 'auto';
    nextBtn.style.pointerEvents = isAtEnd ? 'none' : 'auto';

    // Add visual feedback for mobile
    if (window.innerWidth <= 768) {
        prevBtn.style.transform = isAtStart ? 'translateY(-50%) scale(0.8)' : 'translateY(-50%) scale(1)';
        nextBtn.style.transform = isAtEnd ? 'translateY(-50%) scale(0.8)' : 'translateY(-50%) scale(1)';
    }
}

// Initialize arrow states when books are loaded - Enhanced for mobile
function initializeArrowStates() {
    ['featured-books', 'popular-books', 'recent-books'].forEach(gridId => {
        updateArrowStates(gridId);

        // Add scroll listener to update arrows
        const grid = document.getElementById(gridId);
        if (grid) {
            grid.addEventListener('scroll', () => updateArrowStates(gridId));

            // Add touch event listeners for better mobile support
            if ('ontouchstart' in window) {
                grid.addEventListener('touchstart', () => updateArrowStates(gridId));
                grid.addEventListener('touchend', () => {
                    setTimeout(() => updateArrowStates(gridId), 100);
                });
            }
        }

        // Ensure arrows are properly positioned and clickable
        const container = document.getElementById(gridId)?.closest('.books-grid-container');
        if (container) {
            const arrows = container.querySelectorAll('.books-nav-arrow');
            arrows.forEach(arrow => {
                // Add enhanced touch event listeners
                arrow.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    this.style.transform = 'translateY(-50%) scale(0.95)';
                });

                arrow.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    this.style.transform = 'translateY(-50%) scale(1)';
                    // Trigger click after touch
                    setTimeout(() => this.click(), 50);
                });

                // Ensure proper z-index and positioning
                arrow.style.zIndex = '100';
                arrow.style.position = 'absolute';
            });
        }
    });
}
