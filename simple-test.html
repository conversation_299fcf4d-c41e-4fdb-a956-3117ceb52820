<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للكتب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .books-grid {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid #ffda37;
            border-radius: 10px;
            min-height: 300px;
        }
        .book-card {
            width: 200px;
            height: 280px;
            background: #333;
            border: 1px solid #ffda37;
            border-radius: 8px;
            padding: 10px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            text-align: center;
        }
        .book-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            background: #555;
        }
        .book-title {
            margin: 10px 0 5px 0;
            font-size: 14px;
            font-weight: bold;
        }
        .book-author {
            margin: 0;
            font-size: 12px;
            opacity: 0.8;
        }
        .error {
            color: red;
            background: rgba(255, 0, 0, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 اختبار بسيط لتحميل الكتب</h1>
    
    <div id="error-log"></div>
    
    <h2>الكتب المميزة:</h2>
    <div id="featured-books" class="books-grid">
        <div style="padding: 20px; text-align: center; color: #ffda37;">
            ⏳ جاري تحميل الكتب...
        </div>
    </div>

    <script>
        console.log('🚀 بدء الاختبار البسيط...');
        
        // بيانات كتب بسيطة
        const simpleBooks = [
            {
                id: 1,
                title: "كتاب التشريح",
                author: "د. أحمد محمد",
                cover: "https://picsum.photos/200/150?random=1",
                featured: true
            },
            {
                id: 2,
                title: "كتاب الفسيولوجي",
                author: "د. فاطمة علي",
                cover: "https://picsum.photos/200/150?random=2",
                featured: true
            },
            {
                id: 3,
                title: "كتاب الأمراض",
                author: "د. محمود حسن",
                cover: "https://picsum.photos/200/150?random=3",
                featured: true
            }
        ];

        function createBookCard(book) {
            console.log('📖 إنشاء كتاب:', book.title);
            
            const card = document.createElement('div');
            card.className = 'book-card';
            
            card.innerHTML = `
                <img src="${book.cover}" alt="${book.title}" 
                     onerror="this.style.background='#666'; this.alt='صورة غير متوفرة';">
                <div class="book-title">${book.title}</div>
                <div class="book-author">${book.author}</div>
            `;
            
            return card;
        }

        function loadBooks() {
            console.log('📚 بدء تحميل الكتب...');
            
            try {
                const container = document.getElementById('featured-books');
                
                if (!container) {
                    throw new Error('الحاوية غير موجودة');
                }
                
                // مسح المحتوى الحالي
                container.innerHTML = '';
                
                // تصفية الكتب المميزة
                const featuredBooks = simpleBooks.filter(book => book.featured);
                console.log('⭐ عدد الكتب المميزة:', featuredBooks.length);
                
                if (featuredBooks.length === 0) {
                    container.innerHTML = '<div style="padding: 20px; color: red;">لا توجد كتب مميزة</div>';
                    return;
                }
                
                // إضافة كل كتاب
                featuredBooks.forEach((book, index) => {
                    console.log(`📖 إضافة كتاب ${index + 1}:`, book.title);
                    const bookCard = createBookCard(book);
                    container.appendChild(bookCard);
                });
                
                console.log('✅ تم تحميل جميع الكتب بنجاح');
                
            } catch (error) {
                console.error('❌ خطأ في تحميل الكتب:', error);
                document.getElementById('error-log').innerHTML = `
                    <div class="error">
                        <h3>خطأ في تحميل الكتب:</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // تحميل الكتب عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 تم تحميل الصفحة');
            loadBooks();
        });

        // تحميل فوري أيضاً
        setTimeout(loadBooks, 100);
    </script>
</body>
</html>
