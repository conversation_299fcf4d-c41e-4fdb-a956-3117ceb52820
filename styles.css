/* Medical Library Styles */

:root {
    /* Responsive Typography Variables - Using fluid scaling */
    --h1-font-size: clamp(2.5rem, 5vw, 4rem);
    --h1-font-style: normal;
    --h1-font-family: var(--font-primary);
    --h1-font-weight: 600;
    --h1-line-height: 1.2;
    --h1-m-font-size: clamp(2rem, 4vw, 2.5rem);
    --h1-letter-spacing: -0.02em;
    --h1-text-transform: none;
    --h1-text-decoration: none;

    --h2-font-size: clamp(2rem, 4vw, 3.5rem);
    --h2-font-style: normal;
    --h2-font-family: var(--font-primary);
    --h2-font-weight: 600;
    --h2-line-height: 1.3;
    --h2-m-font-size: clamp(1.75rem, 3.5vw, 2.25rem);
    --h2-letter-spacing: -0.01em;
    --h2-text-transform: none;
    --h2-text-decoration: none;

    --h3-font-size: clamp(1.75rem, 3.5vw, 3rem);
    --h3-font-style: normal;
    --h3-font-family: var(--font-primary);
    --h3-font-weight: 600;
    --h3-line-height: 1.3;
    --h3-m-font-size: clamp(1.5rem, 3vw, 2rem);
    --h3-letter-spacing: -0.01em;
    --h3-text-transform: none;
    --h3-text-decoration: none;

    --h4-font-size: clamp(1.5rem, 3vw, 2.5rem);
    --h4-font-style: normal;
    --h4-font-family: var(--font-primary);
    --h4-font-weight: 600;
    --h4-line-height: 1.4;
    --h4-m-font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    --h4-letter-spacing: 0;
    --h4-text-transform: none;
    --h4-text-decoration: none;

    --h5-font-size: clamp(1.25rem, 2.5vw, 1.625rem);
    --h5-font-style: normal;
    --h5-font-family: var(--font-primary);
    --h5-font-weight: 600;
    --h5-line-height: 1.4;
    --h5-m-font-size: clamp(1.125rem, 2vw, 1.25rem);
    --h5-letter-spacing: 0;
    --h5-text-transform: none;
    --h5-text-decoration: none;

    --h6-font-size: clamp(1.125rem, 2vw, 1.125rem);
    --h6-font-style: normal;
    --h6-font-family: var(--font-primary);
    --h6-font-weight: 600;
    --h6-line-height: 1.4;
    --h6-m-font-size: clamp(1rem, 1.5vw, 1.125rem);
    --h6-letter-spacing: 0;
    --h6-text-transform: none;
    --h6-text-decoration: none;

    --body-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --body-font-style: normal;
    --body-font-family: var(--font-secondary);
    --body-font-weight: 400;
    --body-line-height: 1.6;
    --body-m-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --body-letter-spacing: 0.01em;
    --body-text-transform: none;
    --body-text-decoration: none;

    --body-large-font-size: clamp(1rem, 2vw, 1.125rem);
    --body-large-font-style: normal;
    --body-large-font-family: var(--font-secondary);
    --body-large-font-weight: 400;
    --body-large-line-height: 1.6;
    --body-large-m-font-size: clamp(1rem, 2vw, 1.125rem);
    --body-large-letter-spacing: 0.01em;
    --body-large-text-transform: none;
    --body-large-text-decoration: none;

    --body-small-font-size: clamp(0.75rem, 1.25vw, 0.875rem);
    --body-small-font-style: normal;
    --body-small-font-family: var(--font-secondary);
    --body-small-font-weight: 500;
    --body-small-line-height: 1.4;
    --body-small-m-font-size: clamp(0.75rem, 1.25vw, 0.875rem);
    --body-small-letter-spacing: 0.02em;
    --body-small-text-transform: uppercase;
    --body-small-text-decoration: none;

    /* Font Families */
    --font-primary: 'Outfit', sans-serif;
    --font-secondary: 'Inter', sans-serif;
    --font-arabic: 'Cairo', sans-serif;
    --font-serif: 'PT Serif', serif;

    /* Navigation Variables - Responsive Units */
    --nav-link-color: rgb(255, 255, 255);
    --nav-link-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --nav-link-font-style: normal;
    --nav-link-color-hover: rgb(0, 255, 221);
    --nav-link-font-family: var(--font-secondary);
    --nav-link-font-weight: 400;
    --nav-link-line-height: 1.5;
    --nav-link-m-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --nav-link-letter-spacing: normal;
    --nav-link-text-transform: none;
    --nav-link-text-decoration: none;

    /* Responsive Button Variables */
    --grid-button-primary-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --grid-button-primary-padding-x: clamp(1.5rem, 3vw, 2.5rem);
    --grid-button-primary-padding-y: clamp(0.75rem, 1.5vw, 1rem);
    --grid-button-primary-font-color: #ffffff;
    --grid-button-primary-font-style: normal;
    --grid-button-primary-font-family: var(--font-secondary);
    --grid-button-primary-font-weight: 500;
    --grid-button-primary-line-height: 1.4;
    --grid-button-primary-m-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --grid-button-primary-m-padding-x: clamp(1.25rem, 2.5vw, 2.25rem);
    --grid-button-primary-m-padding-y: clamp(0.75rem, 1.25vw, 0.875rem);
    --grid-button-primary-box-shadow-x: 0;
    --grid-button-primary-box-shadow-y: 0.25rem;
    --grid-button-primary-border-radius: 2rem;
    --grid-button-primary-letter-spacing: 0.01em;
    --grid-button-primary-text-transform: none;
    --grid-button-primary-box-shadow-blur: 0.75rem;
    --grid-button-primary-text-decoration: none;
    --grid-button-primary-background-color: rgb(0, 0, 0);
    --grid-button-primary-box-shadow-color: rgba(0, 0, 0, 0.2);
    --grid-button-primary-box-shadow-spread: 0;
    --grid-button-primary-box-shadow-x-hover: 0;
    --grid-button-primary-box-shadow-y-hover: 0.5rem;
    --grid-button-primary-transition-duration: 0.3s;
    --grid-button-primary-box-shadow-blur-hover: 1.25rem;
    --grid-button-primary-background-color-hover: rgb(29, 30, 32);
    --grid-button-primary-box-shadow-color-hover: rgba(0, 0, 0, 0.3);
    --grid-button-primary-box-shadow-spread-hover: 0;
    --grid-button-primary-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

    --grid-button-secondary-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --grid-button-secondary-padding-x: clamp(1.5rem, 3vw, 2.5rem);
    --grid-button-secondary-padding-y: clamp(0.75rem, 1.5vw, 1rem);
    --grid-button-secondary-font-color: rgb(0, 0, 0);
    --grid-button-secondary-font-style: normal;
    --grid-button-secondary-font-family: var(--font-secondary);
    --grid-button-secondary-font-weight: 500;
    --grid-button-secondary-line-height: 1.4;
    --grid-button-secondary-m-font-size: clamp(0.875rem, 1.5vw, 1rem);
    --grid-button-secondary-m-padding-x: clamp(1.25rem, 2.5vw, 2.25rem);
    --grid-button-secondary-m-padding-y: clamp(0.75rem, 1.25vw, 0.875rem);
    --grid-button-secondary-box-shadow-x: 0;
    --grid-button-secondary-box-shadow-y: 0.25rem;
    --grid-button-secondary-border-radius: 2rem;
    --grid-button-secondary-letter-spacing: 0.01em;
    --grid-button-secondary-text-transform: none;
    --grid-button-secondary-box-shadow-blur: 0.75rem;
    --grid-button-secondary-text-decoration: none;
    --grid-button-secondary-background-color: white;
    --grid-button-secondary-box-shadow-color: rgba(0, 0, 0, 0.1);
    --grid-button-secondary-box-shadow-spread: 0;
    --grid-button-secondary-box-shadow-x-hover: 0;
    --grid-button-secondary-box-shadow-y-hover: 0.5rem;
    --grid-button-secondary-transition-duration: 0.3s;
    --grid-button-secondary-box-shadow-blur-hover: 1.25rem;
    --grid-button-secondary-background-color-hover: rgb(240, 240, 240);
    --grid-button-secondary-box-shadow-color-hover: rgba(0, 0, 0, 0.2);
    --grid-button-secondary-box-shadow-spread-hover: 0;
    --grid-button-secondary-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

    /* Color Palette - R-Ray Inspired */
    --color-meteorite-dark: #2f1c6a;
    --color-meteorite-dark-2: #1F1346;
    --color-meteorite: #8c85ff;
    --color-meteorite-light: #d5dfff;
    --color-primary-dark: #e6b800;
    --color-primary: rgb(255, 218, 55);
    --color-primary-light: #fff8e2;
    --color-primary-charts: #FFE066;
    --color-secondary: #00FFDD;
    --color-secondary-dark: #00e6c7;
    --color-secondary-light: #80fff0;
    --color-danger-dark: #d63163;
    --color-danger: #fc5185;
    --color-danger-light: #ffe8ef;
    --color-danger-charts: #FEA8C2;
    --color-warning-dark: #fea419;
    --color-warning-dark-2: #9F6000;
    --color-warning-charts: #FFD28C;
    --color-warning: #ffcd35;
    --color-warning-light: #fff8e2;
    --color-success-dark: #008361;
    --color-success: #00b090;
    --color-success-light: #def4f0;
    --color-dark: #0a0a0a;
    --color-dark-blue: #1a1a2e;
    --color-darker-blue: #16213e;
    --color-gray-dark: #36344d;
    --color-gray: #727586;
    --color-gray-border: #dadce0;
    --color-gray-light: #f2f3f6;
    --color-light: #fff;
    --color-azure: #357df9;
    --color-azure-light: #e3ebf9;
    --color-azure-dark: #265ab2;
    --color-indigo: #6366F1;
    --color-neutral-200: #D8DAE0;

    /* Custom Variables - Responsive Units */
    --5f31c617: clamp(4rem, 8vw, 5.75rem);
}

/* Enhanced Base Styles for Responsiveness */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    font-size: clamp(14px, 1.5vw, 18px); /* Responsive base font size */
    margin: 0;
    padding: 0;
    height: 100%;
}

body {
    font-family: var(--font-arabic);
    font-size: var(--body-font-size);
    font-weight: var(--body-font-weight);
    line-height: var(--body-line-height);
    letter-spacing: var(--body-letter-spacing);
    background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-blue) 50%, var(--color-darker-blue) 100%);
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    margin: 0;
    padding: 0;
    position: relative;
}

/* Enhanced Responsive Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.5em;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

h1 {
    font-family: var(--h1-font-family);
    font-size: var(--h1-font-size);
    font-weight: var(--h1-font-weight);
    line-height: var(--h1-line-height);
    letter-spacing: var(--h1-letter-spacing);
    text-transform: var(--h1-text-transform);
    text-decoration: var(--h1-text-decoration);
    margin-bottom: 0.75em;
}

h2 {
    font-family: var(--h2-font-family);
    font-size: var(--h2-font-size);
    font-weight: var(--h2-font-weight);
    line-height: var(--h2-line-height);
    letter-spacing: var(--h2-letter-spacing);
    text-transform: var(--h2-text-transform);
    text-decoration: var(--h2-text-decoration);
    margin-bottom: 0.75em;
}

h3 {
    font-family: var(--h3-font-family);
    font-size: var(--h3-font-size);
    font-weight: var(--h3-font-weight);
    line-height: var(--h3-line-height);
    letter-spacing: var(--h3-letter-spacing);
    text-transform: var(--h3-text-transform);
    text-decoration: var(--h3-text-decoration);
    margin-bottom: 0.75em;
}

h4 {
    font-family: var(--h4-font-family);
    font-size: var(--h4-font-size);
    font-weight: var(--h4-font-weight);
    line-height: var(--h4-line-height);
    letter-spacing: var(--h4-letter-spacing);
    text-transform: var(--h4-text-transform);
    text-decoration: var(--h4-text-decoration);
    margin-bottom: 0.5em;
}

h5 {
    font-family: var(--h5-font-family);
    font-size: var(--h5-font-size);
    font-weight: var(--h5-font-weight);
    line-height: var(--h5-line-height);
    letter-spacing: var(--h5-letter-spacing);
    text-transform: var(--h5-text-transform);
    text-decoration: var(--h5-text-decoration);
    margin-bottom: 0.5em;
}

h6 {
    font-family: var(--h6-font-family);
    font-size: var(--h6-font-size);
    font-weight: var(--h6-font-weight);
    line-height: var(--h6-line-height);
    letter-spacing: var(--h6-letter-spacing);
    text-transform: var(--h6-text-transform);
    text-decoration: var(--h6-text-decoration);
    margin-bottom: 0.5em;
}

/* Paragraph styles */
p {
    margin-bottom: 1rem;
    max-width: min(70ch, 100%); /* Optimal reading width with responsive constraint */
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
}

/* Enhanced Responsive Header */
.header {
    background: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(1.25rem);
    -webkit-backdrop-filter: blur(1.25rem);
    padding: clamp(0.5rem, 1.5vw, 0.8rem) 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 218, 55, 0.2);
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0;
    will-change: transform;
}

.nav-container {
    max-width: min(90vw, 75rem);
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 clamp(1rem, 3vw, 2rem);
    position: relative;
    gap: 1rem;
}

.logo {
    font-family: var(--font-serif);
    font-size: clamp(1.25rem, 3vw, 1.8rem);
    font-weight: 400;
    color: var(--color-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: clamp(0.25rem, 1vw, 0.5rem);
    text-shadow: 0 0 1.25rem rgba(255, 218, 55, 0.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    flex-shrink: 0;
}

.logo:hover {
    color: var(--color-secondary);
    text-shadow: 0 0 1.875rem rgba(0, 255, 221, 0.6);
    transform: scale(1.05);
}

.logo i {
    font-size: clamp(1.125rem, 2.5vw, 1.6rem);
    filter: drop-shadow(0 0 0.9375rem rgba(255, 218, 55, 0.4));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.logo:hover i {
    filter: drop-shadow(0 0 1.25rem rgba(0, 255, 221, 0.6));
    transform: rotate(5deg);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: clamp(0.75rem, 2vw, 1.5rem);
    font-family: var(--nav-link-font-family);
    margin: 0;
    padding: 0;
}

.nav-menu a {
    color: var(--nav-link-color);
    text-decoration: var(--nav-link-text-decoration);
    font-weight: var(--nav-link-font-weight);
    font-size: clamp(0.8rem, 1.5vw, 0.9rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: clamp(0.25rem, 1vw, 0.4rem) clamp(0.5rem, 1.5vw, 0.8rem);
    border-radius: 0.375rem;
    position: relative;
    white-space: nowrap;
    display: block;
}

.nav-menu a:hover,
.nav-menu a:focus {
    color: var(--color-primary);
    background: rgba(255, 218, 55, 0.1);
    transform: translateY(-0.125rem);
    box-shadow: 0 0.3125rem 0.9375rem rgba(255, 218, 55, 0.2);
    outline: none;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -0.3125rem;
    left: 50%;
    width: 0;
    height: 0.125rem;
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    box-shadow: 0 0 0.625rem rgba(255, 218, 55, 0.5);
    border-radius: 0.0625rem;
}

.nav-menu a:hover::after,
.nav-menu a:focus::after {
    width: 80%;
}

/* Enhanced Mobile Menu Toggle - Fixed for all devices */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    cursor: pointer !important;
    padding: clamp(0.5rem, 2vw, 0.8rem);
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Enhanced touch support - FIXED */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    pointer-events: auto !important;
    min-width: 2.75rem;
    min-height: 2.75rem;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1001;
    flex-shrink: 0;
    /* Remove any blocking properties */
    opacity: 1;
    visibility: visible;
}

.mobile-menu-toggle:hover,
.mobile-menu-toggle:focus {
    background: rgba(255, 218, 55, 0.1);
    transform: scale(1.05);
    outline: none;
}

.mobile-menu-toggle:active {
    transform: scale(0.95);
    background: rgba(255, 218, 55, 0.2);
}

.mobile-menu-toggle.active {
    color: var(--color-secondary);
    background: rgba(0, 255, 221, 0.1);
}

/* Mobile Menu Overlay - Fixed to only show when menu is active */
.mobile-menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
    /* Critical: Only show when menu is actually active */
    visibility: hidden;
    pointer-events: none;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Mobile Menu - Fixed positioning and visibility */
.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100%;
    background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-blue) 100%);
    z-index: 1001;
    padding: 2rem 1.5rem;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.3);
    transition: right 0.3s ease;
    overflow-y: auto;
    /* Ensure it doesn't interfere when hidden */
    visibility: hidden;
    pointer-events: none;
}

.mobile-menu.active {
    right: 0;
    visibility: visible;
    pointer-events: auto;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 218, 55, 0.2);
}

.mobile-menu-close {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mobile-menu-close:hover {
    background: rgba(255, 218, 55, 0.1);
    transform: scale(1.1);
}

.mobile-nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-menu li {
    margin-bottom: 0.5rem;
}

.mobile-nav-menu a {
    display: block;
    color: var(--nav-link-color);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.mobile-nav-menu a:hover {
    background: rgba(255, 218, 55, 0.1);
    border-color: rgba(255, 218, 55, 0.3);
    color: var(--color-primary);
    transform: translateX(5px);
}

.mobile-search-container {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 218, 55, 0.2);
}

.mobile-search-container .search-container {
    width: 100%;
    max-width: none;
}

.mobile-search-container .search-input {
    width: 100%;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3.125rem;
    padding: 0.125rem;
    border: 1px solid rgba(255, 218, 55, 0.3);
    backdrop-filter: blur(0.9375rem);
    -webkit-backdrop-filter: blur(0.9375rem);
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 0;
    flex-shrink: 1;
}

.search-input {
    background: transparent;
    border: none;
    padding: clamp(0.5rem, 1.5vw, 0.6rem) clamp(0.6rem, 2vw, 0.8rem);
    color: #ffffff;
    width: clamp(8rem, 20vw, 12.5rem);
    font-family: var(--font-secondary);
    font-size: clamp(0.75rem, 1.5vw, 0.85rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 0;
    flex: 1;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
    outline: none;
    color: #ffffff;
    width: clamp(10rem, 25vw, 15rem);
}

.search-container:focus-within {
    border-color: var(--color-primary);
    box-shadow: 0 0 1.875rem rgba(255, 218, 55, 0.3);
    background: rgba(255, 255, 255, 0.08);
    transform: scale(1.02);
}

.search-btn {
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
    border: none;
    border-radius: 50%;
    width: clamp(2.1875rem, 4vw, 2.1875rem);
    height: clamp(2.1875rem, 4vw, 2.1875rem);
    margin-left: 0.3125rem;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    font-size: clamp(0.75rem, 1.5vw, 0.85rem);
    box-shadow: 0 0.25rem 0.9375rem rgba(255, 218, 55, 0.3);
    /* FIXED - Enhanced touch support */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    pointer-events: auto !important;
    min-width: 2.75rem;
    min-height: 2.75rem;
    position: relative;
    z-index: 10;
    flex-shrink: 0;
    /* Remove any blocking properties */
    opacity: 1 !important;
    visibility: visible !important;
}

.search-btn:hover,
.search-btn:focus {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 0.5rem 1.5625rem rgba(255, 218, 55, 0.5);
    background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
    outline: none;
}

.search-btn:active {
    transform: scale(0.95);
    box-shadow: 0 0.125rem 0.5rem rgba(255, 218, 55, 0.4);
}

/* Main Content */
.main-content {
    margin-top: 0; /* إزالة المسافة العلوية لجعل الهيرو يبدأ مباشرة بعد الهيدر */
    padding: 0;
}

/* Enhanced Responsive Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)),
                url('rray.png') center/cover;
    padding: calc(clamp(4rem, 10vh, 5rem) + clamp(3rem, 8vh, 8rem)) 0 clamp(3rem, 8vh, 6rem);
    text-align: center;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(255, 218, 55, 0.1) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
    will-change: opacity;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: min(90vw, 75rem);
    margin: 0 auto;
    padding: 0 clamp(1rem, 4vw, 2rem);
    width: 100%;
}

.hero-content h1 {
    color: var(--color-primary);
    text-shadow: 0 0 1.25rem rgba(255, 218, 55, 0.5);
    margin-bottom: clamp(1rem, 3vh, 1.5rem);
    font-family: var(--font-serif);
    font-weight: 400;
    position: relative;
    font-size: var(--h1-font-size);
    line-height: var(--h1-line-height);
    letter-spacing: var(--h1-letter-spacing);
}

.hero-content h1::after {
    content: '';
    position: absolute;
    bottom: -0.625rem;
    left: 50%;
    transform: translateX(-50%);
    width: clamp(3.125rem, 15vw, 6.25rem);
    height: 0.1875rem;
    background: var(--color-primary);
    border-radius: 0.125rem;
    box-shadow: 0 0 1.25rem rgba(255, 218, 55, 0.5);
}

.hero-content p {
    font-family: var(--body-large-font-family);
    font-size: var(--body-large-font-size);
    margin-bottom: clamp(1.5rem, 4vh, 2.5rem);
    max-width: min(90vw, 43.75rem);
    margin-left: auto;
    margin-right: auto;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0.0625rem 0.0625rem 0.1875rem rgba(0, 0, 0, 0.5);
    line-height: var(--body-large-line-height);
    letter-spacing: var(--body-large-letter-spacing);
}

/* Enhanced Responsive Section Headers */
.section {
    max-width: min(95vw, 87.5rem);
    margin: 0 auto;
    padding: 0 clamp(1rem, 4vw, 2rem);
    margin-bottom: clamp(2rem, 6vh, 4rem);
    position: relative;
}

/* Enhanced Responsive Design - Mobile First Approach */

/* Extra Large Screens (1441px+) */
@media (min-width: 90.0625rem) {
    .nav-container {
        max-width: 80rem;
        padding: 0 2rem;
    }

    .hero-content {
        max-width: 80rem;
    }

    .section {
        max-width: 90rem;
    }
}

/* Large Screens (1025px - 1440px) */
@media (max-width: 90rem) and (min-width: 64.0625rem) {
    .nav-menu {
        gap: 1.25rem;
    }

    .search-input {
        width: 15rem;
    }

    .books-nav-arrow {
        width: 3rem;
        height: 3rem;
        font-size: 1.125rem;
    }
}

/* Tablets (769px - 1024px) */
@media (max-width: 64rem) and (min-width: 48.0625rem) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .search-input {
        width: 12rem;
    }

    .hero-section {
        padding: calc(clamp(4rem, 8vh, 5rem) + clamp(2rem, 6vh, 6rem)) 0 clamp(2rem, 6vh, 4rem);
        min-height: 85vh;
    }

    .books-nav-arrow {
        width: 2.75rem;
        height: 2.75rem;
        font-size: 1rem;
    }

    .books-nav-arrow.prev {
        left: -1rem;
    }

    .books-nav-arrow.next {
        right: -1rem;
    }
}

/* Large Mobile Phones (481px - 768px) */
@media (max-width: 48rem) and (min-width: 30.0625rem) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .search-input {
        width: 10rem;
    }

    .search-input:focus {
        width: 12rem;
    }

    .hero-section {
        padding: calc(clamp(3rem, 6vh, 4rem) + clamp(1.5rem, 4vh, 4rem)) 0 clamp(1.5rem, 4vh, 3rem);
        min-height: 75vh;
    }

    .books-nav-arrow {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 0.9rem;
        background: rgba(255, 218, 55, 1);
        border: 2px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 0.375rem 1.25rem rgba(0, 0, 0, 0.4);
    }

    .books-nav-arrow.prev {
        left: -0.75rem;
    }

    .books-nav-arrow.next {
        right: -0.75rem;
    }

    .books-nav-arrow:hover,
    .books-nav-arrow:focus,
    .books-nav-arrow:active {
        background: var(--color-primary);
        border-color: rgba(0, 0, 0, 0.2);
        transform: translateY(-50%) scale(1.05);
    }
}

/* Small Mobile Phones (320px - 480px) */
@media (max-width: 30rem) {
    .nav-container {
        padding: 0 1rem;
        gap: 0.5rem;
    }

    .logo {
        font-size: 1.125rem;
    }

    .logo i {
        font-size: 1rem;
    }

    .search-input {
        width: 8rem;
        font-size: 0.75rem;
    }

    .search-input:focus {
        width: 10rem;
    }

    .search-btn {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .hero-section {
        padding: calc(clamp(2.5rem, 5vh, 3rem) + clamp(1rem, 3vh, 2rem)) 0 clamp(1rem, 3vh, 2rem);
        min-height: 65vh;
    }

    .categories {
        gap: 0.375rem;
        padding: 0.75rem;
        flex-wrap: nowrap;
        overflow-x: auto;
        justify-content: flex-start;
    }

    .category-btn {
        padding: 0.5rem 0.875rem;
        font-size: 0.75rem;
        min-width: 4.5rem;
        min-height: 2.5rem;
        border-radius: 1.25rem;
        flex-shrink: 0;
    }

    .books-nav-arrow {
        width: 2.25rem;
        height: 2.25rem;
        font-size: 0.8rem;
        background: var(--color-primary);
        border: 3px solid rgba(0, 0, 0, 0.2);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.5);
        z-index: 200;
        font-weight: bold;
    }

    .books-nav-arrow.prev {
        left: -0.375rem;
    }

    .books-nav-arrow.next {
        right: -0.375rem;
    }

    .books-nav-arrow:hover,
    .books-nav-arrow:focus,
    .books-nav-arrow:active {
        background: var(--color-secondary);
        color: #000;
        border-color: rgba(0, 0, 0, 0.4);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 0.75rem 2.1875rem rgba(0, 255, 221, 0.4);
    }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 25px;
    border-right: 4px solid var(--color-primary);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 218, 55, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 218, 55, 0.05) 0%, rgba(0, 255, 221, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.section-header:hover::before {
    opacity: 1;
}

.section-title {
    font-family: var(--font-serif);
    font-size: var(--h2-m-font-size);
    font-weight: 400;
    color: var(--color-primary);
    position: relative;
    z-index: 2;
    text-shadow: 0 0 15px rgba(255, 218, 55, 0.4);
}

.view-all-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
    padding: var(--grid-button-primary-padding-y) var(--grid-button-primary-padding-x);
    border-radius: var(--grid-button-primary-border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: 600;
    font-family: var(--grid-button-primary-font-family);
    position: relative;
    z-index: 2;
    font-size: var(--grid-button-primary-font-size);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.view-all-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 218, 55, 0.2), transparent);
    transition: left 0.5s ease;
}

.view-all-btn:hover::before {
    left: 100%;
}

.view-all-btn:hover {
    background: var(--color-primary);
    color: #000000;
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 218, 55, 0.4);
    border-color: var(--color-primary);
}

/* Books Grid Container - ENHANCED RESPONSIVE DESIGN */
.books-grid-container {
    position: relative !important;
    max-width: min(95vw, 87.5rem);
    margin: 0 auto;
    padding: 0 clamp(1rem, 3vw, 2rem);
    /* CRITICAL - Force visibility */
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    overflow: visible !important;
    min-height: clamp(20rem, 40vh, 28rem) !important;
    background: rgba(255, 255, 255, 0.02) !important;
    border: 1px solid rgba(255, 218, 55, 0.1) !important;
    border-radius: clamp(0.75rem, 2vw, 1rem) !important;
}

/* Books Grid - ENHANCED RESPONSIVE HORIZONTAL DISPLAY */
.books-grid {
    display: flex !important;
    flex-direction: row !important;
    gap: clamp(0.75rem, 2.5vw, 1.5rem);
    padding: clamp(1.5rem, 3vh, 2.5rem) 0;
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) transparent;
    -ms-overflow-style: none;
    position: relative !important;
    align-items: flex-start !important;
    /* CRITICAL - Force absolute visibility */
    opacity: 1 !important;
    visibility: visible !important;
    min-height: clamp(18rem, 35vh, 25rem) !important;
    flex-shrink: 0 !important;
    /* Additional force properties */
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
    clip: none !important;
    clip-path: none !important;
    /* Enhanced scrolling */
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
}

/* Enhanced Scrollbar Design */
.books-grid::-webkit-scrollbar {
    height: clamp(0.375rem, 1vw, 0.5rem);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
}

.books-grid::-webkit-scrollbar-track {
    background: rgba(255, 218, 55, 0.1);
    border-radius: 0.25rem;
    margin: 0 1rem;
}

.books-grid::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.2);
}

.books-grid::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
    box-shadow: 0 0.25rem 0.75rem rgba(255, 218, 55, 0.4);
}

/* FIXED - Enhanced Navigation Arrows for All Devices */
.books-nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: clamp(3rem, 6vw, 3.125rem);
    height: clamp(3rem, 6vw, 3.125rem);
    background: rgba(255, 218, 55, 0.98);
    border: none;
    border-radius: 50%;
    color: #000;
    font-size: clamp(1rem, 2vw, 1.2rem);
    cursor: pointer !important;
    z-index: 150;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    /* CRITICAL FIX - Enhanced touch support */
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    /* CRITICAL FIX - Ensure clickability */
    pointer-events: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
    /* Responsive minimum touch target size */
    min-width: clamp(2.75rem, 6vw, 3.125rem);
    min-height: clamp(2.75rem, 6vw, 3.125rem);
    /* Remove any blocking properties */
    overflow: visible;
    will-change: transform;
}

.books-nav-arrow:hover,
.books-nav-arrow:focus {
    background: var(--color-primary);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 0.375rem 1.25rem rgba(0, 0, 0, 0.4);
    outline: none;
    border: 2px solid rgba(0, 0, 0, 0.1);
}

.books-nav-arrow:active {
    transform: translateY(-50%) scale(0.95);
    background: var(--color-primary-dark);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.3);
}

.books-nav-arrow.prev {
    left: clamp(-1.5625rem, -3vw, -1.25rem);
}

.books-nav-arrow.next {
    right: clamp(-1.5625rem, -3vw, -1.25rem);
}

.books-nav-arrow:disabled {
    opacity: 0.3;
    cursor: not-allowed !important;
    transform: translateY(-50%) scale(0.9);
    pointer-events: none !important;
    background: rgba(128, 128, 128, 0.5);
}

/* تحسين شبكة الكتب للشاشات المختلفة - تمرير أفقي */
@media (max-width: 1200px) {
    .books-grid {
        gap: 1.5rem;
        padding: 2.5rem 0;
    }

    .book-card {
        width: 260px;
        height: 360px;
    }

    .books-nav-arrow {
        width: 48px;
        height: 48px;
        font-size: 1.1rem;
        min-width: 48px;
        min-height: 48px;
    }
}

@media (max-width: 768px) {
    .books-grid-container {
        padding: 0 1rem;
        position: relative;
    }

    .books-grid {
        gap: 1.2rem;
        padding: 2rem 0;
    }

    .book-card {
        width: 220px;
        height: 320px;
    }

    .books-nav-arrow {
        width: 50px;
        height: 50px;
        font-size: 1.1rem;
        min-width: 50px;
        min-height: 50px;
        /* Better positioning for mobile */
        background: rgba(255, 218, 55, 0.98);
        border: 2px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    .books-nav-arrow.prev {
        left: -15px;
    }

    .books-nav-arrow.next {
        right: -15px;
    }

    .books-nav-arrow:hover,
    .books-nav-arrow:focus,
    .books-nav-arrow:active {
        background: var(--color-primary);
        border-color: rgba(0, 0, 0, 0.2);
        transform: translateY(-50%) scale(1.05);
    }
}

@media (max-width: 480px) {
    .books-grid-container {
        padding: 0 0.8rem;
        position: relative;
    }

    .books-grid {
        gap: 1rem;
        padding: 1.5rem 0;
    }

    .book-card {
        width: 180px;
        height: 260px;
    }

    .books-nav-arrow {
        width: 48px;
        height: 48px;
        font-size: 1rem;
        min-width: 48px;
        min-height: 48px;
        /* Enhanced for small screens */
        background: rgba(255, 218, 55, 1);
        border: 3px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
        z-index: 150;
    }

    .books-nav-arrow.prev {
        left: -10px;
    }

    .books-nav-arrow.next {
        right: -10px;
    }

    .books-nav-arrow:hover,
    .books-nav-arrow:focus,
    .books-nav-arrow:active {
        background: var(--color-primary-dark);
        border-color: rgba(0, 0, 0, 0.3);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
    }
}

@media (max-width: 320px) {
    .books-grid-container {
        padding: 0 1rem;
        position: relative;
    }

    .books-grid {
        gap: 0.8rem;
        padding: 1rem 0;
    }

    .book-card {
        width: 150px;
        height: 220px;
    }

    .books-nav-arrow {
        width: 44px;
        height: 44px;
        font-size: 0.9rem;
        min-width: 44px;
        min-height: 44px;
        /* Maximum visibility for very small screens */
        background: var(--color-primary);
        border: 4px solid rgba(0, 0, 0, 0.2);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
        z-index: 200;
        font-weight: bold;
    }

    .books-nav-arrow.prev {
        left: -5px;
    }

    .books-nav-arrow.next {
        right: -5px;
    }

    .books-nav-arrow:hover,
    .books-nav-arrow:focus,
    .books-nav-arrow:active {
        background: var(--color-secondary);
        color: #000;
        border-color: rgba(0, 0, 0, 0.4);
        transform: translateY(-50%) scale(1.15);
        box-shadow: 0 12px 35px rgba(0, 255, 221, 0.4);
    }
}

/* ===== LEGACY BOOK CARD SYSTEM - DISABLED ===== */
/* Old book card system is now replaced with 3D Flipbook */
.book-card {
    /* Legacy system disabled - using .flipbook-card instead */
    display: none !important;
}

/* Enable new 3D Flipbook system */
.flipbook-card {
    /* Styles are in flipbook-3d.css */
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* ===== LEGACY ANIMATIONS - DISABLED ===== */
.book-card.opening-animation,
.book-card.cover-mode,
.book-card.internal-mode,
.book-card.page-1-open,
.book-card.page-2-open,
.book-card.page-3-open {
    /* Legacy animations disabled */
    display: none !important;
}

/* Cover Mode - Clean single page view */
.book-card.cover-mode {
    cursor: pointer;
}

.book-card.cover-mode:hover {
    transform: translateY(-8px);
}

/* Internal Mode - Multi-page view with navigation */
.book-card.internal-mode {
    cursor: default !important;
    /* KEEP BOOK IN PLACE - DON'T MOVE IT */
    position: relative !important;
    transform: none !important;
    margin: 0 !important;
    /* Ensure it stays above others */
    z-index: 100 !important;
}

.book-card.page-1-open,
.book-card.page-2-open,
.book-card.page-3-open {
    z-index: 100;
}

/* Push adjacent books slightly when one is opened */
.books-grid:has(.book-card.page-1-open) .book-card:not(.page-1-open):not(.page-2-open):not(.page-3-open):not(.page-4-open) {
    transform: scale(0.95) translateY(10px);
    opacity: 0.7;
    transition: all 0.3s ease;
}

/* Alternative for browsers that don't support :has() */
.books-grid.has-open-book .book-card:not(.page-1-open):not(.page-2-open):not(.page-3-open):not(.page-4-open) {
    transform: scale(0.95) translateY(10px);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.book-container {
    position: relative;
    width: 100%;
    height: 400px;
    transform-style: preserve-3d;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Remove hover rotation */
.book-card:hover .book-container {
    transform: translateY(-5px);
}

/* Realistic Book Pages System */
.book-pages-container {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
}

/* Base Page Styles - Enhanced for realistic flipping */
.book-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transform-origin: left center;
    transition: transform 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Enhanced page flipping with realistic shadow */
.book-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 0, 0, 0.05) 50%,
        rgba(0, 0, 0, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 6px;
}

/* Show shadow during page flip */
.book-card.page-1-open .book-page-1::before,
.book-card.page-2-open .book-page-2::before,
.book-card.page-3-open .book-page-3::before,
.book-card.page-4-open .book-page-4::before {
    opacity: 1;
}

/* Page 1 - Cover (Always visible initially) */
.book-page-1 {
    z-index: 4;
    transform: rotateY(0deg);
}

/* Page 2 - First Content Page */
.book-page-2 {
    z-index: 3;
    transform: rotateY(0deg);
}

/* Page 3 - Second Content Page */
.book-page-3 {
    z-index: 2;
    transform: rotateY(0deg);
}

/* Page 4 - Actions Page */
.book-page-4 {
    z-index: 1;
    transform: rotateY(0deg);
}

/* Ultra-Realistic Page Flipping - Natural book opening */
.book-card.page-1-open .book-page-1 {
    transform: rotateY(-175deg) translateZ(2px);
    z-index: 1;
}

.book-card.page-2-open .book-page-1 {
    transform: rotateY(-175deg) translateZ(2px);
    z-index: 1;
}

.book-card.page-2-open .book-page-2 {
    transform: rotateY(-175deg) translateZ(4px);
    z-index: 2;
}

.book-card.page-3-open .book-page-1 {
    transform: rotateY(-175deg) translateZ(2px);
    z-index: 1;
}

.book-card.page-3-open .book-page-2 {
    transform: rotateY(-175deg) translateZ(4px);
    z-index: 1;
}

.book-card.page-3-open .book-page-3 {
    transform: rotateY(-175deg) translateZ(6px);
    z-index: 3;
}

.book-card.page-4-open .book-page-1,
.book-card.page-4-open .book-page-2,
.book-card.page-4-open .book-page-3 {
    transform: rotateY(-175deg) translateZ(2px);
    z-index: 1;
}

.book-card.page-4-open .book-page-4 {
    transform: rotateY(0deg) translateZ(8px);
    z-index: 4;
}

/* ===== FIX TEXT ORIENTATION IN FLIPPED PAGES ===== */
/* Fix text orientation for flipped pages - simple horizontal flip */
.book-card.page-1-open .book-page-1 .page-content,
.book-card.page-2-open .book-page-1 .page-content,
.book-card.page-2-open .book-page-2 .page-content,
.book-card.page-3-open .book-page-1 .page-content,
.book-card.page-3-open .book-page-2 .page-content,
.book-card.page-3-open .book-page-3 .page-content {
    transform: scaleX(-1);
    direction: rtl;
    text-align: right;
}

/* Fix buttons and navigation in flipped pages */
.book-card.page-1-open .book-page-1 .page-navigation,
.book-card.page-2-open .book-page-1 .page-navigation,
.book-card.page-2-open .book-page-2 .page-navigation,
.book-card.page-3-open .book-page-1 .page-navigation,
.book-card.page-3-open .book-page-2 .page-navigation,
.book-card.page-3-open .book-page-3 .page-navigation {
    transform: scaleX(-1);
}

/* Fix action buttons in flipped pages */
.book-card.page-1-open .book-page-1 .page-actions,
.book-card.page-2-open .book-page-1 .page-actions,
.book-card.page-2-open .book-page-2 .page-actions,
.book-card.page-3-open .book-page-1 .page-actions,
.book-card.page-3-open .book-page-2 .page-actions,
.book-card.page-3-open .book-page-3 .page-actions {
    transform: scaleX(-1);
}

/* Remove flipped text - keep pages natural */
/* The pages should show normally without text flipping */

/* Page 1 - Book Cover - NO BUTTONS ALLOWED */
.book-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow:
        0 0 0 2px rgba(0, 0, 0, 0.2),
        0 12px 35px rgba(0, 0, 0, 0.5),
        0 25px 50px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

/* ===== NEW COVER PAGE SYSTEM ===== */

/* Cover Page - Clean and Simple */
.book-cover-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    border-radius: 6px;
}

/* Hide cover page when in internal mode */
.book-card.internal-mode .book-cover-page {
    display: none;
}

/* Show cover page only in cover mode */
.book-card.cover-mode .book-cover-page {
    display: block;
}

/* Cover Wrapper - Main container */
.book-cover-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
    border-radius: 6px;
    box-shadow:
        0 0 0 2px #654321,
        0 10px 20px rgba(0, 0, 0, 0.4),
        0 25px 50px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.book-card.cover-mode:hover .book-cover-wrapper {
    box-shadow:
        0 0 0 2px #654321,
        0 15px 30px rgba(0, 0, 0, 0.5),
        0 30px 60px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
}

/* ===== BOOK INFO OVERLAY - TRANSPARENT BAR IN BOTTOM THIRD ===== */
/* This overrides all other book-info styles - positioned inside cover */
.book-cover-wrapper .book-info {
    position: absolute !important;
    bottom: 0;
    left: 0;
    right: 0;
    height: 33.33%; /* Covers bottom third of the cover */
    background: rgba(0, 0, 0, 0.6); /* Semi-transparent black */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 15px;
    box-sizing: border-box;
    z-index: 3;
    border-radius: 0 0 6px 6px; /* Match cover border radius */
    margin: 0 !important; /* Override any margin */
}

/* Book title and author inside the cover overlay */
.book-cover-wrapper .book-info .book-title {
    font-family: var(--font-arabic) !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: var(--color-primary) !important; /* Yellow color */
    margin: 0 0 8px 0 !important;
    text-align: center !important;
    line-height: 1.3 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.book-cover-wrapper .book-info .book-author {
    font-family: var(--font-arabic) !important;
    font-size: 0.9rem !important;
    font-weight: 400 !important;
    color: white !important;
    margin: 0 !important;
    text-align: center !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
    opacity: 0.95;
}

/* Cover Click Area - FORCE VISIBILITY */
.cover-click-area {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 10 !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    /* Force interaction */
    pointer-events: auto !important;
    touch-action: manipulation !important;
    /* Debug background - remove after testing */
    background: rgba(255, 218, 55, 0.1) !important;
    border: 1px dashed rgba(255, 218, 55, 0.3) !important;
    background: rgba(0, 0, 0, 0);
    transition: all 0.3s ease;
}

.cover-hint {
    background: rgba(255, 218, 55, 0.9) !important;
    color: #000 !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    /* Make always visible for testing */
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    /* Force visibility */
    display: block !important;
    position: relative !important;
    z-index: 20 !important;
}

.book-card.cover-mode:hover .cover-hint {
    opacity: 1;
    transform: translateY(0);
}

/* ===== INTERNAL PAGES SYSTEM ===== */

/* Hide internal pages in cover mode */
.book-card.cover-mode .internal-page {
    display: none;
}

/* Show internal pages only in internal mode */
.book-card.internal-mode .internal-page {
    display: block;
}

/* ===== HIDE BOOK ELEMENTS IN COVER MODE ===== */

/* Hide book spine and pages in cover mode for clean look */
.book-card.cover-mode .book-spine,
.book-card.cover-mode .book-pages {
    display: none !important;
}

/* Show book spine and pages only in internal mode */
.book-card.internal-mode .book-spine,
.book-card.internal-mode .book-pages {
    display: block;
}

/* ===== BOOK RATING - TRANSPARENT BADGE ===== */
.book-cover-wrapper .book-rating {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7); /* شفاف أسود */
    color: var(--color-primary); /* أصفر */
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    font-family: var(--font-arabic);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 4;
    backdrop-filter: blur(5px); /* تأثير ضبابي */
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

/* تأثير عند التمرير */
.book-card.cover-mode:hover .book-rating {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* أيقونة النجمة */
.book-rating i {
    color: var(--color-primary);
    margin-left: 3px;
}

/* IMPORTANT: Hide ALL buttons and navigation from cover page */
.book-page-1 .page-nav-btn,
.book-page-1 .book-action-btn,
.book-page-1 .page-2-navigation,
.book-page-1 .page-3-actions {
    display: none !important;
    visibility: hidden !important;
}

.book-cover-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;
}

.book-cover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    filter: contrast(1.1) saturate(1.1) brightness(0.95);
    display: block;
}

/* Book Cover Overlay for Realism */
.book-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 30%,
        transparent 70%,
        rgba(0, 0, 0, 0.1) 100%
    );
    z-index: 2;
    border-radius: 6px;
}

/* Page Content Styles */
.book-page-content {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fefefe 0%, #f8f8f8 100%);
    border-radius: 6px;
    padding: 25px;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: #333;
    font-family: var(--font-secondary);
}

/* ===== NEW INTERNAL PAGE CONTENT STYLES ===== */

/* Page Content Container */
.page-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
}

.page-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: 1rem;
    text-align: center;
    border-bottom: 2px solid var(--color-primary);
    padding-bottom: 0.5rem;
}

.page-description {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1.5rem;
}

.page-description p {
    margin-bottom: 0.8rem;
}

/* Actions Page Specific Styles */
.page-content.actions-page {
    text-align: center;
}

.page-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 2rem 0;
}

.book-action-btn {
    background: linear-gradient(135deg, var(--color-primary), #e6b800);
    color: #000;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(254, 207, 5, 0.3);
    /* Enhanced touch support */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    pointer-events: auto !important;
    min-height: 48px;
    min-width: 120px;
    position: relative;
    z-index: 10;
}

.book-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(254, 207, 5, 0.4);
}

.book-action-btn.read {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.book-action-btn.download {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

/* Page Navigation */
.page-navigation {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: auto;
}

.page-nav-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
    padding: 0.7rem 1.2rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    /* Enhanced touch support */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    pointer-events: auto !important;
    min-height: 44px;
    min-width: 80px;
    position: relative;
    z-index: 10;
}

.page-nav-btn:hover {
    background: var(--color-primary);
    color: #000;
    transform: translateY(-1px);
}

.page-nav-btn.back-to-cover {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

.page-nav-btn.back-to-cover:hover {
    background: #dc3545;
    color: white;
}

.page-nav-btn.close-book {
    background: rgba(108, 117, 125, 0.1);
    border-color: #6c757d;
    color: #6c757d;
}

.page-nav-btn.close-book:hover {
    background: #6c757d;
    color: white;
}

/* Add center binding shadow when pages are open */
.book-card.page-1-open .book-page-content::after,
.book-card.page-2-open .book-page-content::after,
.book-card.page-3-open .book-page-content::after,
.book-card.page-4-open .book-page-content::after {
    content: '';
    position: absolute;
    left: -5px;
    top: 0;
    width: 10px;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 0, 0, 0.1) 50%,
        transparent 100%);
    pointer-events: none;
    z-index: 1;
}

.book-page-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 218, 55, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 218, 55, 0.03) 0%, transparent 50%);
    border-radius: 6px;
    pointer-events: none;
}

/* Page 2 Content - Description */
.page-2-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.page-2-title {
    font-family: var(--font-serif);
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--color-primary-dark);
    margin-bottom: 1rem;
    text-align: center;
    line-height: 1.3;
}

.page-2-description {
    color: #444;
    font-size: 0.9rem;
    line-height: 1.6;
    text-align: right;
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.page-2-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.page-nav-btn {
    background: rgba(255, 218, 55, 0.1);
    border: 2px solid var(--color-primary);
    color: var(--color-primary-dark);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    font-weight: 600;
}

.page-nav-btn:hover {
    background: var(--color-primary);
    color: #000;
    transform: scale(1.05);
}

/* Page 3 Content - Actions */
.page-3-content {
    position: relative;
    z-index: 2;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.page-3-title {
    font-family: var(--font-serif);
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--color-primary-dark);
    margin-bottom: 2rem;
}

.page-3-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.book-action-btn {
    background: var(--color-primary);
    color: #000000;
    border: none;
    padding: 1.2rem 2rem;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-family: var(--font-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 6px 20px rgba(255, 218, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.book-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.book-action-btn:hover::before {
    left: 100%;
}

.book-action-btn:hover {
    background: var(--color-primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 218, 55, 0.5);
}

.book-action-btn.download {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.book-action-btn.download:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.5);
}

/* Book Spine - More Realistic */
.book-spine {
    position: absolute;
    left: -15px;
    top: 0;
    width: 30px;
    height: 100%;
    background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0.7) 50%,
        rgba(0, 0, 0, 0.9) 100%
    );
    transform: rotateY(-90deg);
    transform-origin: right center;
    border-radius: 0 0 0 8px;
    box-shadow:
        inset -4px 0 8px rgba(0, 0, 0, 0.6),
        0 0 15px rgba(0, 0, 0, 0.4);
}

/* Realistic Book Pages */
.book-pages {
    position: absolute;
    right: -4px;
    top: 4px;
    width: 12px;
    height: calc(100% - 8px);
    background: linear-gradient(
        180deg,
        #fafaf5 0%,
        #f2f2ed 25%,
        #eaeae5 50%,
        #e2e2dd 75%,
        #dadad5 100%
    );
    border-radius: 0 4px 4px 0;
    box-shadow:
        inset 3px 0 6px rgba(0, 0, 0, 0.2),
        4px 0 12px rgba(0, 0, 0, 0.3);
}

.book-pages::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent 0px,
        transparent 2px,
        rgba(0, 0, 0, 0.03) 2px,
        rgba(0, 0, 0, 0.03) 3px
    );
    border-radius: 0 3px 3px 0;
}

/* Book Inner Pages (When Opened) */
.book-inner {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fefefe 0%, #f8f8f8 100%);
    transform: rotateY(180deg);
    backface-visibility: hidden;
    border-radius: 6px;
    padding: 30px;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.book-inner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 218, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 218, 55, 0.05) 0%, transparent 50%);
    border-radius: 6px;
}

.book-inner-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.book-inner-title {
    font-family: var(--font-serif);
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--color-primary-dark);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.book-inner-description {
    color: #444;
    font-size: 0.95rem;
    line-height: 1.6;
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1.5rem;
    text-align: right;
}

.book-inner-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.book-action-btn {
    background: var(--color-primary);
    color: #000000;
    border: none;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-family: var(--font-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 6px 20px rgba(255, 218, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.book-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.book-action-btn:hover::before {
    left: 100%;
}

.book-action-btn:hover {
    background: var(--color-primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 218, 55, 0.5);
}

.book-action-btn.download {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.book-action-btn.download:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.5);
}

/* Book Rating Badge */
.book-rating {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 218, 55, 0.95);
    color: #000000;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.85rem;
    box-shadow: 0 4px 12px rgba(255, 218, 55, 0.4);
    z-index: 10;
    backdrop-filter: blur(10px);
}

/* Book Information Below Card - Simple Design */
.book-info {
    position: relative;
    width: 100%;
    text-align: center;
    padding: 10px 5px;
    margin-top: 15px;
    z-index: 0;
}

.book-title {
    font-family: var(--font-serif);
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: 0.3rem;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.book-author {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.8rem;
    font-weight: 400;
    font-family: var(--font-secondary);
    margin-bottom: 0.3rem;
}

.book-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-family: var(--font-secondary);
}

/* Advanced Book Opening Animation */
@keyframes bookOpenRealistic {
    0% {
        transform: rotateY(0deg) rotateX(0deg) scale(1);
    }
    25% {
        transform: rotateY(-45deg) rotateX(5deg) scale(1.05);
    }
    50% {
        transform: rotateY(-90deg) rotateX(10deg) scale(1.1);
    }
    75% {
        transform: rotateY(-135deg) rotateX(5deg) scale(1.05);
    }
    100% {
        transform: rotateY(-160deg) rotateX(5deg) scale(1.1);
    }
}

@keyframes bookCloseRealistic {
    0% {
        transform: rotateY(-160deg) rotateX(5deg) scale(1.1);
    }
    25% {
        transform: rotateY(-135deg) rotateX(5deg) scale(1.05);
    }
    50% {
        transform: rotateY(-90deg) rotateX(10deg) scale(1.1);
    }
    75% {
        transform: rotateY(-45deg) rotateX(5deg) scale(1.05);
    }
    100% {
        transform: rotateY(0deg) rotateX(0deg) scale(1);
    }
}

.book-card.opening .book-container {
    animation: bookOpenRealistic 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.book-card.closing .book-container {
    animation: bookCloseRealistic 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Simple Hover Effects */
.book-card:hover {
    transform: translateY(-8px);
}

.book-card:hover .book-container {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 25px 50px rgba(255, 218, 55, 0.1);
}

/* Subtle Page Glow Effect */
.book-card:hover .book-pages::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 218, 55, 0.1) 50%,
        transparent 100%
    );
    animation: pageGlow 2s ease-in-out infinite;
    border-radius: 0 3px 3px 0;
}

@keyframes pageGlow {
    0%, 100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

/* Book Cover Image */
.book-cover-image {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 80px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.book-cover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: sepia(10%) contrast(1.1) brightness(0.9);
}

/* Book Back Cover */
.book-back {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f5dc 0%, #e6e6d4 100%);
    border-radius: 8px;
    transform: rotateY(180deg);
    backface-visibility: hidden;
    padding: 30px;
    box-shadow:
        0 0 0 2px #8b4513,
        0 5px 15px rgba(0, 0, 0, 0.3);
}

.book-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.05) 0%,
        transparent 10%,
        transparent 90%,
        rgba(0, 0, 0, 0.1) 100%
    );
    border-radius: 8px;
}

/* Book Info on Cover */
.book-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0.7) 50%,
        transparent 100%
    );
    border-radius: 0 0 8px 8px;
    z-index: 2;
}

.book-title {
    font-family: var(--font-serif);
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--color-primary);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.book-author {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-family: var(--body-font-family);
}

.book-rating {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 218, 55, 0.95);
    color: #000000;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 3;
}

/* Book Inner Content */
.book-inner-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    color: #333;
}

.book-inner-title {
    font-family: var(--font-serif);
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--color-primary-dark);
    margin-bottom: 1rem;
    text-align: center;
    line-height: 1.3;
}

.book-description {
    color: #555;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
    overflow-y: auto;
}

.book-actions {
    display: flex;
    gap: 0.8rem;
    margin-top: auto;
}

.read-btn {
    background: var(--color-primary);
    color: #000000;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    font-size: 0.9rem;
    font-family: var(--grid-button-primary-font-family);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(255, 218, 55, 0.3);
}

.read-btn:hover {
    background: var(--color-primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 218, 55, 0.5);
}

.bookmark-btn {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid var(--color-primary);
    color: var(--color-primary-dark);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.bookmark-btn:hover {
    background: var(--color-primary);
    color: #000000;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 218, 55, 0.4);
}

.bookmark-btn.active {
    background: var(--color-primary);
    color: #000000;
    border-color: var(--color-primary-dark);
}

/* Book Opening Animation */
@keyframes bookOpen {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(-90deg) scale(1.1);
    }
    100% {
        transform: rotateY(-180deg);
    }
}

@keyframes bookClose {
    0% {
        transform: rotateY(-180deg);
    }
    50% {
        transform: rotateY(-90deg) scale(1.1);
    }
    100% {
        transform: rotateY(0deg);
    }
}

.book-card.opening .book-3d {
    animation: bookOpen 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.book-card.closing .book-3d {
    animation: bookClose 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Hover Effects */
.book-card:hover {
    transform: translateY(-10px);
}

.book-card:hover .book-3d {
    transform: rotateY(-15deg) rotateX(5deg) scale(1.05);
}

.book-card:hover .book-cover {
    box-shadow:
        0 0 0 2px #8b4513,
        0 15px 35px rgba(0, 0, 0, 0.4),
        0 25px 55px rgba(255, 218, 55, 0.2);
}

/* Page Flip Effect */
.book-card:hover .book-pages::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 218, 55, 0.1) 50%,
        transparent 100%
    );
    animation: pageFlip 2s ease-in-out infinite;
}

@keyframes pageFlip {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.book-cover {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.book-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.book-card:hover .book-cover img {
    transform: scale(1.1);
}

.book-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.book-card:hover .book-overlay {
    opacity: 1;
}

.book-rating {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(254, 207, 5, 0.9);
    color: #000000;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.book-info {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.book-title {
    font-family: var(--font-serif);
    font-size: var(--h4-m-font-size);
    font-weight: 400;
    margin-bottom: 0.8rem;
    color: var(--color-primary);
    line-height: var(--h4-line-height);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-shadow: 0 0 10px rgba(255, 218, 55, 0.4);
    transition: all 0.3s ease;
}

.book-card:hover .book-title {
    text-shadow: 0 0 20px rgba(255, 218, 55, 0.8);
    transform: scale(1.02);
}

.book-author {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--body-large-font-size);
    margin-bottom: 1rem;
    font-weight: 500;
    font-family: var(--body-large-font-family);
    transition: all 0.3s ease;
}

.book-card:hover .book-author {
    color: rgba(255, 255, 255, 1);
}

.book-description {
    color: rgba(255, 255, 255, 0.85);
    font-size: var(--body-font-size);
    line-height: var(--body-line-height);
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-family: var(--body-font-family);
}

.book-description.expanded {
    -webkit-line-clamp: unset;
    line-clamp: unset;
    display: block;
}

.book-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.read-btn {
    background: var(--color-primary);
    color: #000000;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--grid-button-primary-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    font-size: var(--grid-button-primary-font-size);
    font-family: var(--grid-button-primary-font-family);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(255, 218, 55, 0.4);
    position: relative;
    overflow: hidden;
}

.read-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.read-btn:hover::before {
    left: 100%;
}

.read-btn:hover {
    background: var(--color-primary-dark);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(255, 218, 55, 0.6);
}

.bookmark-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(0, 255, 221, 0.4);
    color: var(--color-secondary);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.bookmark-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(0, 255, 221, 0.3), transparent);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.bookmark-btn:hover::before {
    width: 100%;
    height: 100%;
}

.bookmark-btn:hover {
    border-color: var(--color-secondary);
    transform: scale(1.15) rotate(10deg);
    box-shadow: 0 8px 25px rgba(0, 255, 221, 0.4);
    color: var(--color-primary);
}

.bookmark-btn.active {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
    color: #000000;
    border-color: var(--color-primary);
    box-shadow: 0 8px 25px rgba(255, 218, 55, 0.5);
}

/* Categories */
.categories {
    display: flex;
    gap: clamp(0.5rem, 2vw, 1rem);
    margin-bottom: clamp(2rem, 5vh, 4rem);
    padding: clamp(1rem, 3vw, 2rem);
    justify-content: center;
    flex-wrap: wrap;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 1.25rem;
    backdrop-filter: blur(1.25rem);
    -webkit-backdrop-filter: blur(1.25rem);
    border: 1px solid rgba(255, 218, 55, 0.1);
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
    max-width: min(95vw, 87.5rem);
    margin-left: auto;
    margin-right: auto;
    overflow-x: auto;
    /* Enhanced mobile scroll support */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 218, 55, 0.5) transparent;
    position: relative;
}

/* Hide scrollbar on webkit browsers */
.categories::-webkit-scrollbar {
    height: 0.25rem;
}

.categories::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.125rem;
}

.categories::-webkit-scrollbar-thumb {
    background: rgba(255, 218, 55, 0.5);
    border-radius: 0.125rem;
}

.categories::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 218, 55, 0.7);
}

/* تخصيص شريط التمرير للشاشات الصغيرة */
.categories::-webkit-scrollbar {
    height: 6px;
}

.categories::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.categories::-webkit-scrollbar-thumb {
    background: rgba(255, 218, 55, 0.5);
    border-radius: 3px;
}

.categories::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 218, 55, 0.7);
}

.category-btn {
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(254, 207, 5, 0.3);
    color: #ffffff;
    padding: clamp(0.6rem, 2vw, 0.8rem) clamp(1rem, 3vw, 1.5rem);
    border-radius: 1.5625rem;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    font-weight: 600;
    font-family: var(--font-arabic);
    font-size: clamp(0.75rem, 1.5vw, 0.9rem);
    backdrop-filter: blur(0.9375rem);
    -webkit-backdrop-filter: blur(0.9375rem);
    position: relative;
    overflow: hidden;
    box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1);
    min-width: clamp(5rem, 15vw, 7.5rem);
    text-align: center;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    /* FIXED - Enhanced touch support */
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    pointer-events: auto !important;
    /* Responsive minimum touch target size */
    min-height: clamp(2.75rem, 6vw, 3.125rem);
    letter-spacing: 0.03125rem;
    text-transform: none;
    /* Ensure visibility and clickability */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 10;
}

/* Enhanced hover and focus effects for category buttons */
.category-btn:hover,
.category-btn:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--color-primary);
    color: var(--color-primary);
    transform: translateY(-0.125rem) scale(1.02);
    box-shadow: 0 0.5rem 1.25rem rgba(255, 218, 55, 0.3);
    outline: none;
}

.category-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2);
}

.category-btn.active {
    background: var(--color-primary);
    color: #000;
    border-color: var(--color-primary);
    box-shadow: 0 0.375rem 1rem rgba(255, 218, 55, 0.4);
}

.category-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(254, 207, 5, 0.2), transparent);
    transition: left 0.5s ease;
}

.category-btn:hover::before {
    left: 100%;
}

.category-btn:hover,
.category-btn.active {
    background: var(--color-primary);
    color: #000000;
    border-color: var(--color-primary);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 218, 55, 0.4);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    backdrop-filter: blur(10px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
    border-radius: 20px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(254, 207, 5, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-title {
    font-family: var(--h3-font-family);
    font-size: var(--h3-m-font-size);
    font-weight: var(--h3-font-weight);
    color: var(--color-primary);
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--color-primary);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(254, 207, 5, 0.3);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 1rem;
        max-width: 100%;
    }

    .logo {
        font-size: 2rem;
    }

    .logo i {
        font-size: 1.8rem;
    }

    .nav-menu {
        gap: 1.5rem;
    }

    .search-input {
        width: 200px;
    }

    .search-btn {
        width: 38px;
        height: 38px;
        font-size: 0.9rem;
    }
}

/* Tablet Specific */
@media (max-width: 900px) and (min-width: 769px) {
    .nav-container {
        padding: 0 1.2rem;
    }

    .nav-menu {
        gap: 1.3rem;
    }

    .nav-menu a {
        font-size: 0.85rem;
        padding: 0.3rem 0.6rem;
    }

    .search-input {
        width: 160px;
        font-size: 0.8rem;
    }

    .search-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .hero-section {
        padding: 10rem 0 6rem;
        min-height: 90vh;
    }

    .hero-content h1 {
        font-size: var(--h1-m-font-size);
    }

    .books-grid {
        display: flex;
        gap: 1.5rem;
        padding: 2.5rem 0;
        overflow-x: auto;
        scroll-behavior: smooth;
    }

    .section {
        padding: 0 1.5rem;
    }

    .section-title {
        font-size: var(--h2-m-font-size);
    }
}

/* استعلامات وسائط للتابلت والشاشات المتوسطة */
@media (max-width: 1024px) and (min-width: 769px) {
    .categories {
        gap: 0.8rem;
        padding: 1.8rem 1.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .category-btn {
        padding: 0.7rem 1.3rem;
        font-size: 0.85rem;
        min-width: 110px;
    }

    .books-grid {
        display: flex;
        gap: 1.5rem;
        padding: 2.5rem 0;
        overflow-x: auto;
        scroll-behavior: smooth;
    }

    .hero-section {
        padding: calc(80px + 6rem) 0 5rem;
    }

    .hero-content h1 {
        font-size: calc(var(--h1-font-size) * 0.9);
    }

    .hero-content p {
        font-size: calc(var(--body-large-font-size) * 0.95);
    }
}

@media (max-width: 768px) {
    .header {
        padding: 0.6rem 0;
        top: 0;
        margin-top: 0;
    }

    body {
        margin-top: 0;
        padding-top: 0;
    }

    .nav-container {
        justify-content: space-between;
        padding: 0.5rem 1rem;
    }

    .logo {
        font-size: 1.6rem;
        gap: 0.4rem;
    }

    .logo i {
        font-size: 1.4rem;
    }

    /* Hide desktop menu */
    .nav-menu {
        display: none;
    }

    /* Hide desktop search */
    .search-container {
        display: none;
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: block;
    }

    /* Enable mobile menu functionality - but don't show overlay by default */
    .mobile-menu {
        display: block;
        /* Ensure it stays hidden when not active */
        visibility: hidden;
        pointer-events: none;
    }

    .mobile-menu.active {
        visibility: visible;
        pointer-events: auto;
    }

    /* Overlay should only be visible when active */
    .mobile-menu-overlay {
        display: none;
        visibility: hidden;
        pointer-events: none;
    }

    .mobile-menu-overlay.active {
        display: block;
        visibility: visible;
        pointer-events: auto;
    }

    /* Ensure main content is always accessible when menu is closed */
    .main-content {
        position: relative;
        z-index: 1;
        pointer-events: auto;
    }

    /* Ensure all buttons remain clickable */
    .books-nav-arrow,
    .category-btn,
    .page-nav-btn,
    .book-action-btn,
    .search-btn,
    .mobile-menu-toggle {
        pointer-events: auto !important;
        position: relative;
        z-index: 50;
    }

    /* Hero section adjustments */
    .hero-section {
        padding: 8rem 0 4rem;
        min-height: 80vh;
    }

    .main-content {
        margin-top: 70px;
    }
}

/* Extra Small Devices */
@media (max-width: 480px) {
    .nav-container {
        padding: 0.4rem 0.8rem;
    }

    .logo {
        font-size: 1.4rem;
        gap: 0.3rem;
    }

    .logo i {
        font-size: 1.2rem;
    }

    .mobile-menu {
        width: 100%;
        right: -100%;
    }

    .mobile-menu.active {
        right: 0;
    }

    .hero-section {
        padding: 6rem 0 3rem;
        min-height: 70vh;
    }

    .main-content {
        margin-top: 60px;
    }

    .books-grid {
        display: flex;
        gap: 1.2rem;
        padding: 2rem 0;
        overflow-x: auto;
        scroll-behavior: smooth;
    }

    .section {
        padding: 0 1rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .section-title {
        font-size: calc(var(--h2-m-font-size) * 0.9);
    }

    .view-all-btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .hero-section {
        padding: calc(80px + 4rem) 0 3rem;
        min-height: 80vh;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .hero-content h1 {
        font-size: var(--h1-m-font-size);
        margin-bottom: 1rem;
    }

    .hero-content p {
        font-size: var(--body-large-m-font-size);
        margin-bottom: 2rem;
    }

    .section {
        padding: 0 1rem;
        margin-bottom: 3rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 1rem 0.5rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
    }

    .section-title {
        font-size: calc(var(--h2-m-font-size) * 0.8);
    }

    .view-all-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.8rem;
    }

    .books-grid {
        display: flex;
        gap: 1rem;
        padding: 1.5rem 0;
        overflow-x: auto;
        scroll-behavior: smooth;
    }

    .book-card {
        width: 100%;
        max-width: 200px;
        height: 300px;
        margin-bottom: 3rem;
    }

    .book-container {
        height: 340px;
    }

    .book-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .bookmark-btn {
        width: 100%;
        border-radius: 30px;
        height: 50px;
    }

    .categories {
        gap: 0.8rem;
        padding: 1.5rem 1rem;
        flex-wrap: nowrap; /* منع التفاف الأزرار */
        justify-content: flex-start; /* محاذاة لليسار */
        overflow-x: auto; /* تمرير أفقي */
        -webkit-overflow-scrolling: touch; /* تمرير سلس على iOS */
        scrollbar-width: none; /* إخفاء شريط التمرير على Firefox */
        -ms-overflow-style: none; /* إخفاء شريط التمرير على IE */
    }

    /* إخفاء شريط التمرير على Webkit */
    .categories::-webkit-scrollbar {
        display: none;
    }

    .category-btn {
        padding: 0.8rem 1.3rem;
        font-size: 0.85rem;
        min-width: 100px;
        min-height: 48px;
        flex-shrink: 0;
        border-radius: 20px;
        /* Enhanced for tablet touch */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 0.5rem 0;
        top: 0;
        margin: 0;
    }

    .hero-section {
        padding: calc(70px + 2rem) 0 2rem; /* تقليل المسافة العلوية */
        min-height: 70vh;
    }

    .hero-content {
        padding: 0 0.5rem;
    }

    .hero-content h1 {
        font-size: calc(var(--h1-m-font-size) * 0.8);
        margin-bottom: 0.8rem;
    }

    .hero-content p {
        font-size: var(--body-m-font-size);
        margin-bottom: 1.5rem;
        line-height: 1.4;
    }

    .section-title {
        font-size: calc(var(--h2-m-font-size) * 0.8);
    }

    .books-grid {
        display: flex;
        gap: 0.8rem;
        padding: 1rem 0;
        overflow-x: auto;
        scroll-behavior: smooth;
    }

    .book-card {
        width: 100%;
        max-width: 160px;
        height: 240px;
        margin-bottom: 2rem;
    }

    .book-container {
        height: 300px;
    }

    .book-info {
        bottom: -50px;
    }

    .book-title {
        font-size: 1rem;
    }

    .book-author {
        font-size: 0.9rem;
    }

    .book-description {
        font-size: 0.8rem;
    }

    .book-inner {
        padding: 20px;
    }

    .book-inner-title {
        font-size: 1.2rem;
    }

    .book-inner-description {
        font-size: 0.85rem;
    }

    .book-action-btn {
        padding: 1rem 1.8rem;
        font-size: 0.9rem;
        min-height: 50px;
        min-width: 140px;
        /* Enhanced for mobile touch */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        border: 3px solid transparent;
        font-weight: 700;
    }

    .page-nav-btn {
        padding: 0.8rem 1.4rem;
        font-size: 0.85rem;
        min-height: 48px;
        min-width: 100px;
        border-width: 3px;
        font-weight: 600;
    }

    .book-info {
        padding: 1.5rem;
    }

    .book-title {
        font-size: calc(var(--h4-m-font-size) * 0.8);
    }

    .categories {
        gap: 0.6rem;
        padding: 1rem 0.5rem;
        margin-bottom: 2rem;
    }

    .category-btn {
        padding: 0.7rem 1.1rem;
        font-size: 0.8rem;
        min-width: 90px;
        min-height: 46px;
        border-radius: 18px;
        /* Enhanced for mobile touch */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        border-width: 3px;
        font-weight: 700;
    }
}

/* استعلام وسائط للشاشات الصغيرة جداً */
@media (max-width: 320px) {
    .books-grid {
        display: flex;
        gap: 0.6rem;
        padding: 0.8rem 0;
        overflow-x: auto;
        scroll-behavior: smooth;
    }

    /* Enhanced buttons for very small screens */
    .book-action-btn {
        padding: 1.2rem 2rem;
        font-size: 1rem;
        min-height: 52px;
        min-width: 150px;
        border-radius: 12px;
        font-weight: 800;
        letter-spacing: 0.5px;
    }

    .page-nav-btn {
        padding: 1rem 1.6rem;
        font-size: 0.9rem;
        min-height: 50px;
        min-width: 110px;
        border-width: 4px;
        border-radius: 10px;
        font-weight: 700;
    }

    /* Ensure all interactive elements are easily tappable */
    .cover-click-area {
        min-height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .cover-hint {
        padding: 12px 20px;
        font-size: 1rem;
        font-weight: 700;
        border-radius: 25px;
    }

    .book-card {
        width: 100%;
        max-width: 140px;
        height: 200px;
        margin-bottom: 1.5rem;
    }
}

/* Global fixes for button accessibility on all screen sizes */
@media (max-width: 768px) {
    /* Ensure no element blocks button interactions */
    * {
        -webkit-tap-highlight-color: transparent;
    }

    /* Force all interactive elements to be accessible */
    button,
    .book-action-btn,
    .page-nav-btn,
    .category-btn,
    .books-nav-arrow,
    .search-btn,
    .mobile-menu-toggle,
    .cover-click-area {
        pointer-events: auto !important;
        touch-action: manipulation !important;
        user-select: none !important;
        position: relative !important;
        z-index: 50 !important;
    }

    /* Prevent any overlay from blocking interactions when menu is closed */
    body:not(.menu-open) .mobile-menu-overlay {
        display: none !important;
        visibility: hidden !important;
        pointer-events: none !important;
        opacity: 0 !important;
    }

    /* Ensure main content is always accessible */
    .main-content,
    .books-grid-container,
    .categories,
    .section {
        position: relative;
        z-index: 1;
        pointer-events: auto;
    }

    /* Fix any potential blocking elements */
    .hero-section::before,
    .section-header::before,
    .book-page-content::before {
        pointer-events: none !important;
    }

    /* Critical: Ensure mobile menu toggle is always accessible */
    .mobile-menu-toggle {
        z-index: 1000 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Ensure navigation arrows are always on top */
    .books-nav-arrow {
        z-index: 100 !important;
        position: absolute !important;
        pointer-events: auto !important;
    }

    /* Prevent any pseudo-elements from blocking clicks */
    *::before,
    *::after {
        pointer-events: none !important;
    }

    /* Re-enable pointer events for interactive pseudo-elements */
    .category-btn::before,
    .book-action-btn::before,
    .view-all-btn::before {
        pointer-events: none !important;
    }
}

/* CRITICAL FIX - Ensure all interactive elements work on all devices */
.books-nav-arrow,
.category-btn,
.search-btn,
.mobile-menu-toggle,
.page-nav-btn,
.book-action-btn,
.view-all-btn,
.close-btn,
.cover-click-area {
    /* Force clickability */
    pointer-events: auto !important;
    cursor: pointer !important;
    user-select: none !important;

    /* Enhanced touch support */
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(255, 218, 55, 0.3) !important;

    /* Ensure visibility */
    opacity: 1 !important;
    visibility: visible !important;

    /* Proper positioning */
    position: relative !important;
    z-index: 50 !important;

    /* Remove any blocking properties */
    overflow: visible !important;
}

/* Special handling for navigation arrows */
.books-nav-arrow {
    z-index: 150 !important;
    background: rgba(255, 218, 55, 0.98) !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.3) !important;
}

.books-nav-arrow:hover,
.books-nav-arrow:focus,
.books-nav-arrow:active {
    background: var(--color-primary) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 0.375rem 1.25rem rgba(0, 0, 0, 0.4) !important;
}

/* Prevent any pseudo-elements from blocking interactions */
*::before,
*::after {
    pointer-events: none !important;
}

/* Re-enable for interactive pseudo-elements */
.category-btn::before,
.book-action-btn::before,
.view-all-btn::before {
    pointer-events: none !important;
}

/* Ensure containers don't block interactions */
.books-grid-container,
.section,
.main-content,
.categories {
    pointer-events: auto;
    position: relative;
    z-index: 1;
}

/* Mobile-specific fixes */
@media (max-width: 48rem) {
    .books-nav-arrow {
        min-width: 2.75rem !important;
        min-height: 2.75rem !important;
        font-size: 1rem !important;
        z-index: 200 !important;
    }

    .category-btn {
        min-height: 2.75rem !important;
        padding: 0.75rem 1.25rem !important;
    }

    .search-btn {
        min-width: 2.5rem !important;
        min-height: 2.5rem !important;
    }

    .mobile-menu-toggle {
        min-width: 2.75rem !important;
        min-height: 2.75rem !important;
    }
}

/* Extra small screens */
@media (max-width: 30rem) {
    .books-nav-arrow {
        min-width: 2.5rem !important;
        min-height: 2.5rem !important;
        font-size: 0.9rem !important;
        z-index: 250 !important;
        background: var(--color-primary) !important;
        border: 3px solid rgba(0, 0, 0, 0.2) !important;
    }
}

/* Accessibility improvements */
.books-nav-arrow:focus,
.category-btn:focus,
.search-btn:focus,
.mobile-menu-toggle:focus {
    outline: 2px solid var(--color-primary) !important;
    outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .books-nav-arrow {
        border: 3px solid #000 !important;
        background: var(--color-primary) !important;
    }

    .category-btn {
        border: 2px solid #000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .books-nav-arrow,
    .category-btn,
    .search-btn {
        transition: none !important;
        animation: none !important;
    }
}

/* EMERGENCY FIX - Force books to be visible */
.books-grid,
.book-card,
.book-container,
.book-pages-container {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative !important;
}

.books-grid {
    display: flex !important;
    flex-direction: row !important;
}

.book-card {
    flex-shrink: 0 !important;
    min-width: 200px !important;
    min-height: 280px !important;
}

/* Ensure images load properly */
.book-cover-image img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    display: block !important;
}

/* Debug styles - remove after fixing */
.books-grid:empty::after {
    content: "⚠️ No books loaded - Check console for errors";
    color: red;
    font-size: 1rem;
    padding: 2rem;
    background: rgba(255, 0, 0, 0.1);
    border: 2px dashed red;
    border-radius: 8px;
    display: block;
    text-align: center;
}

/* Force container heights */
.books-grid-container {
    min-height: 400px !important;
    position: relative !important;
}

.section {
    min-height: 500px !important;
}

/* ULTIMATE FORCE - Make books visible at all costs */
#featured-books,
#popular-books,
#recent-books {
    display: flex !important;
    flex-direction: row !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    overflow-x: auto !important;
    overflow-y: visible !important;
    min-height: 400px !important;
    width: 100% !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 2px solid rgba(255, 218, 55, 0.3) !important;
    border-radius: 10px !important;
    padding: 20px !important;
    gap: 20px !important;
}

/* Force book cards to be visible */
.book-card {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    flex-shrink: 0 !important;
    min-width: 200px !important;
    min-height: 280px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 218, 55, 0.5) !important;
    border-radius: 8px !important;
    padding: 10px !important;
}

/* Debug: Show empty containers */
#featured-books:empty::before,
#popular-books:empty::before,
#recent-books:empty::before {
    content: "⚠️ الحاوية فارغة - تحقق من JavaScript";
    color: #ff6b6b;
    font-size: 16px;
    padding: 20px;
    background: rgba(255, 107, 107, 0.2);
    border: 2px dashed #ff6b6b;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 200px;
    text-align: center;
}

/* Override any potential hiding CSS */
.books-grid-container *,
.books-grid *,
.book-card * {
    opacity: 1 !important;
    visibility: visible !important;
}

/* FORCE BOOK PAGES TO BE VISIBLE - ENHANCED FOR ANIMATION */
.book-page,
.book-page-1,
.book-page-2,
.book-page-3 {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: #f8f8f8 !important;
    border-radius: 8px !important;
    border: 1px solid #ddd !important;
    transform-origin: left center !important;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    backface-visibility: hidden !important;
    transform-style: preserve-3d !important;
}

/* Ensure internal pages are positioned correctly for 3D effect */
.book-page.internal-page {
    z-index: 3 !important;
    transform: rotateY(0deg) !important;
}

/* Hide internal pages in cover mode */
.book-card.cover-mode .book-page-1,
.book-card.cover-mode .book-page-2 {
    display: none !important;
}

/* Show internal pages in internal mode */
.book-card.internal-mode .book-page-1,
.book-card.internal-mode .book-page-2 {
    display: block !important;
}

/* Page content styling */
.page-content {
    padding: 20px !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    color: #333 !important;
    background: #fff !important;
    border-radius: 8px !important;
}

.page-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 20px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid #eee !important;
}

.page-title {
    color: #ffda37 !important;
    font-size: 18px !important;
    font-weight: bold !important;
    margin: 0 !important;
}

.back-to-cover-btn,
.prev-page-btn,
.next-page-btn {
    background: #ffda37 !important;
    color: #000 !important;
    border: none !important;
    padding: 8px 12px !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.back-to-cover-btn:hover,
.prev-page-btn:hover,
.next-page-btn:hover {
    background: #e6c533 !important;
    transform: scale(1.05) !important;
}

.book-action-btn {
    background: #ffda37 !important;
    color: #000 !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: bold !important;
    margin: 10px 0 !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
}

.book-action-btn:hover {
    background: #e6c533 !important;
    transform: translateY(-2px) !important;
}

/* Additional force for sections */
.section {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Force main content visibility */
.main-content {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Ensure no overlay is blocking */
.hero-section::before,
.section::before,
.books-grid-container::before {
    display: none !important;
}

/* Force all book-related elements */
.book-container,
.book-pages-container,
.book-page,
.book-cover-page,
.book-cover-wrapper,
.book-cover-image,
.book-info {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
}

/* Make sure images show */
.book-cover-image img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* ===== ENHANCED BOOKS CONTAINER WITH VERTICAL SCROLLING ===== */
.books-container {
    position: relative;
    width: 100%;
    /* Remove fixed height to allow natural flow */
    overflow-y: visible; /* Changed from auto to visible */
    overflow-x: hidden;
    border-radius: clamp(0.75rem, 2vw, 1rem);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 218, 55, 0.1);
    padding: clamp(1rem, 2vw, 1.5rem);
    /* Enhanced scrollbar */
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) rgba(255, 255, 255, 0.1);
    /* Add bottom padding to separate from load more button */
    padding-bottom: clamp(2rem, 4vh, 3rem);
}

.books-container::-webkit-scrollbar {
    width: clamp(0.375rem, 1vw, 0.5rem);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.25rem;
}

.books-container::-webkit-scrollbar-track {
    background: rgba(255, 218, 55, 0.1);
    border-radius: 0.25rem;
    margin: 0.5rem 0;
}

.books-container::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--color-primary), var(--color-primary-dark));
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.2);
}

.books-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--color-primary-dark), var(--color-primary));
    box-shadow: 0 0.25rem 0.75rem rgba(255, 218, 55, 0.4);
}

/* ===== ENHANCED LOAD MORE BUTTON - FIXED POSITIONING ===== */
.load-more-container {
    position: relative; /* Changed from sticky to relative */
    margin-top: clamp(1.5rem, 3vh, 2rem); /* Add top margin to separate from books */
    left: 0;
    right: 0;
    background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0.7) 50%,
        transparent 100%
    );
    padding: clamp(1rem, 2vh, 1.5rem) 0 clamp(0.5rem, 1vh, 1rem) 0;
    text-align: center;
    backdrop-filter: blur(0.5rem);
    border-radius: clamp(0.75rem, 2vw, 1rem);
    z-index: 100;
}

.load-more-btn {
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
    border: none;
    color: #000;
    padding: clamp(0.75rem, 2vw, 1rem) clamp(1.5rem, 4vw, 2rem);
    border-radius: clamp(1.5rem, 4vw, 2rem);
    cursor: pointer;
    font-family: var(--font-arabic);
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: clamp(0.5rem, 1vw, 0.75rem);
    margin: 0 auto;
    box-shadow: 0 0.25rem 0.75rem rgba(255, 218, 55, 0.3);
    position: relative;
    overflow: hidden;
    min-width: clamp(8rem, 20vw, 12rem);
    /* Enhanced touch support */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}

.load-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.load-more-btn:hover::before {
    left: 100%;
}

.load-more-btn:hover {
    transform: translateY(-0.125rem) scale(1.02);
    box-shadow: 0 0.5rem 1.25rem rgba(255, 218, 55, 0.5);
    background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
}

.load-more-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 0.125rem 0.375rem rgba(255, 218, 55, 0.4);
}

.load-more-btn i {
    font-size: clamp(0.75rem, 1.25vw, 0.875rem);
    transition: transform 0.3s ease;
}

.load-more-btn:hover i {
    transform: rotate(90deg) scale(1.1);
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Disable hover effects on touch devices for better performance */
    .flipbook-card:hover,
    .book-card:hover,
    .nav-btn:hover,
    .action-btn:hover,
    .load-more-btn:hover {
        transform: none;
        box-shadow: inherit;
        background: inherit;
    }

    /* Larger touch targets */
    .nav-btn,
    .action-btn,
    .load-more-btn,
    .mobile-menu-toggle,
    .search-btn {
        min-width: 2.75rem;
        min-height: 2.75rem;
        padding: clamp(0.75rem, 2vw, 1rem);
    }

    /* Simplified animations for better performance */
    .flipbook-card,
    .book-card {
        transition: transform 0.2s ease;
    }

    /* Reduce motion for better battery life */
    * {
        animation-duration: 0.3s !important;
        transition-duration: 0.3s !important;
    }
}

/* Small screen optimizations */
@media (max-width: 768px) {
    /* Reduce complexity on small screens */
    .hero-section::before,
    .section-header::before,
    .flipbook-page::before,
    .flipbook-page::after {
        display: none;
    }

    /* Simplify shadows for better performance */
    .flipbook-card,
    .book-card,
    .nav-btn,
    .action-btn {
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2) !important;
    }

    /* Optimize scrolling */
    .books-grid,
    .books-container {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: auto; /* Disable smooth scrolling on mobile */
    }

    /* Reduce backdrop filters for better performance */
    .header,
    .mobile-menu,
    .flipbook-navigation {
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}

/* Very small screens - maximum optimization */
@media (max-width: 480px) {
    /* Disable all non-essential animations */
    .flipbook-card,
    .book-card,
    .nav-btn,
    .action-btn,
    .load-more-btn {
        transition: none !important;
        animation: none !important;
    }

    /* Simplify gradients */
    .load-more-btn,
    .action-btn {
        background: var(--color-primary) !important;
    }

    /* Reduce visual complexity */
    .books-container,
    .books-grid-container {
        background: rgba(255, 255, 255, 0.05) !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Optimize text rendering */
    body,
    h1, h2, h3, h4, h5, h6,
    p, span, div {
        text-rendering: optimizeSpeed;
        -webkit-font-smoothing: subpixel-antialiased;
    }
}

/* Battery saving mode */
@media (prefers-reduced-motion: reduce) {
    /* Disable all animations and transitions */
    *,
    *::before,
    *::after {
        animation: none !important;
        transition: none !important;
    }

    /* Remove all transform effects */
    .flipbook-card:hover,
    .book-card:hover,
    .nav-btn:hover,
    .action-btn:hover {
        transform: none !important;
    }
}

/* High contrast mode optimizations */
@media (prefers-contrast: high) {
    /* Simplify visual effects */
    .flipbook-card,
    .book-card,
    .nav-btn,
    .action-btn {
        box-shadow: none !important;
        border: 2px solid !important;
    }

    /* Remove gradients */
    .load-more-btn,
    .action-btn {
        background: var(--color-primary) !important;
    }
}

/* ===== FINAL RESPONSIVE POLISH ===== */

/* Ensure all sections are properly spaced */
.section {
    margin-bottom: clamp(2rem, 5vh, 4rem);
    padding: 0 clamp(1rem, 3vw, 2rem);
}

.section-header {
    margin-bottom: clamp(2rem, 4vh, 3rem);
    padding: clamp(1.5rem, 3vw, 2rem);
}

.section-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    line-height: 1.2;
    margin-bottom: clamp(0.5rem, 1vh, 1rem);
}

/* Ensure books grid is always visible and properly sized */
.books-grid {
    min-height: clamp(18rem, 35vh, 25rem) !important;
    padding: clamp(1.5rem, 3vh, 2.5rem) 0 !important;
    gap: clamp(0.75rem, 2vw, 1.5rem) !important;
}

/* Fix flipbook card positioning in grid */
.books-grid .flipbook-card {
    flex-shrink: 0 !important;
    scroll-snap-align: center !important;
    margin: 0 !important; /* Remove margin to prevent spacing issues */
}

/* Ensure proper spacing between flipbook cards */
.books-grid .flipbook-card:not(:last-child) {
    margin-left: clamp(0.75rem, 2vw, 1.5rem) !important;
}

/* Fix hero section responsiveness */
.hero-section {
    min-height: clamp(60vh, 80vh, 100vh);
    padding: calc(clamp(3rem, 8vh, 5rem) + clamp(2rem, 6vh, 6rem)) 0 clamp(2rem, 6vh, 4rem);
}

.hero-content h1 {
    font-size: clamp(2rem, 6vw, 4rem);
    line-height: 1.1;
    margin-bottom: clamp(1rem, 3vh, 2rem);
}

.hero-content p {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    line-height: 1.5;
    max-width: min(90vw, 50rem);
}

/* Enhanced mobile navigation */
.mobile-menu-toggle {
    display: none;
}

@media (max-width: 768px) {
    .nav-menu {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }

    .search-input {
        width: clamp(8rem, 25vw, 12rem);
    }

    .search-input:focus {
        width: clamp(10rem, 30vw, 15rem);
    }
}

/* Ultra-small screen optimizations */
@media (max-width: 320px) {
    .flipbook-card {
        min-width: clamp(8rem, 35vw, 12rem) !important;
        min-height: clamp(12rem, 45vw, 18rem) !important;
    }

    .books-grid {
        gap: clamp(0.5rem, 3vw, 1rem) !important;
        padding: clamp(1rem, 4vw, 1.5rem) 0 !important;
    }

    .section-header {
        padding: clamp(1rem, 4vw, 1.5rem);
    }

    .section-title {
        font-size: clamp(1.25rem, 6vw, 1.75rem);
    }
}

/* Landscape orientation optimizations */
@media (max-height: 600px) and (orientation: landscape) {
    .hero-section {
        min-height: 90vh;
        padding: calc(clamp(2rem, 6vh, 3rem) + clamp(1rem, 4vh, 3rem)) 0 clamp(1rem, 4vh, 2rem);
    }

    .flipbook-card {
        max-height: clamp(16rem, 70vh, 20rem) !important;
    }

    .books-grid {
        min-height: clamp(16rem, 70vh, 20rem) !important;
    }
}

/* Print styles */
@media print {
    .header,
    .mobile-menu,
    .mobile-menu-overlay,
    .flipbook-navigation,
    .load-more-container {
        display: none !important;
    }

    .flipbook-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .books-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
        gap: 1rem !important;
    }
}
