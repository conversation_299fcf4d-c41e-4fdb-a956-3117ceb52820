<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أنيميشن الكتاب</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
            font-family: Arial, sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-header h1 {
            color: #ffda37;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .instructions {
            background: rgba(255, 218, 55, 0.1);
            border: 1px solid #ffda37;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .instructions h3 {
            color: #ffda37;
            margin-bottom: 15px;
        }
        .instructions ul {
            text-align: right;
            margin: 15px 0;
            padding-right: 20px;
        }
        .instructions li {
            margin: 8px 0;
            font-size: 1.1rem;
        }
        .books-showcase {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 500px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 15px;
            padding: 40px;
            border: 1px solid rgba(255, 218, 55, 0.2);
        }
        .animation-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffda37;
            border-radius: 10px;
            padding: 15px;
            z-index: 9999;
            font-size: 14px;
            min-width: 250px;
        }
        .animation-status h4 {
            color: #ffda37;
            margin: 0 0 10px 0;
        }
        .status-item {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .status-waiting {
            background: rgba(255, 218, 55, 0.2);
            color: #ffda37;
        }
        .status-active {
            background: rgba(81, 207, 102, 0.2);
            color: #51cf66;
        }
        .status-complete {
            background: rgba(81, 207, 102, 0.3);
            color: #51cf66;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎬 اختبار أنيميشن الكتاب الحقيقي</h1>
            <p>اختبار مخصص للتأكد من أن الكتاب يفتح مثل الكتاب الحقيقي مع أنيميشن تقليب الصفحات</p>
        </div>

        <div class="instructions">
            <h3>📋 ما يجب أن تراه:</h3>
            <ul>
                <li>🖱️ <strong>انقر على الكتاب</strong> - يجب أن يبدأ الأنيميشن فوراً</li>
                <li>🎭 <strong>أنيميشن الفتح</strong> - الكتاب يكبر قليلاً ويرتفع</li>
                <li>📖 <strong>تقليب الصفحة</strong> - الغلاف يدور مثل الكتاب الحقيقي</li>
                <li>📄 <strong>إظهار المحتوى</strong> - تظهر الصفحة الداخلية</li>
                <li>📍 <strong>البقاء في المكان</strong> - الكتاب لا ينتقل من موقعه</li>
                <li>🔙 <strong>العودة للغلاف</strong> - انقر خارج الكتاب أو على زر العودة</li>
            </ul>
        </div>

        <!-- Animation Status Panel -->
        <div class="animation-status">
            <h4>🎬 حالة الأنيميشن</h4>
            <div id="status-click" class="status-item status-waiting">⏳ انتظار النقر</div>
            <div id="status-opening" class="status-item status-waiting">⏳ أنيميشن الفتح</div>
            <div id="status-flip" class="status-item status-waiting">⏳ تقليب الصفحة</div>
            <div id="status-content" class="status-item status-waiting">⏳ إظهار المحتوى</div>
        </div>

        <!-- Books Showcase -->
        <div class="books-showcase">
            <div class="books-grid-container">
                <div id="featured-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="script-fixed.js"></script>
    
    <!-- Animation Monitoring Script -->
    <script>
        function updateStatus(elementId, text, status = 'active') {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = text;
                element.className = `status-item status-${status}`;
            }
        }

        // Monitor book clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('.book-card')) {
                updateStatus('status-click', '✅ تم النقر على الكتاب', 'complete');
                
                // Monitor for opening animation
                setTimeout(() => {
                    const openingBooks = document.querySelectorAll('.book-card.opening-animation');
                    if (openingBooks.length > 0) {
                        updateStatus('status-opening', '🎭 أنيميشن الفتح نشط', 'active');
                    }
                }, 50);
            }
        });

        // Monitor class changes for animation states
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('book-card')) {
                        const bookId = target.getAttribute('data-book-id');
                        
                        if (target.classList.contains('opening-animation')) {
                            updateStatus('status-opening', '🎭 أنيميشن الفتح نشط', 'active');
                        }
                        
                        if (target.classList.contains('internal-mode')) {
                            updateStatus('status-opening', '✅ تم الفتح', 'complete');
                            updateStatus('status-flip', '📖 تقليب الصفحة نشط', 'active');
                        }
                        
                        if (target.classList.contains('page-1-open')) {
                            updateStatus('status-flip', '✅ تم تقليب الصفحة', 'complete');
                            updateStatus('status-content', '✅ تم إظهار المحتوى', 'complete');
                        }
                        
                        if (target.classList.contains('cover-mode')) {
                            // Reset all statuses when book closes
                            updateStatus('status-click', '⏳ انتظار النقر', 'waiting');
                            updateStatus('status-opening', '⏳ أنيميشن الفتح', 'waiting');
                            updateStatus('status-flip', '⏳ تقليب الصفحة', 'waiting');
                            updateStatus('status-content', '⏳ إظهار المحتوى', 'waiting');
                        }
                    }
                }
            });
        });

        // Start monitoring after books are loaded
        setTimeout(() => {
            const bookCards = document.querySelectorAll('.book-card');
            bookCards.forEach(card => {
                observer.observe(card, { attributes: true, attributeFilter: ['class'] });
            });
            console.log('🎬 بدء مراقبة أنيميشن الكتب');
        }, 2000);

        // Monitor for animation completion
        document.addEventListener('transitionend', function(e) {
            if (e.target.classList.contains('book-card')) {
                console.log('🎬 انتهى انتقال CSS:', e.propertyName);
            }
        });

        console.log('🚀 بدء اختبار أنيميشن الكتاب');
    </script>
</body>
</html>
