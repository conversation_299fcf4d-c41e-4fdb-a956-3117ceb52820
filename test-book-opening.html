<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فتح الكتب - المكتبة الطبية</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-header {
            text-align: center;
            color: #ffda37;
            margin-bottom: 30px;
        }
        .test-instructions {
            background: rgba(255, 218, 55, 0.1);
            border: 1px solid #ffda37;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
        }
        .test-instructions h3 {
            color: #ffda37;
            margin-bottom: 15px;
        }
        .test-instructions ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        .test-instructions li {
            margin: 5px 0;
        }
        .debug-panel {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffda37;
            border-radius: 10px;
            padding: 15px;
            z-index: 9999;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-panel h4 {
            color: #ffda37;
            margin: 0 0 10px 0;
        }
        .debug-log {
            color: #fff;
            margin: 2px 0;
        }
        .books-test-area {
            background: rgba(255, 255, 255, 0.02);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 218, 55, 0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔍 اختبار وظيفة فتح الكتب</h1>
            <p>اختبار شامل لوظيفة فتح الكتب مثل الكتاب الحقيقي</p>
        </div>

        <div class="test-instructions">
            <h3>📋 تعليمات الاختبار:</h3>
            <ul>
                <li>🖱️ انقر على أي كتاب لفتحه</li>
                <li>📖 يجب أن يفتح الكتاب مثل الكتاب الحقيقي</li>
                <li>➡️ استخدم أزرار التنقل للانتقال بين الصفحات</li>
                <li>🔙 انقر على "العودة للغلاف" للإغلاق</li>
                <li>🖱️ أو انقر خارج الكتاب لإغلاقه</li>
                <li>📱 اختبر على الهاتف والكمبيوتر</li>
            </ul>
        </div>

        <!-- Debug Panel -->
        <div class="debug-panel">
            <h4>🔍 سجل التشخيص</h4>
            <div id="debug-output">⏳ جاري التحميل...</div>
        </div>

        <!-- Books Test Area -->
        <div class="books-test-area">
            <h2 style="color: #ffda37; text-align: center; margin-bottom: 30px;">
                📚 الكتب المتاحة للاختبار
            </h2>
            
            <div class="books-grid-container">
                <div id="featured-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="script-fixed.js"></script>
    
    <!-- Debug Script -->
    <script>
        let debugOutput = document.getElementById('debug-output');
        let debugMessages = [];
        
        function addDebugMessage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            debugMessages.push(`<div class="debug-log">[${timestamp}] ${message}</div>`);
            
            if (debugMessages.length > 15) {
                debugMessages.shift();
            }
            
            if (debugOutput) {
                debugOutput.innerHTML = debugMessages.join('');
                debugOutput.scrollTop = debugOutput.scrollHeight;
            }
        }
        
        // Override console methods for debugging
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            addDebugMessage(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addDebugMessage('❌ ' + args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        // Monitor book opening events
        document.addEventListener('click', function(e) {
            if (e.target.closest('.cover-click-area')) {
                addDebugMessage('🖱️ تم النقر على منطقة فتح الكتاب');
            }
            
            if (e.target.closest('.book-action-btn')) {
                addDebugMessage('🔘 تم النقر على زر إجراء');
            }
            
            if (e.target.closest('.next-page-btn')) {
                addDebugMessage('➡️ تم النقر على الصفحة التالية');
            }
            
            if (e.target.closest('.prev-page-btn')) {
                addDebugMessage('⬅️ تم النقر على الصفحة السابقة');
            }
            
            if (e.target.closest('.back-to-cover-btn')) {
                addDebugMessage('🔙 تم النقر على العودة للغلاف');
            }
        });
        
        // Monitor book state changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('book-card')) {
                        const bookId = target.getAttribute('data-book-id');
                        if (target.classList.contains('internal-mode')) {
                            addDebugMessage(`📖 كتاب ${bookId} تم فتحه`);
                        } else if (target.classList.contains('cover-mode')) {
                            addDebugMessage(`📕 كتاب ${bookId} تم إغلاقه`);
                        }
                        
                        if (target.classList.contains('page-1-open')) {
                            addDebugMessage(`📄 كتاب ${bookId} - الصفحة 1`);
                        } else if (target.classList.contains('page-2-open')) {
                            addDebugMessage(`📄 كتاب ${bookId} - الصفحة 2`);
                        }
                    }
                }
            });
        });
        
        // Start observing
        setTimeout(() => {
            document.querySelectorAll('.book-card').forEach(card => {
                observer.observe(card, { attributes: true, attributeFilter: ['class'] });
            });
            addDebugMessage('👁️ بدء مراقبة تغييرات الكتب');
        }, 2000);
        
        // Initial debug message
        addDebugMessage('🚀 بدء اختبار فتح الكتب');
        
        // Check if functions are available
        setTimeout(() => {
            if (typeof openBookFromCover === 'function') {
                addDebugMessage('✅ وظيفة openBookFromCover متوفرة');
            } else {
                addDebugMessage('❌ وظيفة openBookFromCover غير متوفرة');
            }
            
            if (typeof backToCover === 'function') {
                addDebugMessage('✅ وظيفة backToCover متوفرة');
            } else {
                addDebugMessage('❌ وظيفة backToCover غير متوفرة');
            }
            
            if (typeof nextPage === 'function') {
                addDebugMessage('✅ وظيفة nextPage متوفرة');
            } else {
                addDebugMessage('❌ وظيفة nextPage غير متوفرة');
            }
            
            // Check if books have click areas
            const clickAreas = document.querySelectorAll('.cover-click-area');
            addDebugMessage(`🎯 تم العثور على ${clickAreas.length} منطقة نقر`);
            
            // Test click area visibility
            clickAreas.forEach((area, index) => {
                const styles = window.getComputedStyle(area);
                const isVisible = styles.display !== 'none' && styles.visibility !== 'hidden' && styles.opacity !== '0';
                addDebugMessage(`👁️ منطقة نقر ${index + 1}: ${isVisible ? 'مرئية' : 'مخفية'}`);
            });
            
        }, 3000);
    </script>
</body>
</html>
