<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النقر على الكتاب</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: #1a1a2e;
            color: white;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-header h1 {
            color: #ffda37;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .instructions {
            background: rgba(255, 218, 55, 0.1);
            border: 1px solid #ffda37;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .instructions h3 {
            color: #ffda37;
            margin-bottom: 15px;
        }
        .debug-info {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffda37;
            border-radius: 10px;
            padding: 15px;
            z-index: 9999;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-info h4 {
            color: #ffda37;
            margin: 0 0 10px 0;
        }
        .debug-log {
            color: #fff;
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .debug-success {
            background: rgba(81, 207, 102, 0.2);
            color: #51cf66;
        }
        .debug-error {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }
        .books-test-area {
            background: rgba(255, 255, 255, 0.02);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 218, 55, 0.2);
        }
        .test-status {
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: bold;
        }
        .status-waiting {
            background: rgba(255, 218, 55, 0.2);
            color: #ffda37;
            border: 1px solid #ffda37;
        }
        .status-success {
            background: rgba(81, 207, 102, 0.2);
            color: #51cf66;
            border: 1px solid #51cf66;
        }
        .status-error {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🖱️ اختبار النقر على الكتاب</h1>
            <p>اختبار مخصص للتأكد من أن النقر على الكتاب يفتحه مباشرة</p>
        </div>

        <div class="instructions">
            <h3>📋 تعليمات الاختبار:</h3>
            <p><strong>🖱️ انقر مباشرة على أي كتاب لفتحه</strong></p>
            <p>يجب أن يفتح الكتاب فوراً بدون الحاجة للنقر على زر أو منطقة معينة</p>
        </div>

        <div id="test-status" class="test-status status-waiting">
            ⏳ جاري تحميل الكتب... انتظر حتى تظهر الكتب ثم انقر على أي منها
        </div>

        <!-- Debug Panel -->
        <div class="debug-info">
            <h4>🔍 سجل الأحداث</h4>
            <div id="debug-output">⏳ جاري التحميل...</div>
        </div>

        <!-- Books Test Area -->
        <div class="books-test-area">
            <h2 style="color: #ffda37; text-align: center; margin-bottom: 30px;">
                📚 انقر على أي كتاب لفتحه
            </h2>
            
            <div class="books-grid-container">
                <div id="featured-books" class="books-grid">
                    <!-- Books will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="script-fixed.js"></script>
    
    <!-- Test Script -->
    <script>
        let debugOutput = document.getElementById('debug-output');
        let testStatus = document.getElementById('test-status');
        let debugMessages = [];
        let testStarted = false;
        
        function addDebugMessage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            let className = 'debug-log';
            
            if (type === 'success') className = 'debug-log debug-success';
            else if (type === 'error') className = 'debug-log debug-error';
            
            debugMessages.push(`<div class="${className}">[${timestamp}] ${message}</div>`);
            
            if (debugMessages.length > 20) {
                debugMessages.shift();
            }
            
            if (debugOutput) {
                debugOutput.innerHTML = debugMessages.join('');
                debugOutput.scrollTop = debugOutput.scrollHeight;
            }
        }
        
        function updateTestStatus(message, type = 'waiting') {
            testStatus.innerHTML = message;
            testStatus.className = `test-status status-${type}`;
        }
        
        // Override console methods for debugging
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            const message = args.join(' ');
            addDebugMessage(message, 'log');
            originalLog.apply(console, args);
            
            // Check for specific book opening messages
            if (message.includes('تم النقر على الكتاب')) {
                updateTestStatus('🖱️ تم النقر على الكتاب! جاري الفتح...', 'success');
            } else if (message.includes('تم فتح الكتاب بنجاح')) {
                updateTestStatus('✅ نجح الاختبار! الكتاب فتح بنجاح عند النقر عليه', 'success');
            }
        };
        
        console.error = function(...args) {
            addDebugMessage('❌ ' + args.join(' '), 'error');
            originalError.apply(console, args);
            updateTestStatus('❌ حدث خطأ - تحقق من سجل الأحداث', 'error');
        };
        
        // Monitor book state changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('book-card')) {
                        const bookId = target.getAttribute('data-book-id');
                        
                        if (target.classList.contains('internal-mode')) {
                            addDebugMessage(`📖 كتاب ${bookId} تم فتحه بنجاح!`, 'success');
                            updateTestStatus('🎉 ممتاز! الكتاب فتح عند النقر عليه مباشرة', 'success');
                        }
                        
                        if (target.classList.contains('page-1-open')) {
                            addDebugMessage(`📄 كتاب ${bookId} - الصفحة الأولى مفتوحة`, 'success');
                        }
                    }
                }
            });
        });
        
        // Start monitoring after books are loaded
        setTimeout(() => {
            const bookCards = document.querySelectorAll('.book-card');
            if (bookCards.length > 0) {
                bookCards.forEach(card => {
                    observer.observe(card, { attributes: true, attributeFilter: ['class'] });
                });
                addDebugMessage(`👁️ بدء مراقبة ${bookCards.length} كتاب`, 'success');
                updateTestStatus('✅ الكتب جاهزة! انقر على أي كتاب لاختبار الفتح', 'waiting');
            } else {
                addDebugMessage('❌ لم يتم العثور على كتب', 'error');
                updateTestStatus('❌ لم يتم تحميل الكتب', 'error');
            }
        }, 3000);
        
        // Initial debug message
        addDebugMessage('🚀 بدء اختبار النقر على الكتاب', 'success');
        
        // Check if functions are available
        setTimeout(() => {
            if (typeof openBookFromCover === 'function') {
                addDebugMessage('✅ وظيفة openBookFromCover متوفرة', 'success');
            } else {
                addDebugMessage('❌ وظيفة openBookFromCover غير متوفرة', 'error');
                updateTestStatus('❌ خطأ: وظيفة فتح الكتاب غير متوفرة', 'error');
            }
        }, 2000);
    </script>
</body>
</html>
