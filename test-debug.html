<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل الكتب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .debug-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #ffda37;
        }
        .books-grid {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 20px 0;
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid green;
        }
        .book-card {
            width: 200px;
            height: 300px;
            background: rgba(255, 218, 55, 0.2);
            border: 2px solid #ffda37;
            border-radius: 10px;
            padding: 10px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .error {
            color: red;
            background: rgba(255, 0, 0, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: green;
            background: rgba(0, 255, 0, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 اختبار تحميل الكتب - تشخيص المشكلة</h1>
    
    <div class="debug-section">
        <h2>1. اختبار تحميل JavaScript</h2>
        <div id="js-test">❌ JavaScript لم يتم تحميله</div>
    </div>

    <div class="debug-section">
        <h2>2. اختبار بيانات الكتب</h2>
        <div id="books-data-test">❌ بيانات الكتب غير متوفرة</div>
    </div>

    <div class="debug-section">
        <h2>3. اختبار الحاويات</h2>
        <div id="containers-test">❌ الحاويات غير موجودة</div>
    </div>

    <div class="debug-section">
        <h2>4. اختبار إنشاء الكتب</h2>
        <div id="books-creation-test">❌ لم يتم إنشاء الكتب</div>
    </div>

    <div class="debug-section">
        <h2>5. الكتب المميزة</h2>
        <div id="featured-books" class="books-grid">
            <!-- سيتم إدراج الكتب هنا -->
        </div>
    </div>

    <div class="debug-section">
        <h2>6. الكتب الشائعة</h2>
        <div id="popular-books" class="books-grid">
            <!-- سيتم إدراج الكتب هنا -->
        </div>
    </div>

    <script>
        console.log('🚀 بدء اختبار التشخيص...');
        
        // 1. اختبار تحميل JavaScript
        document.getElementById('js-test').innerHTML = '✅ JavaScript تم تحميله بنجاح';
        document.getElementById('js-test').className = 'success';

        // 2. بيانات الكتب للاختبار
        const testBooksData = [
            {
                id: 1,
                title: "كتاب اختبار 1",
                author: "مؤلف اختبار",
                description: "وصف الكتاب",
                cover: "https://picsum.photos/200/300?random=1",
                rating: 4.5,
                category: "test",
                featured: true,
                popular: false
            },
            {
                id: 2,
                title: "كتاب اختبار 2",
                author: "مؤلف اختبار 2",
                description: "وصف الكتاب 2",
                cover: "https://picsum.photos/200/300?random=2",
                rating: 4.8,
                category: "test",
                featured: false,
                popular: true
            },
            {
                id: 3,
                title: "كتاب اختبار 3",
                author: "مؤلف اختبار 3",
                description: "وصف الكتاب 3",
                cover: "https://picsum.photos/200/300?random=3",
                rating: 4.2,
                category: "test",
                featured: true,
                popular: true
            }
        ];

        console.log('📚 بيانات الكتب:', testBooksData);
        document.getElementById('books-data-test').innerHTML = `✅ تم تحميل ${testBooksData.length} كتب`;
        document.getElementById('books-data-test').className = 'success';

        // 3. اختبار الحاويات
        const featuredContainer = document.getElementById('featured-books');
        const popularContainer = document.getElementById('popular-books');
        
        if (featuredContainer && popularContainer) {
            document.getElementById('containers-test').innerHTML = '✅ جميع الحاويات موجودة';
            document.getElementById('containers-test').className = 'success';
        } else {
            document.getElementById('containers-test').innerHTML = '❌ بعض الحاويات مفقودة';
            document.getElementById('containers-test').className = 'error';
        }

        // 4. وظيفة إنشاء كتاب بسيطة
        function createSimpleBookCard(book) {
            console.log('🏗️ إنشاء كتاب:', book.title);
            
            const card = document.createElement('div');
            card.className = 'book-card';
            card.innerHTML = `
                <img src="${book.cover}" alt="${book.title}" 
                     style="width: 100%; height: 150px; object-fit: cover; border-radius: 5px;"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNzUiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiI+S2l0YWI8L3RleHQ+Cjwvc3ZnPg==';">
                <h3 style="margin: 10px 0 5px 0; font-size: 14px;">${book.title}</h3>
                <p style="margin: 0; font-size: 12px; opacity: 0.8;">${book.author}</p>
                <p style="margin: 5px 0 0 0; font-size: 12px;">⭐ ${book.rating}</p>
            `;
            return card;
        }

        // 5. تحميل الكتب
        try {
            const featuredBooks = testBooksData.filter(book => book.featured);
            const popularBooks = testBooksData.filter(book => book.popular);
            
            console.log('⭐ كتب مميزة:', featuredBooks.length);
            console.log('🔥 كتب شائعة:', popularBooks.length);
            
            // إضافة الكتب المميزة
            featuredBooks.forEach(book => {
                const bookCard = createSimpleBookCard(book);
                featuredContainer.appendChild(bookCard);
            });
            
            // إضافة الكتب الشائعة
            popularBooks.forEach(book => {
                const bookCard = createSimpleBookCard(book);
                popularContainer.appendChild(bookCard);
            });
            
            document.getElementById('books-creation-test').innerHTML = `✅ تم إنشاء ${featuredBooks.length + popularBooks.length} كتاب بنجاح`;
            document.getElementById('books-creation-test').className = 'success';
            
            console.log('🎉 تم تحميل جميع الكتب بنجاح!');
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الكتب:', error);
            document.getElementById('books-creation-test').innerHTML = `❌ خطأ: ${error.message}`;
            document.getElementById('books-creation-test').className = 'error';
        }
    </script>
</body>
</html>
