<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مبسط - المكتبة الطبية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 218, 55, 0.3);
        }

        .header h1 {
            color: #ffda37;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
        }

        .section {
            margin-bottom: 50px;
        }

        .section-title {
            color: #ffda37;
            font-size: 2rem;
            margin-bottom: 20px;
            text-align: center;
            padding: 15px;
            background: rgba(255, 218, 55, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 218, 55, 0.3);
        }

        .books-grid {
            display: flex !important;
            gap: 20px;
            padding: 30px;
            overflow-x: auto;
            scroll-behavior: smooth;
            scrollbar-width: none;
            -ms-overflow-style: none;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 15px;
            border: 1px solid rgba(255, 218, 55, 0.1);
            min-height: 450px;
            align-items: flex-start;
        }

        .books-grid::-webkit-scrollbar {
            display: none;
        }

        .books-grid:empty::before {
            content: "⏳ جاري تحميل الكتب...";
            color: #ffda37;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            text-align: center;
        }

        .debug-info {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 350px;
            max-height: 500px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffda37;
            border-radius: 10px;
            padding: 15px;
            z-index: 9999;
            overflow-y: auto;
            font-size: 12px;
        }

        .debug-info h3 {
            color: #ffda37;
            margin: 0 0 15px 0;
            font-size: 16px;
            text-align: center;
        }

        .debug-log {
            color: #fff;
            margin: 3px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .debug-error {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .debug-success {
            background: rgba(81, 207, 102, 0.2);
            color: #51cf66;
        }

        .debug-warning {
            background: rgba(255, 218, 55, 0.2);
            color: #ffda37;
        }

        @media (max-width: 768px) {
            .debug-info {
                width: 300px;
                font-size: 11px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 1.5rem;
            }
            
            .books-grid {
                gap: 15px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-info">
        <h3>🔍 لوحة التشخيص المبسطة</h3>
        <div id="debug-output">⏳ جاري التحميل...</div>
    </div>

    <!-- Header -->
    <div class="header">
        <h1>📚 المكتبة الطبية الإلكترونية</h1>
        <p>اختبار مبسط لتحميل الكتب</p>
    </div>

    <!-- Featured Books -->
    <div class="section">
        <h2 class="section-title">📖 الكتب المميزة</h2>
        <div id="featured-books" class="books-grid">
            <!-- Books will be loaded here -->
        </div>
    </div>

    <!-- Popular Books -->
    <div class="section">
        <h2 class="section-title">🔥 الكتب الشائعة</h2>
        <div id="popular-books" class="books-grid">
            <!-- Books will be loaded here -->
        </div>
    </div>

    <!-- Recent Books -->
    <div class="section">
        <h2 class="section-title">🆕 الكتب الحديثة</h2>
        <div id="recent-books" class="books-grid">
            <!-- Books will be loaded here -->
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="script-minimal.js"></script>
    
    <!-- Debug Script -->
    <script>
        let debugOutput = document.getElementById('debug-output');
        let debugMessages = [];
        
        function addDebugMessage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            let className = 'debug-log';
            
            if (type === 'error') className = 'debug-log debug-error';
            else if (type === 'success') className = 'debug-log debug-success';
            else if (type === 'warning') className = 'debug-log debug-warning';
            
            debugMessages.push(`<div class="${className}">[${timestamp}] ${message}</div>`);
            
            // Keep only last 25 messages
            if (debugMessages.length > 25) {
                debugMessages.shift();
            }
            
            if (debugOutput) {
                debugOutput.innerHTML = debugMessages.join('');
                debugOutput.scrollTop = debugOutput.scrollHeight;
            }
        }
        
        // Override console methods for debugging
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            addDebugMessage(args.join(' '), 'log');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addDebugMessage(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addDebugMessage(args.join(' '), 'warning');
            originalWarn.apply(console, args);
        };
        
        // Initial debug message
        addDebugMessage('🚀 بدء تحميل الصفحة المبسطة', 'success');
        
        // Check script loading after delay
        setTimeout(function() {
            if (typeof booksData !== 'undefined') {
                addDebugMessage(`✅ تم تحميل بيانات الكتب: ${booksData.length} كتاب`, 'success');
            } else {
                addDebugMessage('❌ فشل تحميل بيانات الكتب', 'error');
            }
            
            // Check containers
            const containers = ['featured-books', 'popular-books', 'recent-books'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                if (container) {
                    addDebugMessage(`✅ حاوية ${id}: ${container.children.length} عنصر`, 'success');
                } else {
                    addDebugMessage(`❌ حاوية ${id} غير موجودة`, 'error');
                }
            });
        }, 3000);
        
        // Monitor for errors
        window.addEventListener('error', function(e) {
            addDebugMessage(`❌ خطأ: ${e.message}`, 'error');
        });
        
        addDebugMessage('🔍 تم تشغيل نظام التشخيص', 'success');
    </script>
</body>
</html>
